#!/usr/bin/env python3
"""
Test MCP server directly via requests to verify functionality
"""

import requests
import json
import time

def test_mcp_tool_call(tool_name, arguments=None):
    """Test calling an MCP tool via POST request"""
    
    if arguments is None:
        arguments = {}
    
    # MCP request format
    payload = {
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        }
    }
    
    try:
        print(f"🔧 Testing tool: {tool_name}")
        print(f"📋 Arguments: {arguments}")
        
        # Try different possible endpoints
        endpoints = [
            "http://localhost:8051/",
            "http://localhost:8051/mcp",
            "http://localhost:8051/tools",
            "http://localhost:8051/rpc"
        ]
        
        for endpoint in endpoints:
            try:
                print(f"   Trying endpoint: {endpoint}")
                response = requests.post(
                    endpoint,
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    },
                    timeout=30
                )
                
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ Success: {json.dumps(result, indent=2)[:200]}...")
                    return result
                else:
                    print(f"   ❌ Error: {response.text[:100]}...")
                    
            except Exception as e:
                print(f"   ⚠️ Exception: {e}")
                continue
        
        print(f"   ❌ All endpoints failed for {tool_name}")
        return None
        
    except Exception as e:
        print(f"❌ Tool call failed: {e}")
        return None

def main():
    """Test the main MCP tools"""
    print("🧪 Direct MCP Tool Testing")
    print("=" * 50)
    
    # Test 1: get_available_sources (no parameters)
    test_mcp_tool_call("get_available_sources")
    
    print("\n" + "-" * 50)
    
    # Test 2: get_database_stats (no parameters)  
    test_mcp_tool_call("get_database_stats")
    
    print("\n" + "-" * 50)
    
    # Test 3: smart_crawl_url (with parameters)
    test_mcp_tool_call("smart_crawl_url", {
        "url": "https://httpbin.org/html",
        "max_depth": 1,
        "max_concurrent": 2,
        "chunk_size": 5000
    })
    
    print("\n" + "=" * 50)
    print("🏁 Testing complete")

if __name__ == "__main__":
    main()