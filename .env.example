# AY RAG MCP Server

# =================================================================
# MCP SERVER CONFIGURATION (Required for ay-rag-mcp container)
# =================================================================

# Core MCP Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse

# Docker Integration
CRAWL4AI_DOCKER_HOST=http://crawl4ai:11235

# =================================================================
# LLM PROVIDER CONFIGURATION
# =================================================================

# Provider Selection (openai or openrouter)
LLM_PROVIDER=openrouter

# API Credentials (REQUIRED - server will crash without valid values)
OPENAI_API_KEY=your-openai-api-key
OPENROUTER_API_KEY=your-openrouter-api-key

# OpenRouter App Identification (Optional but recommended)
OPENROUTER_APP_NAME=AY RAG MCP Server
OPENROUTER_APP_URL=https://github.com/user/mcp-crawl4ai-rag

# =================================================================
# MODEL SELECTION (3 specialized models - OpenRouter optimized)
# =================================================================

# Contextual Embedding: Document comprehension and summarization
CONTEXTUAL_EMBEDDING_MODEL=meta-llama/llama-3.1-8b-instruct
CONTEXTUAL_EMBEDDING_MAX_TOKENS=200
CONTEXTUAL_EMBEDDING_TEMPERATURE=0.3

# Code Analysis: Code extraction and understanding  
CODE_ANALYSIS_MODEL=moonshotai/kimi-k2
CODE_ANALYSIS_MAX_TOKENS=1000
CODE_ANALYSIS_TEMPERATURE=0.1

# Query Enhancement: Search query improvement
QUERY_ENHANCEMENT_MODEL=moonshotai/kimi-k2
QUERY_ENHANCEMENT_MAX_TOKENS=100
QUERY_ENHANCEMENT_TEMPERATURE=0.2

# Legacy support (used if above models not configured)
MODEL_CHOICE=gpt-4.1-nano


# Supabase Configuration (REQUIRED - server will crash without valid values)  
SUPABASE_URL=your-supabase-project-url
SUPABASE_SERVICE_KEY=your-supabase-service-key

# =================================================================
# RAG STRATEGY FLAGS (Enhanced Hybrid System)
# =================================================================

# Core RAG Strategies - inspired by ref-rag-project
USE_CONTEXTUAL_EMBEDDINGS=true      # LLM-enhanced chunk context
USE_HYBRID_SEARCH=true              # Vector + keyword search combination  
USE_AGENTIC_RAG=true                # Code example extraction and storage
USE_RERANKING=true                  # Cross-encoder result reranking
USE_KNOWLEDGE_GRAPH=false           # Neo4j hallucination detection

# Enhanced Features
USE_ENHANCED_CHUNKING=false         # Context7-inspired semantic chunking
USE_QUERY_EXPANSION=true            # Query enhancement and expansion
USE_RESULT_DEDUPLICATION=false      # Remove near-duplicate results
USE_SEMANTIC_CACHING=false          # Cache query embeddings

# =================================================================
# SCORING CONFIGURATION (Hybrid Scoring System)
# =================================================================

# Scoring mode: simple, normalized, adaptive
SCORING_MODE=normalized

# Score weights (should sum to ~1.0)
VECTOR_WEIGHT=0.5                   # Vector similarity weight
KEYWORD_WEIGHT=0.3                  # Keyword match weight  
RERANK_WEIGHT=0.2                   # Cross-encoder rerank weight

# Hybrid search optimization
DUAL_MATCH_BOOST=1.2                # Boost for items in both vector+keyword
RERANK_THRESHOLD=-2.0               # Filter very low rerank scores
MINIMUM_RELEVANCE_SCORE=0.1         # Filter low relevance results

# =================================================================
# PERFORMANCE TUNING
# =================================================================

# Concurrent processing
MAX_CONCURRENT_SEARCHES=3           # Max parallel search operations
ENABLE_PARALLEL_PROCESSING=true     # Enable async parallel processing

# Batch processing
EMBEDDING_BATCH_SIZE=50             # Embedding creation batch size
QUERY_TIMEOUT_SECONDS=30            # Max query processing time

# Quality filters
ENABLE_CONTENT_VALIDATION=true      # Validate content quality
MAXIMUM_DUPLICATE_SIMILARITY=0.95   # Deduplication threshold

# Caching settings
CACHE_TTL_SECONDS=3600             # Cache time-to-live (1 hour)
MAX_CACHE_SIZE=1000                # Max cached items
ENABLE_QUERY_CACHE=true            # Cache frequent queries
ENABLE_EMBEDDING_CACHE=true        # Cache embeddings

# Code Detection Configuration
MIN_CODE_LENGTH=50

# GitHub Integration (Optional)
GITHUB_TOKEN=your-github-token

# Resilience Settings
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_TIMEOUT=60
USE_QUERY_EXPANSION=true

# =================================================================
# OPTIONAL INTEGRATIONS
# =================================================================

# Redis Configuration (Optional)
REDIS_URL=redis://redis:6379
REDIS_TIMEOUT=5
ENABLE_REDIS_RATE_LIMITING=false

# Monitoring and Observability
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=your-sentry-dsn-if-using

# Development Settings
RELOAD_ON_CHANGE=false
ENABLE_DOCS=true

# =================================================================
# PERFORMANCE SETTINGS
# =================================================================

# Batch size for processing operations
DEFAULT_BATCH_SIZE=50