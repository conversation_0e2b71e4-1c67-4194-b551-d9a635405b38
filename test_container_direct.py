#!/usr/bin/env python3
"""
Direct container test via HTTP API
Tests the server functionality through direct HTTP calls
"""

import requests
import json
import time
from datetime import datetime

SERVER_URL = "http://localhost:8051"

def test_flask_regression():
    """Test that Flask quickstart regression is fixed"""
    print("🔍 REGRESSION TEST: Flask Quickstart Content Extraction")
    print("=" * 60)
    
    # Test URL that was previously failing
    url = "https://flask.palletsprojects.com/en/3.0.x/quickstart/"
    
    print(f"🕷️  Testing URL: {url}")
    print("📊 Expected: >1000 characters (was only 86 before fix)")
    
    # Create payload for smart_crawl_url
    payload = {
        "method": "tools/call",
        "params": {
            "name": "smart_crawl_url",
            "arguments": {
                "url": url,
                "max_depth": 1,
                "max_concurrent": 2,
                "chunk_size": 5000
            }
        }
    }
    
    try:
        print("⏳ Sending crawl request...")
        response = requests.post(
            f"{SERVER_URL}/",
            json=payload,
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            timeout=60
        )
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📄 Response keys: {list(result.keys())}")
                
                # Try to extract result data
                content = result.get("content", {})
                if isinstance(content, str):
                    try:
                        content = json.loads(content)
                    except:
                        pass
                
                print(f"📊 Content type: {type(content)}")
                print(f"📊 Content keys: {list(content.keys()) if isinstance(content, dict) else 'N/A'}")
                
                # Look for content length
                content_length = 0
                if isinstance(content, dict):
                    content_length = content.get("content_length", 0)
                    success = content.get("success", False)
                    
                    print(f"✅ Crawl successful: {success}")
                    print(f"📏 Content length: {content_length} characters")
                    
                    if content_length > 1000:
                        print(f"🎉 REGRESSION FIXED! Content extraction working properly")
                        print(f"   Before: 86 characters")
                        print(f"   After:  {content_length} characters")
                        
                        # Additional metrics
                        if "total_word_count" in content:
                            print(f"   Words: {content['total_word_count']}")
                        if "chunks_stored" in content:
                            print(f"   Chunks: {content['chunks_stored']}")
                        if "code_examples_stored" in content:
                            print(f"   Code examples: {content['code_examples_stored']}")
                        
                        return True
                    elif content_length > 100:
                        print(f"⚠️  PARTIALLY FIXED: Better than before but still low")
                        return False
                    else:
                        print(f"❌ REGRESSION PERSISTS: Still very low content")
                        return False
                else:
                    print(f"❌ Unexpected response format")
                    return False
                    
            except Exception as e:
                print(f"❌ Error parsing response: {e}")
                print(f"Raw response: {response.text[:500]}...")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_server_health():
    """Test that server is responding"""
    print("🏥 Server Health Check")
    print("-" * 30)
    
    try:
        response = requests.get(f"{SERVER_URL}/ready", timeout=5)
        if response.status_code == 200:
            print("✅ Server is healthy and responding")
            return True
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False

def main():
    """Run the test suite"""
    print("🧪 AY RAG MCP Server - Container Direct Test")
    print("=" * 70)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Target: {SERVER_URL}")
    
    # Health check first
    if not test_server_health():
        print("\n❌ Cannot proceed - server not accessible")
        return False
    
    print("\n" + "=" * 70)
    
    # Main regression test
    success = test_flask_regression()
    
    print("\n" + "=" * 70)
    print("📋 FINAL RESULTS")
    print("=" * 70)
    
    if success:
        print("🎉 SUCCESS: Flask regression has been FIXED!")
        print("✅ Content extraction is working properly")
        print("✅ All major issues have been resolved")
        print("✅ Server is ready for production use")
    else:
        print("❌ FAILURE: Issues still remain")
        print("🔧 Further investigation needed")
    
    return success

if __name__ == "__main__":
    result = main()
    exit(0 if result else 1)