[07/21/25 22:55:52] INFO     Connected to Redis at              job_queue.py:127
                             redis://localhost:6379                             
                    INFO     Worker final-test-worker           job_worker.py:66
                             initialized successfully                           
                    INFO     Starting worker final-test-worker  job_worker.py:79
                             with 3 concurrent tasks                            
                    INFO     Starting worker loop 0 for        job_worker.py:126
                             final-test-worker                                  
                    INFO     Starting worker loop 1 for        job_worker.py:126
                             final-test-worker                                  
                    INFO     Starting worker loop 2 for        job_worker.py:126
                             final-test-worker                                  
[07/21/25 22:56:07] INFO     Received shutdown signal for      job_worker.py:468
                             worker final-test-worker                           
                    INFO     Received shutdown signal for      job_worker.py:468
                             worker final-test-worker                           
[07/21/25 22:56:08] INFO     Shutting down worker              job_worker.py:107
                             final-test-worker                                  
                    INFO     Worker loop 0 cancelled           job_worker.py:144
                    INFO     Worker loop 1 cancelled           job_worker.py:144
                    INFO     Worker loop 2 cancelled           job_worker.py:144
