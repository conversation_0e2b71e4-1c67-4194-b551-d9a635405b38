#!/usr/bin/env python3
"""
Minimal test to verify button text visibility.
"""

from textual.app import App, ComposeResult
from textual.widgets import Button, Label
from textual.containers import Vertical


class MinimalApp(App):
    """Minimal app to test button text."""
    
    CSS = """
    Screen {
        background: black;
    }
    
    Button {
        background: #333333;
        color: white;
        border: solid #ff0080;
        text-style: bold;
        width: 100%;
        height: 3;
        margin: 1;
    }
    
    Label {
        color: white;
        text-style: bold;
        text-align: center;
        margin: 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create minimal interface."""
        yield Label("MINIMAL TEST")
        yield Label("Can you see this text?")
        
        with Vertical():
            yield Button("TEST BUTTON 1")
            yield Button("TEST BUTTON 2") 
            yield Button("TEST BUTTON 3")
        
        yield Label("End of test")


if __name__ == "__main__":
    app = MinimalApp()
    print("Starting minimal test...")
    print("You should see:")
    print("- White text labels")
    print("- Buttons with white text on grey background")
    print("- Pink borders around buttons")
    app.run()
