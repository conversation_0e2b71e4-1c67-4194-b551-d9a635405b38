# Redis Queue Performance Benchmark Report
==================================================
Generated: 2025-07-21 21:48:56
Redis URL: redis://localhost:6379

## Performance Summary
| Operation | Ops/Sec | Avg Time (ms) | Memory (MB) | Success Rate |
|-----------|---------|---------------|-------------|--------------|
| enqueue | 2992.15 | 0.33 | 0.25 | 100.0% |
| dequeue | 1281.41 | 0.78 | 0.00 | 100.0% |
| concurrent | 1261.69 | 0.79 | 9.25 | 100.0% |
| status_update | 842.33 | 1.19 | 0.00 | 100.0% |
| large_payload | 1651.39 | 0.61 | 0.00 | 100.0% |

## Detailed Results

### Enqueue Performance
- **Total Operations**: 1,000
- **Total Time**: 0.33 seconds
- **Throughput**: 2992.15 operations/second
- **Average Operation Time**: 0.33ms
- **Min Operation Time**: 0.26ms
- **Max Operation Time**: 3.47ms
- **Memory Usage**: 0.25MB
- **Success Rate**: 100.0%

### Dequeue Performance
- **Total Operations**: 1,000
- **Total Time**: 0.78 seconds
- **Throughput**: 1281.41 operations/second
- **Average Operation Time**: 0.78ms
- **Min Operation Time**: 0.64ms
- **Max Operation Time**: 3.65ms
- **Memory Usage**: 0.00MB
- **Success Rate**: 100.0%

### Concurrent Performance
- **Total Operations**: 500
- **Total Time**: 0.40 seconds
- **Throughput**: 1261.69 operations/second
- **Average Operation Time**: 0.79ms
- **Min Operation Time**: 0.00ms
- **Max Operation Time**: 0.00ms
- **Memory Usage**: 9.25MB
- **Success Rate**: 100.0%

### Status_Update Performance
- **Total Operations**: 500
- **Total Time**: 0.59 seconds
- **Throughput**: 842.33 operations/second
- **Average Operation Time**: 1.19ms
- **Min Operation Time**: 1.04ms
- **Max Operation Time**: 4.38ms
- **Memory Usage**: 0.00MB
- **Success Rate**: 100.0%

### Large_Payload Performance
- **Total Operations**: 100
- **Total Time**: 0.06 seconds
- **Throughput**: 1651.39 operations/second
- **Average Operation Time**: 0.61ms
- **Min Operation Time**: 0.43ms
- **Max Operation Time**: 1.15ms
- **Memory Usage**: 0.00MB
- **Success Rate**: 100.0%

## Performance Assessment
✅ **Excellent throughput** - System can handle high-volume operations
✅ **High reliability** - Excellent error handling
✅ **Efficient memory usage** - Low memory footprint