[07/21/25 22:56:17] INFO     Worker initializing with Redis     job_worker.py:45
                             URL: redis://localhost:6379                        
                    INFO     Connected to <PERSON><PERSON> at              job_queue.py:127
                             redis://localhost:6379                             
                    INFO     Worker debug-worker initialized    job_worker.py:67
                             successfully                                       
                    INFO     Starting worker debug-worker with  job_worker.py:80
                             3 concurrent tasks                                 
                    INFO     Starting worker loop 0 for        job_worker.py:127
                             debug-worker                                       
                    INFO     Starting worker loop 1 for        job_worker.py:127
                             debug-worker                                       
                    INFO     Starting worker loop 2 for        job_worker.py:127
                             debug-worker                                       
[07/21/25 22:56:46] INFO     Received shutdown signal for      job_worker.py:469
                             worker debug-worker                                
                    INFO     Received shutdown signal for      job_worker.py:469
                             worker debug-worker                                
[07/21/25 22:56:47] INFO     Shutting down worker debug-worker job_worker.py:108
                    INFO     Worker loop 0 cancelled           job_worker.py:145
                    INFO     Worker loop 1 cancelled           job_worker.py:145
                    INFO     Worker loop 2 cancelled           job_worker.py:145
