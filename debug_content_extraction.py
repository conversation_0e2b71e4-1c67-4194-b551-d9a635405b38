#!/usr/bin/env python3
"""
Debug Content Extraction Issues

This script helps debug issues where only copyright footers are being extracted
instead of main content. It tests the enhanced content filtering system with
representative content to identify overly aggressive filtering.
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from enhanced_content_filter import (
    EnhancedContentFilter,
    FilteringLevel,
    create_enhanced_crawler_config
)


def test_flask_like_content():
    """Test with content structure similar to Flask quickstart"""
    flask_like_content = """
# Flask Quickstart

## A Minimal Application

A minimal Flask application looks something like this:

```python
from flask import Flask

app = Flask(__name__)

@app.route("/")
def hello_world():
    return "<p>Hello, World!</p>"
```

## Debug Mode

You'll want to enable debug mode in development, as it provides helpful error messages:

```python
app.run(debug=True)
```

## HTML Escaping

When returning HTML (the default response type in Flask), any user-provided values rendered in the output must be escaped to protect from injection attacks.

## Routing

Modern web applications use meaningful URLs to help users. Users are more likely to like a page and come back if the page uses a meaningful URL they can remember and use to directly visit a page.

Use the route() decorator to bind a function to a URL.

```python
@app.route("/")
def index():
    return "Index Page"

@app.route("/hello")
def hello():
    return "Hello World"
```

## Variable Rules

You can add variable sections to a URL by marking sections with <variable_name>.

```python
@app.route("/user/<username>")
def show_user_profile(username):
    # show the user profile for that user
    return f"User {username}"
```

## Copyright

Copyright 2023 Flask Team. All rights reserved.
"""
    
    print("🧪 Testing Flask-like Documentation Content")
    print("=" * 60)
    
    # Test different filtering levels
    filter_system = EnhancedContentFilter()
    
    for level in [FilteringLevel.MINIMAL, FilteringLevel.STANDARD, FilteringLevel.AGGRESSIVE]:
        print(f"\n📊 Testing {level.value.upper()} filtering:")
        
        result = filter_system.filter_content(
            content=flask_like_content,
            url="https://flask.palletsprojects.com/quickstart/",
            filtering_level=level,
            preserve_code=True
        )
        
        print(f"   Original length: {result.original_length}")
        print(f"   Filtered length: {result.filtered_length}")
        print(f"   Reduction: {result.reduction_percentage:.1f}%")
        print(f"   Quality score: {result.quality_score:.2f}")
        print(f"   Filters applied: {result.filters_applied}")
        
        # Check if main content is preserved
        key_content_checks = [
            ("Flask Quickstart" in result.filtered_content, "Title preserved"),
            ("A minimal Flask application" in result.filtered_content, "Description preserved"),
            ("```python" in result.filtered_content, "Code blocks preserved"),
            ("@app.route" in result.filtered_content, "Code content preserved"),
            ("Debug Mode" in result.filtered_content, "Section headers preserved"),
            ("Variable Rules" in result.filtered_content, "Tutorial sections preserved"),
        ]
        
        for check, description in key_content_checks:
            status = "✅" if check else "❌"
            print(f"   {status} {description}")
        
        if result.reduction_percentage > 50:
            print(f"   ⚠️  High reduction rate - might be too aggressive!")
        
        if "Copyright" not in result.filtered_content:
            print(f"   ✅ Copyright footer removed as expected")
        else:
            print(f"   ⚠️  Copyright footer still present")


def test_current_css_selectors():
    """Test what CSS selectors we're currently excluding"""
    print("\n🔍 Analyzing Current CSS Selector Exclusions")
    print("=" * 60)
    
    config = create_enhanced_crawler_config(
        url="https://flask.palletsprojects.com/quickstart/",
        filtering_level=FilteringLevel.STANDARD
    )
    
    css_selectors = config.get('css_selector_to_exclude', [])
    excluded_tags = config.get('excluded_tags', [])
    word_threshold = config.get('word_count_threshold', 0)
    
    print(f"📋 Excluded tags: {excluded_tags}")
    print(f"📋 Word count threshold: {word_threshold}")
    print(f"📋 CSS selectors to exclude ({len(css_selectors)} total):")
    
    for i, selector in enumerate(css_selectors, 1):
        print(f"   {i:2d}. {selector}")
    
    # Check for potentially problematic selectors
    problematic_selectors = []
    for selector in css_selectors:
        if selector in ['main', 'article', '.content', '.main-content', '.documentation']:
            problematic_selectors.append(selector)
    
    if problematic_selectors:
        print(f"\n⚠️  POTENTIALLY PROBLEMATIC SELECTORS:")
        for selector in problematic_selectors:
            print(f"   🚨 {selector} - This might exclude main content!")
    else:
        print(f"\n✅ No obviously problematic selectors found")


def test_navigation_detection():
    """Test if our navigation detection is too aggressive"""
    print("\n🧭 Testing Navigation Detection Logic")
    print("=" * 60)
    
    # Test various content types
    test_cases = [
        ("Main documentation content", """
## Getting Started

This is the main documentation content that explains how to use the library.

It contains detailed explanations and examples.
"""),
        ("Navigation-like list", """
## Navigation
- Home
- About
- Contact
- Documentation
- API Reference
"""),
        ("Content list (should be preserved)", """
## Features

Our library provides:
- Fast performance with optimized algorithms
- Easy-to-use API with comprehensive documentation
- Extensive plugin system for customization
- Built-in security features and best practices
- Cross-platform compatibility across all major operating systems
"""),
        ("Mixed content", """
# Documentation

## Table of Contents
- Getting Started
- API Reference
- Examples

## Getting Started

This section explains how to get started with the library.

### Installation

Install using pip:

```bash
pip install our-library
```

### Usage

Basic usage example:

```python
import our_library
result = our_library.process_data(data)
```
""")
    ]
    
    filter_system = EnhancedContentFilter()
    
    for test_name, content in test_cases:
        print(f"\n📝 Testing: {test_name}")
        
        result = filter_system.filter_content(
            content=content,
            filtering_level=FilteringLevel.STANDARD,
            preserve_code=True
        )
        
        print(f"   Original: {len(content)} chars")
        print(f"   Filtered: {len(result.filtered_content)} chars")
        print(f"   Reduction: {result.reduction_percentage:.1f}%")
        
        if result.reduction_percentage > 80:
            print(f"   🚨 VERY HIGH reduction - likely removing important content!")
        elif result.reduction_percentage > 50:
            print(f"   ⚠️  High reduction - check if important content removed")
        else:
            print(f"   ✅ Reasonable reduction level")
        
        # Show filtered content for analysis
        if len(result.filtered_content) < 200:
            print(f"   Filtered content: {repr(result.filtered_content[:100])}...")


def simulate_flask_quickstart_crawl():
    """Simulate what happens when we crawl Flask quickstart with current settings"""
    print("\n🌐 Simulating Flask Quickstart Crawl")
    print("=" * 60)
    
    # This simulates HTML that might be extracted from Flask quickstart
    simulated_html_content = """
# Flask Quickstart

A minimal Flask application looks something like this:

```python
from flask import Flask
app = Flask(__name__)

@app.route("/")
def hello_world():
    return "<p>Hello, World!</p>"
```

Save this as hello.py or something similar. Make sure to not call your application flask.py because this would conflict with Flask itself.

To run the application, use the flask command or python -m flask. You need to tell the Flask where your application is with the --app option.

```bash
$ flask --app hello run
 * Serving Flask app 'hello'
 * Running on http://127.0.0.1:5000
```

## Debug Mode

You'll want to enable debug mode in development, as it provides helpful error messages and will restart the server automatically when code changes.

```python
app.run(debug=True)
```

Copyright 2023 Pallets Team. Licensed under BSD-3-Clause.
"""
    
    # Test with current enhanced filtering config
    config = create_enhanced_crawler_config(
        url="https://flask.palletsprojects.com/quickstart/",
        filtering_level=FilteringLevel.ADAPTIVE
    )
    
    print("🔧 Current crawler configuration:")
    print(f"   Excluded tags: {config.get('excluded_tags', [])}")
    print(f"   CSS selectors to exclude: {len(config.get('css_selector_to_exclude', []))} selectors")
    print(f"   Word count threshold: {config.get('word_count_threshold', 0)}")
    
    # Apply filtering
    filter_system = EnhancedContentFilter()
    result = filter_system.filter_content(
        content=simulated_html_content,
        url="https://flask.palletsprojects.com/quickstart/",
        filtering_level=FilteringLevel.ADAPTIVE,
        preserve_code=True
    )
    
    print(f"\n📊 Filtering Results:")
    print(f"   Original length: {result.original_length}")
    print(f"   Filtered length: {result.filtered_length}")
    print(f"   Reduction percentage: {result.reduction_percentage:.1f}%")
    print(f"   Quality score: {result.quality_score:.2f}")
    print(f"   Content type: {result.content_classification.content_type if result.content_classification else 'Unknown'}")
    
    # Check what's left
    if result.filtered_length < 100:
        print(f"\n🚨 CRITICAL: Very little content remains!")
        print(f"   Remaining content: {repr(result.filtered_content)}")
    elif "Flask" not in result.filtered_content:
        print(f"\n🚨 CRITICAL: Main topic 'Flask' not found in filtered content!")
    elif "```python" not in result.filtered_content:
        print(f"\n⚠️  WARNING: Code examples removed!")
    else:
        print(f"\n✅ Content filtering appears reasonable")
    
    # Check if only copyright remains
    lines = [line.strip() for line in result.filtered_content.split('\n') if line.strip()]
    if len(lines) <= 3 and any('copyright' in line.lower() for line in lines):
        print(f"\n🚨 CRITICAL ISSUE DETECTED: Only copyright footer remains!")
        print(f"   This matches the reported problem.")
        return False
    
    return True


def main():
    """Run all diagnostic tests"""
    print("🔍 Content Extraction Debug Suite")
    print("=" * 80)
    print("Investigating why only copyright footers are being extracted...")
    
    # Run all tests
    test_current_css_selectors()
    test_flask_like_content()
    test_navigation_detection()
    success = simulate_flask_quickstart_crawl()
    
    print("\n" + "=" * 80)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 80)
    
    if not success:
        print("🚨 ISSUE CONFIRMED: Content extraction is too aggressive")
        print("\n🔧 RECOMMENDED FIXES:")
        print("   1. Reduce CSS selector exclusions")
        print("   2. Lower word count thresholds")
        print("   3. Make navigation detection less aggressive")
        print("   4. Add main content preservation logic")
        print("   5. Test with real Flask documentation")
    else:
        print("✅ Content extraction appears to be working correctly")
        print("   The issue might be site-specific or related to HTML structure")


if __name__ == "__main__":
    main()