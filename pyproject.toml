[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ay-rag-mcp"
version = "1.0.0"
description = "AY RAG MCP Server - Intelligent web crawling and RAG queries"
readme = "README.md"
requires-python = ">=3.11"
license = "MIT"
authors = [
    {name = "AY Team", email = "<EMAIL>"}
]
keywords = ["rag", "mcp", "crawling", "ai"]

dependencies = [
    # Core MCP framework
    "fastmcp>=0.3.0",
    
    # Web crawling
    "crawl4ai==0.6.2",
    "aiohttp>=3.9.0",
    "httpx>=0.25.0",
    
    # Database
    "supabase>=2.0.0",
    "asyncpg>=0.29.0",
    
    # Redis for job queue
    "redis[hiredis]>=5.0.0",
    
    # AI/ML
    "openai>=1.0.0",
    "sentence-transformers>=2.2.0",
    
    # Utilities
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
    
    # Error handling
    "tenacity>=8.2.3",
    
    # Performance
    "uvloop>=0.19.0",
    
    # TUI dependencies
    "textual>=0.50.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.11.0",
    "isort>=5.12.0",
]

[tool.setuptools.packages.find]
where = ["src"]
include = ["*"]

[tool.setuptools.package-data]
"*" = ["*.md", "*.txt", "*.json"]