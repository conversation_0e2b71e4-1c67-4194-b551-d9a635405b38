"""Test MCP connection and response format"""
import asyncio
import aiohttp
import json

async def test_mcp_connection():
    """Test different ways to connect to MCP server"""
    
    # Test 1: Direct HTTP to SSE endpoint
    print("Test 1: Direct HTTP POST to SSE endpoint")
    async with aiohttp.ClientSession() as session:
        try:
            # Try the format that MCP might expect
            request_data = {
                "jsonrpc": "2.0",
                "id": "test-1",
                "method": "tools/call",
                "params": {
                    "name": "get_database_stats",
                    "arguments": {}
                }
            }
            
            async with session.post(
                "http://localhost:8051/sse",
                json=request_data,
                headers={"Content-Type": "application/json", "Accept": "text/event-stream"}
            ) as response:
                print(f"Status: {response.status}")
                print(f"Headers: {dict(response.headers)}")
                
                # Try to read as SSE
                text = await response.text()
                print(f"Response (first 500 chars): {text[:500]}")
                
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: Check if there's a hidden HTTP endpoint
    print("Test 2: Try various HTTP endpoints")
    endpoints = [
        ("/tools/call", "POST", {"name": "get_database_stats", "arguments": {}}),
        ("/api/tools/call", "POST", {"name": "get_database_stats", "arguments": {}}),
        ("/mcp/tools/call", "POST", {"name": "get_database_stats", "arguments": {}}),
        ("/rpc", "POST", {"method": "tools/call", "params": {"name": "get_database_stats", "arguments": {}}}),
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint, method, data in endpoints:
            try:
                url = f"http://localhost:8051{endpoint}"
                print(f"\nTrying {method} {url}")
                
                if method == "POST":
                    async with session.post(url, json=data) as response:
                        print(f"Status: {response.status}")
                        if response.status == 200:
                            result = await response.json()
                            print(f"Success! Response: {json.dumps(result, indent=2)}")
                            return
                        else:
                            text = await response.text()
                            print(f"Response: {text}")
                            
            except Exception as e:
                print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_mcp_connection())