#!/usr/bin/env python3
"""
Simple worker test to verify the Redis connection fix
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue

async def test_worker_redis():
    """Test that the worker fix connects to the right Redis"""
    print("🧪 Testing worker Redis connection fix...")
    
    # Test direct connection (should work)
    print("📡 Testing direct RedisJobQueue connection to port 6380...")
    queue = RedisJobQueue("redis://localhost:6380")
    await queue.connect()
    print("✅ Direct connection successful")
    
    # Check if there are jobs in queue
    jobs = await queue.dequeue_job()
    if jobs:
        print(f"📋 Found job in queue: {jobs.job_id}")
        print(f"🔗 Job URL: {jobs.url}")
        print(f"📊 Job type: {jobs.job_type}")
        
        # Put it back for now
        await queue.enqueue_job(jobs)
        print("📤 Put job back in queue for testing")
    else:
        print("📭 No jobs found in queue")
    
    await queue.disconnect()
    print("🏁 Worker Redis test completed")

if __name__ == "__main__":
    asyncio.run(test_worker_redis())