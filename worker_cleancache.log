[07/21/25 22:57:45] INFO     Starting worker with config:      job_worker.py:500
                             redis_url=redis://localhost:6379,                  
                             worker_id=clean-cache-test,                        
                             concurrency=3                                      
                    INFO     Worker initializing with Redis     job_worker.py:45
                             URL: redis://localhost:6379                        
                    INFO     Connected to Red<PERSON> at              job_queue.py:127
                             redis://localhost:6379                             
                    INFO     Worker clean-cache-test            job_worker.py:67
                             initialized successfully                           
                    INFO     Starting worker clean-cache-test   job_worker.py:80
                             with 3 concurrent tasks                            
                    INFO     Starting worker loop 0 for        job_worker.py:127
                             clean-cache-test                                   
                    INFO     Starting worker loop 1 for        job_worker.py:127
                             clean-cache-test                                   
                    INFO     Starting worker loop 2 for        job_worker.py:127
                             clean-cache-test                                   
[07/21/25 22:58:02] INFO     Received shutdown signal for      job_worker.py:469
                             worker clean-cache-test                            
                    INFO     Received shutdown signal for      job_worker.py:469
                             worker clean-cache-test                            
                    INFO     Shutting down worker              job_worker.py:108
                             clean-cache-test                                   
                    INFO     Worker loop 0 cancelled           job_worker.py:145
                    INFO     Worker loop 1 cancelled           job_worker.py:145
                    INFO     Worker loop 2 cancelled           job_worker.py:145
