services:
  # Redis for job queue
  redis:
    image: redis:7-alpine
    container_name: ay-rag-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Background job worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - PORT=8051
    container_name: ay-rag-worker
    environment:
      # Core Configuration
      - REDIS_URL=redis://redis:6379
      - WORKER_ID=worker-1
      - WORKER_CONCURRENCY=3
      
      # LLM Provider Configuration
      - LLM_PROVIDER=${LLM_PROVIDER:-openai}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      
      # Model Selection
      - CONTEXTUAL_EMBEDDING_MODEL=${CONTEXTUAL_EMBEDDING_MODEL:-gpt-4o-mini}
      - CODE_ANALYSIS_MODEL=${CODE_ANALYSIS_MODEL:-gpt-4o-mini}
      - QUERY_ENHANCEMENT_MODEL=${QUERY_ENHANCEMENT_MODEL:-gpt-4o-mini}
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4o-mini}
      
      # Supabase Configuration
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # RAG Strategy Flags
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-false}
      
      # Code Detection
      - MIN_CODE_LENGTH=${MIN_CODE_LENGTH:-50}
    command: python -m src.job_worker
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # AY RAG MCP Server (Main API)
  ay-rag-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - PORT=8051
    container_name: ay-rag-mcp-server
    ports:
      - "8051:8051"
    environment:
      # Core MCP Configuration (from .env)
      - HOST=${HOST:-0.0.0.0}
      - PORT=8051
      - TRANSPORT=${TRANSPORT:-sse}
            
      # LLM Provider Configuration (from .env)
      - LLM_PROVIDER=${LLM_PROVIDER:-openai}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      
      # Model Selection (from .env)
      - CONTEXTUAL_EMBEDDING_MODEL=${CONTEXTUAL_EMBEDDING_MODEL:-gpt-4.1-nano}
      - CODE_ANALYSIS_MODEL=${CODE_ANALYSIS_MODEL:-gpt-4.1-nano}
      - QUERY_ENHANCEMENT_MODEL=${QUERY_ENHANCEMENT_MODEL:-gpt-4.1-nano}
      
      # Legacy support (from .env)
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4.1-nano}
      
      # Supabase Configuration (from .env)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      
      # RAG Strategy Flags (from .env)
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-true}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-true}
      - USE_RERANKING=${USE_RERANKING:-true}
      
      # Code Detection Configuration (from .env)
      - MIN_CODE_LENGTH=${MIN_CODE_LENGTH:-50}
      
      # GitHub Integration (from .env)
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      
      # Resilience Settings (from .env)
      - CIRCUIT_BREAKER_THRESHOLD=${CIRCUIT_BREAKER_THRESHOLD:-3}
      - CIRCUIT_BREAKER_TIMEOUT=${CIRCUIT_BREAKER_TIMEOUT:-60}
      - USE_QUERY_EXPANSION=${USE_QUERY_EXPANSION:-true}
      
      # Performance Settings (from .env)
      - DEFAULT_BATCH_SIZE=${DEFAULT_BATCH_SIZE:-50}
      
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s  # Reduced time for simplified KISS architecture
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
    # REMOVED: Custom network - single container uses default bridge
    volumes:
      # Optional: Mount local directory for persistent logs
      - ./logs:/app/logs

volumes:
  redis_data:
