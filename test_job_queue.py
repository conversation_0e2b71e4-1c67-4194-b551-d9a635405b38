#!/usr/bin/env python3
"""
Simple test script to submit a job to the Redis queue and monitor its processing
"""
import asyncio
import json
import time
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import get_job_queue, JobRequest, JobStatus

async def test_job_submission():
    """Test submitting a crawl job and monitoring its status"""
    print("🧪 Starting job queue test...")
    
    # Configure for our test Redis instance
    import os
    os.environ["REDIS_URL"] = "redis://localhost:6380"
    
    try:
        # Get job queue
        print("📡 Connecting to Redis job queue...")
        queue = await get_job_queue()
        print("✅ Connected to Redis job queue successfully")
        
        # Create a test job
        job_request = JobRequest.create(
            job_type="smart_crawl",
            url="https://example.com",
            parameters={
                "max_depth": 1,
                "max_concurrent": 2,
                "chunk_size": 1000
            },
            priority=5
        )
        
        print(f"📝 Submitting test job: {job_request.job_id}")
        print(f"🌐 URL: {job_request.url}")
        
        # Submit job
        job_id = await queue.enqueue_job(job_request)
        print(f"✅ Job submitted successfully with ID: {job_id}")
        
        # Monitor job status
        print("👀 Monitoring job status...")
        for i in range(30):  # Monitor for up to 30 seconds
            result = await queue.get_job_status(job_id)
            if result:
                print(f"📊 Status check {i+1}: {result.status.value}")
                if result.error_message:
                    print(f"❌ Error: {result.error_message}")
                
                if result.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                    print(f"🏁 Final result: {result}")
                    if result.result_data:
                        print(f"📄 Result data: {result.result_data}")
                    break
            else:
                print(f"📊 Status check {i+1}: No result yet")
            
            await asyncio.sleep(1)
        else:
            print("⏰ Monitoring timeout - job may still be processing")
            
        print("🏁 Test completed")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_job_submission())