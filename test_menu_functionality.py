#!/usr/bin/env python3
"""
Test script to verify TUI menu functionality.
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all TUI components can be imported."""
    try:
        print("Testing TUI imports...")
        
        # Test main app import
        from tui.app import AYKnowledgeBaseTUI
        print("✓ Main app imported successfully")
        
        # Test screen imports
        from tui.screens.home import HomeScreen
        print("✓ Home screen imported successfully")
        
        from tui.screens.crawl_url import CrawlURLScreen
        print("✓ Crawl URL screen imported successfully")
        
        from tui.screens.crawl_search import CrawlSearchScreen
        print("✓ Crawl search screen imported successfully")
        
        from tui.screens.chat import ChatScreen
        print("✓ Chat screen imported successfully")
        
        from tui.screens.sources import SourcesScreen
        print("✓ Sources screen imported successfully")
        
        from tui.screens.settings import SettingsScreen
        print("✓ Settings screen imported successfully")
        
        from tui.screens.help import HelpScreen
        print("✓ Help screen imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_app_creation():
    """Test that the TUI app can be created."""
    try:
        print("\nTesting app creation...")
        from tui.app import AYKnowledgeBaseTUI
        
        app = AYKnowledgeBaseTUI()
        print("✓ App created successfully")
        
        # Test basic properties
        assert app.title == "AY Knowledge Base v0.1"
        print("✓ App title correct")
        
        assert hasattr(app, 'show_crawl_url')
        print("✓ Navigation methods present")
        
        return True
        
    except Exception as e:
        print(f"✗ App creation failed: {e}")
        return False

def test_screen_creation():
    """Test that screens can be created."""
    try:
        print("\nTesting screen creation...")
        
        from tui.screens.home import HomeScreen
        home = HomeScreen()
        print("✓ Home screen created successfully")
        
        from tui.screens.crawl_url import CrawlURLScreen
        crawl_url = CrawlURLScreen()
        print("✓ Crawl URL screen created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Screen creation failed: {e}")
        return False

if __name__ == "__main__":
    print("TUI Menu Functionality Test")
    print("=" * 40)
    
    success = True
    success &= test_imports()
    success &= test_app_creation()
    success &= test_screen_creation()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed! Menu functionality should work correctly.")
    else:
        print("✗ Some tests failed. Check the errors above.")
    
    sys.exit(0 if success else 1)
