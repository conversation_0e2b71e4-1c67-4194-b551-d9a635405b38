"""
Model Configuration Manager
Handles environment-based LLM model configuration for different use cases
"""

import os
import logging
from dataclasses import dataclass
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

@dataclass
class ModelSettings:
    """Settings for a specific model and use case"""
    name: str
    max_tokens: int
    temperature: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API calls"""
        return {
            "model": self.name,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }

@dataclass  
class LLMConfiguration:
    """Complete LLM configuration for all use cases"""
    contextual_model: ModelSettings
    code_analysis_model: ModelSettings
    query_enhancement_model: ModelSettings
    provider: str
    
    def get_model_for_task(self, task: str) -> ModelSettings:
        """Get model settings for a specific task"""
        task_mapping = {
            "contextual": self.contextual_model,
            "code_analysis": self.code_analysis_model,
            "query_enhancement": self.query_enhancement_model
        }
        
        if task not in task_mapping:
            raise ValueError(f"Unknown task: {task}. Available: {list(task_mapping.keys())}")
        
        return task_mapping[task]

def get_model_config() -> LLMConfiguration:
    """Load model configuration from environment variables"""
    
    # Provider selection
    provider = os.getenv("LLM_PROVIDER", "openai").lower()
    logger.info(f"Using LLM provider: {provider}")
    
    # Legacy fallback model
    legacy_model = os.getenv("MODEL_CHOICE", "gpt-4o-mini")
    
    # Contextual embedding model configuration
    contextual_model = ModelSettings(
        name=os.getenv("CONTEXTUAL_EMBEDDING_MODEL", legacy_model),
        max_tokens=int(os.getenv("CONTEXTUAL_EMBEDDING_MAX_TOKENS", "200")),
        temperature=float(os.getenv("CONTEXTUAL_EMBEDDING_TEMPERATURE", "0.3"))
    )
    
    # Code analysis model configuration
    code_analysis_model = ModelSettings(
        name=os.getenv("CODE_ANALYSIS_MODEL", legacy_model),
        max_tokens=int(os.getenv("CODE_ANALYSIS_MAX_TOKENS", "1000")),
        temperature=float(os.getenv("CODE_ANALYSIS_TEMPERATURE", "0.1"))
    )
    
    # Query enhancement model configuration
    query_enhancement_model = ModelSettings(
        name=os.getenv("QUERY_ENHANCEMENT_MODEL", legacy_model),
        max_tokens=int(os.getenv("QUERY_ENHANCEMENT_MAX_TOKENS", "100")),
        temperature=float(os.getenv("QUERY_ENHANCEMENT_TEMPERATURE", "0.2"))
    )
    
    config = LLMConfiguration(
        contextual_model=contextual_model,
        code_analysis_model=code_analysis_model,
        query_enhancement_model=query_enhancement_model,
        provider=provider
    )
    
    # Log configuration (without sensitive data)
    logger.info(f"Model configuration loaded:")
    logger.info(f"  Contextual: {contextual_model.name} (tokens: {contextual_model.max_tokens})")
    logger.info(f"  Code Analysis: {code_analysis_model.name} (tokens: {code_analysis_model.max_tokens})")
    logger.info(f"  Query Enhancement: {query_enhancement_model.name} (tokens: {query_enhancement_model.max_tokens})")
    
    return config

def validate_configuration() -> bool:
    """Validate LLM configuration at startup"""
    try:
        config = get_model_config()
        
        # Check provider-specific requirements
        if config.provider == "openai":
            if not os.getenv("OPENAI_API_KEY"):
                raise ValueError("OPENAI_API_KEY is required when using OpenAI provider")
        
        elif config.provider == "openrouter":
            if not os.getenv("OPENROUTER_API_KEY"):
                raise ValueError("OPENROUTER_API_KEY is required when using OpenRouter provider")
        
        else:
            raise ValueError(f"Unsupported provider: {config.provider}. Use 'openai' or 'openrouter'")
        
        # Validate model settings
        for task, model in [
            ("contextual", config.contextual_model),
            ("code_analysis", config.code_analysis_model),
            ("query_enhancement", config.query_enhancement_model)
        ]:
            if not model.name:
                raise ValueError(f"Model name for {task} task cannot be empty")
            
            if model.max_tokens <= 0:
                raise ValueError(f"Max tokens for {task} must be positive")
            
            if not 0 <= model.temperature <= 2:
                raise ValueError(f"Temperature for {task} must be between 0 and 2")
        
        logger.info("LLM configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"LLM configuration validation failed: {e}")
        raise

def get_recommended_models() -> Dict[str, Dict[str, str]]:
    """Get recommended model configurations for different scenarios"""
    return {
        "cost_optimized": {
            "LLM_PROVIDER": "openrouter",
            "CONTEXTUAL_EMBEDDING_MODEL": "meta-llama/llama-3.1-8b-instruct",
            "CODE_ANALYSIS_MODEL": "openai/gpt-4o-mini",
            "QUERY_ENHANCEMENT_MODEL": "meta-llama/llama-3.1-8b-instruct"
        },
        "quality_optimized": {
            "LLM_PROVIDER": "openrouter", 
            "CONTEXTUAL_EMBEDDING_MODEL": "anthropic/claude-3-sonnet",
            "CODE_ANALYSIS_MODEL": "openai/gpt-4o",
            "QUERY_ENHANCEMENT_MODEL": "anthropic/claude-3-haiku"
        },
        "balanced": {
            "LLM_PROVIDER": "openrouter",
            "CONTEXTUAL_EMBEDDING_MODEL": "anthropic/claude-3-haiku",
            "CODE_ANALYSIS_MODEL": "openai/gpt-4o-mini",
            "QUERY_ENHANCEMENT_MODEL": "meta-llama/llama-3.1-8b-instruct"
        },
        "openai_only": {
            "LLM_PROVIDER": "openai",
            "CONTEXTUAL_EMBEDDING_MODEL": "gpt-4o-mini",
            "CODE_ANALYSIS_MODEL": "gpt-4o-mini",
            "QUERY_ENHANCEMENT_MODEL": "gpt-3.5-turbo"
        }
    }

# Global configuration instance (lazy initialization)
_model_config = None

def get_default_model_config() -> LLMConfiguration:
    """Get or create the default model configuration instance"""
    global _model_config
    
    if _model_config is None:
        _model_config = get_model_config()
    
    return _model_config

def reload_model_config():
    """Force reload of model configuration from environment"""
    global _model_config
    _model_config = None
    return get_default_model_config()