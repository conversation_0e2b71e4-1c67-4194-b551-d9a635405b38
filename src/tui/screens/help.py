"""Help screen with scrollable documentation."""

from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, Footer, Input
)
from textual.containers import ScrollableContainer
from textual.containers import Container, Vertical, Horizontal
from textual import on
from rich.markdown import Markdown
from rich.text import Text


class HelpSection(Static):
    """A collapsible help section."""
    
    def __init__(self, title: str, content: str, expanded: bool = False):
        self.title = title
        self.content = content
        self.expanded = expanded
        super().__init__()
        self.update_display()
    
    def update_display(self):
        """Update the display based on expanded state."""
        if self.expanded:
            icon = "▼"
            display = f"[bold cyan]{icon} {self.title}[/bold cyan]\n\n{self.content}\n"
        else:
            icon = "▶"
            display = f"[bold cyan]{icon} {self.title}[/bold cyan]"
        
        self.update(display)
    
    def toggle(self):
        """Toggle expanded state."""
        self.expanded = not self.expanded
        self.update_display()


class HelpScreen(Screen):
    """Comprehensive help documentation screen."""
    
    CSS = """
    HelpScreen {
        align: center middle;
    }
    
    #main-container {
        width: 85%;
        height: 90%;
        background: $surface;
        border: solid $primary;
        padding: 2;
    }
    
    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 1;
    }
    
    #search-container {
        height: auto;
        margin-bottom: 1;
    }
    
    #help-search {
        width: 50%;
    }
    
    #content-container {
        height: 75%;
        border: solid $primary;
        padding: 1;
    }
    
    #help-content {
        height: 100%;
        overflow-y: auto;
        scrollbar-size: 1 1;
    }
    
    #button-container {
        height: auto;
        align: center middle;
        margin-top: 1;
    }
    
    HelpSection {
        margin-bottom: 1;
        padding: 1;
        border: solid $secondary;
        cursor: pointer;
    }
    
    HelpSection:hover {
        background: $background;
    }
    """
    
    HELP_SECTIONS = [
        {
            "title": "🚀 Getting Started",
            "content": """Welcome to AY Knowledge Base TUI!

**Quick Start:**
1. Crawl content using URLs or search queries
2. Chat with your knowledge base to find information
3. Manage sources and settings as needed

**Key Features:**
- Intelligent web crawling with automatic content extraction
- RAG-powered semantic search across all crawled content
- Source management with secure deletion
- Configurable OpenRouter model selection"""
        },
        {
            "title": "🧭 Navigation",
            "content": """**Global Shortcuts:**
- `Ctrl+H`: Return to home screen from anywhere
- `Ctrl+Q`: Quit the application
- `Tab`: Navigate between form fields
- `Enter`: Select menu items or submit forms
- `Escape`: Cancel operations or go back
- `Arrow Keys`: Navigate menus and tables

**Screen Navigation:**
- Use arrow keys to highlight menu options
- Press Enter to select an option
- Use Tab to move between interactive elements"""
        },
        {
            "title": "🌐 Crawling URLs",
            "content": """**Supported URL Types:**
- Regular webpages (https://example.com)
- XML sitemaps (sitemap.xml)
- Text files with URL lists (.txt)

**Crawling Process:**
1. Enter the URL in the input field
2. Click "Start Crawling" or press Enter
3. Monitor progress in the log display
4. Cancel anytime with the Cancel button

**Tips:**
- Sitemaps crawl all listed pages automatically
- Text files should have one URL per line
- The crawler respects robots.txt rules
- Large sites may take several minutes"""
        },
        {
            "title": "🔍 Search-Based Crawling",
            "content": """**How It Works:**
1. Enter a search query (e.g., "Python async tutorials")
2. Brave Search finds relevant websites
3. LLM analyzes and selects the best sources
4. Selected URLs are crawled automatically

**LLM Selection Logic:**
- Evaluates relevance to your query
- Considers domain authority
- Checks content freshness
- Assesses technical depth

**Tips:**
- Be specific in your search queries
- Review LLM reasoning in the log
- Manually select/deselect URLs as needed
- Use "Select All" for comprehensive crawling"""
        },
        {
            "title": "💬 Chat Interface",
            "content": """**Chat Commands:**
- `/new`: Start a fresh chat session
- `/sources`: View available sources inline

**Features:**
- Rich markdown rendering for responses
- Source citations with each answer
- Automatic chat history within session
- Scrollable message history

**Query Tips:**
- Be specific in your questions
- Reference crawled domains for targeted search
- Use natural language queries
- Ask follow-up questions for clarity"""
        },
        {
            "title": "📚 Managing Sources",
            "content": """**Source Management:**
- View all crawled domains and statistics
- Search/filter sources by domain name
- Select multiple sources for bulk operations
- Secure deletion with password protection

**Deletion Process:**
1. Select sources to delete (click rows)
2. Click "Delete Selected"
3. Enter admin password
4. Confirm deletion

**Important:**
- Deletion is permanent and cannot be undone
- Default admin password: admin123
- Change password in Settings for security"""
        },
        {
            "title": "⚙️ Configuration",
            "content": """**Settings Available:**
- OpenRouter model selection
- Admin password for deletions
- Environment variable status

**Model Options:**
- GPT-4 variants (Optimized, Mini)
- Claude 3 family (Haiku, Sonnet, Opus)
- Llama 3.1 models (8B, 70B)
- Other providers (Gemini, Mistral)

**Environment Variables:**
Required variables are configured in .env file:
- SUPABASE_URL: Database connection
- SUPABASE_SERVICE_KEY: Database auth
- OPENAI_API_KEY: Embeddings
- OPENROUTER_API_KEY: LLM access"""
        },
        {
            "title": "🛠️ Troubleshooting",
            "content": """**Common Issues:**

**Connection Errors:**
- Check internet connectivity
- Verify API keys in .env file
- Ensure MCP server is running

**Crawling Failures:**
- Some sites block automated access
- Check if robots.txt allows crawling
- Try using sitemap.xml if available

**Chat Not Responding:**
- Verify OpenRouter API key
- Check selected model availability
- Ensure sources are crawled first

**Deletion Failed:**
- Verify admin password
- Check database connection
- Ensure source exists"""
        },
        {
            "title": "⌨️ Keyboard Shortcuts",
            "content": """**Global:**
- `Ctrl+H`: Home
- `Ctrl+Q`: Quit

**Navigation:**
- `Tab`: Next field
- `Shift+Tab`: Previous field
- `Enter`: Submit/Select
- `Escape`: Cancel/Back

**Tables:**
- `↑/↓`: Navigate rows
- `Space`: Select row
- `Enter`: Toggle selection

**Text Input:**
- `Ctrl+A`: Select all
- `Ctrl+C`: Copy
- `Ctrl+V`: Paste
- `Ctrl+Z`: Undo"""
        }
    ]
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("❓ Help & Documentation", id="title")
            
            with Horizontal(id="search-container"):
                yield Label("Search help topics:")
                yield Input(
                    placeholder="Enter keywords...",
                    id="help-search"
                )
            
            with Container(id="content-container"):
                with ScrollableContainer(id="help-content"):
                    # Add all help sections
                    for i, section in enumerate(self.HELP_SECTIONS):
                        # First section expanded by default
                        yield HelpSection(
                            section["title"],
                            section["content"],
                            expanded=(i == 0)
                        )
            
            with Horizontal(id="button-container"):
                yield Button("Back to Home", variant="primary", id="back-btn")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.original_sections = list(self.HELP_SECTIONS)
    
    def on_click(self, event) -> None:
        """Handle clicking on a help section to expand/collapse."""
        if isinstance(event.sender, HelpSection):
            event.sender.toggle()
    
    @on(Input.Changed, "#help-search")
    def handle_search(self, event) -> None:
        """Filter help sections based on search."""
        search_term = event.value.lower()
        content_container = self.query_one("#help-content", ScrollableContainer)
        
        # Remove all current sections
        for widget in list(content_container.children):
            widget.remove()
        
        # Add filtered sections
        for i, section in enumerate(self.original_sections):
            if (search_term in section["title"].lower() or 
                search_term in section["content"].lower()):
                # Expand all matching sections when searching
                content_container.mount(
                    HelpSection(
                        section["title"],
                        section["content"],
                        expanded=bool(search_term)
                    )
                )
    
    @on(Button.Pressed, "#back-btn")
    def handle_back(self) -> None:
        """Handle back button press."""
        self.app.action_go_home()