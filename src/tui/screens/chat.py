"""Chat screen with message history and markdown rendering."""

import asyncio
from datetime import datetime
from typing import Dict, Any, List
from textual.app import ComposeResult
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, TextArea,
    Footer, LoadingIndicator
)
from textual.containers import ScrollableContainer
from textual.containers import Container, Vertical, Horizontal
from textual import on, work
from textual.reactive import reactive
from rich.markdown import Markdown
from rich.text import Text
from rich.panel import Panel


class MessageWidget(Static):
    """Widget for displaying a single chat message."""
    
    def __init__(self, message: Dict[str, Any]):
        self.message = message
        self.timestamp = message.get("timestamp", datetime.now())
        self.role = message.get("role", "user")
        self.content = message.get("content", "")
        
        # Create styled content
        if self.role == "user":
            prefix = "[bold cyan]You:[/bold cyan]"
            style = "cyan"
        elif self.role == "assistant":
            prefix = "[bold green]AI:[/bold green]"
            style = "green"
        else:
            prefix = "[bold yellow]System:[/bold yellow]"
            style = "yellow"
        
        # Format timestamp
        time_str = self.timestamp.strftime("%H:%M:%S")
        header = f"{prefix} [dim]{time_str}[/dim]"
        
        # Create panel with markdown content
        if self.role == "assistant":
            # Render markdown for assistant messages
            md = Markdown(self.content)
            content_panel = Panel(
                md,
                title=header,
                title_align="left",
                border_style=style,
                padding=(0, 1)
            )
        else:
            # Plain text for user messages
            content_panel = Panel(
                self.content,
                title=header,
                title_align="left",
                border_style=style,
                padding=(0, 1)
            )
        
        super().__init__(content_panel)


class ChatScreen(Screen):
    """Interactive chat screen with RAG queries."""
    
    CSS = """
    ChatScreen {
        align: center middle;
        padding: 2;
    }

    #main-container {
        width: 92%;
        height: 90%;
        background: $surface;
        border: solid $border;
        
        padding: 2;
    }

    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 2;
    }

    #chat-container {
        height: 65%;
        border: solid $border;
        
        padding: 1;
        margin-bottom: 2;
        background: $background;
    }

    #message-list {
        height: 100%;
        overflow-y: auto;
        scrollbar-size: 1 1;
        padding: 1;
    }

    #input-container {
        height: 22%;
        border: solid $border;
        
        padding: 2;
        background: $surface;
        margin-bottom: 1;
    }

    #input-container Label {
        color: $text-secondary;
        margin-bottom: 1;
        text-style: bold;
    }

    #chat-input {
        width: 100%;
        height: 100%;
        background: $background;
        border: solid $border;
        
        padding: 1;
    }

    #chat-input:focus {
        border: solid $accent;
        background: $surface;
    }

    #button-container {
        height: auto;
        align: center middle;
        margin-top: 1;
    }

    #loading-container {
        align: center middle;
        height: 3;
        margin-bottom: 1;
    }

    .chat-button {
        margin: 0 1;
        height: 3;
        min-width: 12;
        
    }

    #send-btn {
        background: $success;
        color: $text-primary;
    }

    #send-btn:hover {
        background: $success;
    }

    #clear-btn, #sources-btn {
        background: $secondary;
        color: $text-primary;
    }

    #clear-btn:hover, #sources-btn:hover {
        background: $secondary;
    }

    MessageWidget {
        margin-bottom: 2;
        padding: 2;
        background: $surface;
        
        border: solid $border;
    }

    LoadingIndicator {
        color: $accent;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("💬 Chat with Knowledge Base", id="title")
            
            with Container(id="chat-container"):
                with ScrollableContainer(id="message-list"):
                    # Messages will be added dynamically
                    pass
            
            with Container(id="loading-container"):
                yield LoadingIndicator(id="loading")
            
            with Vertical(id="input-container"):
                yield Label("Your message (commands: /new, /sources):")
                yield TextArea(
                    id="chat-input",
                    language="markdown"
                )
            
            with Horizontal(id="button-container"):
                yield Button("Send", variant="primary", 
                           classes="chat-button", id="send-btn")
                yield Button("Clear Chat", variant="secondary", 
                           classes="chat-button", id="clear-btn")
                yield Button("View Sources", variant="secondary", 
                           classes="chat-button", id="sources-btn")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.query_worker = None
        
        # Hide loading indicator initially
        loading = self.query_one("#loading", LoadingIndicator)
        loading.display = False
        
        # Add welcome message
        self.add_message({
            "role": "system",
            "content": "Welcome to AY Knowledge Base Chat! I can help you search through your crawled content. Type your question or use commands:\n• /new - Start a new chat session\n• /sources - View available sources",
            "timestamp": datetime.now()
        })
        
        # Load chat history from app
        if self.app.chat_history:
            for message in self.app.chat_history:
                self.add_message(message)
        
        self.set_focus(self.query_one("#chat-input"))
    
    def add_message(self, message: Dict[str, Any]):
        """Add a message to the chat display."""
        message_list = self.query_one("#message-list", ScrollableContainer)
        message_widget = MessageWidget(message)
        message_list.mount(message_widget)
        
        # Auto-scroll to bottom
        message_list.scroll_end(animate=False)
        
        # Add to app's chat history if not system message
        if message["role"] != "system":
            self.app.chat_history.append(message)
    
    def handle_command(self, text: str) -> bool:
        """Handle special commands. Returns True if handled."""
        command = text.strip().lower()
        
        if command == "/new":
            self.clear_chat()
            self.add_message({
                "role": "system",
                "content": "Chat session cleared. Starting fresh!",
                "timestamp": datetime.now()
            })
            return True
        
        elif command == "/sources":
            self.show_sources_info()
            return True
        
        return False
    
    def show_sources_info(self):
        """Display available sources in chat."""
        try:
            from ..mcp_client import get_available_sources
            result = get_available_sources()
            
            if result.get("success") and result.get("sources"):
                sources = result["sources"]
                content = f"**Available Sources ({len(sources)}):**\n\n"
                
                for source in sources[:10]:  # Show first 10
                    content += f"• **{source.get('domain', 'Unknown')}** - "
                    content += f"{source.get('document_count', 0)} documents, "
                    content += f"Last crawled: {source.get('last_crawled', 'Unknown')}\n"
                
                if len(sources) > 10:
                    content += f"\n... and {len(sources) - 10} more sources"
            else:
                content = "No sources available. Try crawling some URLs first!"
        
        except Exception as e:
            content = f"Error fetching sources: {str(e)}"
        
        self.add_message({
            "role": "assistant",
            "content": content,
            "timestamp": datetime.now()
        })
    
    @on(Button.Pressed, "#send-btn")
    def handle_send(self) -> None:
        """Handle send button press."""
        input_area = self.query_one("#chat-input", TextArea)
        text = input_area.value.strip()
        
        if not text:
            return
        
        # Clear input
        input_area.value = ""
        
        # Handle commands
        if text.startswith("/"):
            if self.handle_command(text):
                return
        
        # Add user message
        self.add_message({
            "role": "user",
            "content": text,
            "timestamp": datetime.now()
        })
        
        # Process query
        self.query_worker = self.process_query(text)
    
    @on(TextArea.Changed, "#chat-input")
    def handle_input_changed(self, event) -> None:
        """Handle text area changes (we'll handle Enter separately)."""
        # Check if Enter was pressed without Shift
        pass
    
    @work(exclusive=True)
    async def process_query(self, query: str) -> None:
        """Process user query asynchronously."""
        loading = self.query_one("#loading", LoadingIndicator)
        loading.visible = True
        
        try:
            # Perform RAG query
            from ..mcp_client import perform_rag_query
            result = await asyncio.to_thread(
                perform_rag_query,
                query=query,
                top_k=5
            )
            
            if result.get("success"):
                # Format response with sources
                content = result.get("response", "No response generated.")
                
                # Add source citations if available
                if result.get("sources"):
                    content += "\n\n**Sources:**\n"
                    for i, source in enumerate(result["sources"][:3], 1):
                        content += f"{i}. [{source.get('title', 'Untitled')}]({source.get('url', '#')})\n"
                
                self.add_message({
                    "role": "assistant",
                    "content": content,
                    "timestamp": datetime.now()
                })
            else:
                error = result.get("error", "Unknown error occurred")
                self.add_message({
                    "role": "assistant",
                    "content": f"❌ Error: {error}",
                    "timestamp": datetime.now()
                })
        
        except Exception as e:
            self.add_message({
                "role": "assistant",
                "content": f"❌ Error processing query: {str(e)}",
                "timestamp": datetime.now()
            })
        
        finally:
            loading.visible = False
            self.set_focus(self.query_one("#chat-input"))
    
    @on(Button.Pressed, "#clear-btn")
    def handle_clear(self) -> None:
        """Handle clear chat button."""
        self.clear_chat()
    
    def clear_chat(self):
        """Clear all chat messages."""
        # Clear display
        message_list = self.query_one("#message-list", ScrollableContainer)
        for widget in list(message_list.children):
            widget.remove()
        
        # Clear app history
        self.app.chat_history.clear()
        
        # Add fresh welcome message
        self.add_message({
            "role": "system",
            "content": "Chat cleared! Ready for new questions.",
            "timestamp": datetime.now()
        })
    
    @on(Button.Pressed, "#sources-btn")
    def handle_sources_button(self) -> None:
        """Handle view sources button."""
        self.app.show_sources()