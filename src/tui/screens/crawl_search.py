"""Search-based crawling screen with Brave search and LLM selection."""

import asyncio
from datetime import datetime
from typing import List, Dict, Any
from textual.app import ComposeResult
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, Input, 
    DataTable, RichLog, Footer, ProgressBar
)
from textual.containers import Container, Vertical, Horizontal
from textual import on, work
from rich.text import Text


class CrawlSearchScreen(Screen):
    """Screen for search-based crawling with LLM selection."""
    
    CSS = """
    CrawlSearchScreen {
        align: center middle;
    }
    
    #main-container {
        width: 90%;
        height: 90%;
        background: $surface;
        border: solid $primary;
        padding: 2;
    }
    
    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 1;
    }
    
    #search-container {
        height: auto;
        margin-bottom: 2;
    }
    
    #search-input {
        width: 100%;
        margin-bottom: 1;
    }
    
    #results-container {
        height: 40%;
        margin-bottom: 2;
    }
    
    #results-table {
        width: 100%;
        height: 100%;
        border: solid $primary;
    }
    
    #llm-container {
        height: 30%;
        margin-bottom: 2;
        border: solid $secondary;
        padding: 1;
    }
    
    #action-container {
        height: auto;
        align: center middle;
        margin-bottom: 2;
    }
    
    #progress-container {
        height: auto;
    }
    
    .action-button {
        margin: 0 1;
    }
    
    RichLog {
        background: $background;
        scrollbar-size: 1 1;
    }
    
    ProgressBar {
        width: 100%;
        margin-top: 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("🔍 Crawl by Search", id="title")
            
            with Vertical(id="search-container"):
                yield Label("Enter search query:")
                yield Input(
                    placeholder="e.g., 'Python async programming tutorials'",
                    id="search-input"
                )
                yield Button("Search", variant="primary", id="search-btn")
            
            with Container(id="results-container"):
                yield Label("Search Results:")
                yield DataTable(id="results-table")
            
            with Container(id="llm-container"):
                yield Label("LLM Selection Logic:", classes="section-title")
                yield RichLog(id="llm-log", highlight=True, markup=True)
            
            with Horizontal(id="action-container"):
                yield Button("Select All", variant="secondary", 
                           classes="action-button", id="select-all-btn", disabled=True)
                yield Button("Crawl Selected", variant="primary", 
                           classes="action-button", id="crawl-btn", disabled=True)
                yield Button("Cancel", variant="error", 
                           classes="action-button", id="cancel-btn", disabled=True)
            
            with Vertical(id="progress-container"):
                yield Label("", id="progress-label")
                yield ProgressBar(id="progress-bar", show_percentage=True, visible=False)
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.search_results: List[Dict[str, Any]] = []
        self.selected_urls: set = set()
        self.crawl_worker = None
        self.status_worker = None
        self.active_jobs: Dict[str, str] = {}  # job_id -> url mapping

        # Setup data table
        table = self.query_one("#results-table", DataTable)
        table.add_columns("✓", "Title", "URL", "Description")
        table.cursor_type = "row"

        self.set_focus(self.query_one("#search-input"))
    
    def log_llm(self, message: str, style: str = ""):
        """Add message to LLM reasoning log."""
        log = self.query_one("#llm-log", RichLog)
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if style:
            log.write(f"[dim]{timestamp}[/dim] [{style}]{message}[/{style}]")
        else:
            log.write(f"[dim]{timestamp}[/dim] {message}")
    
    @on(Button.Pressed, "#search-btn")
    def handle_search(self) -> None:
        """Handle search button press."""
        query_input = self.query_one("#search-input", Input)
        query = query_input.value.strip()
        
        if not query:
            self.log_llm("Please enter a search query", "red")
            return
        
        self.perform_search(query)
    
    @work(exclusive=True)
    async def perform_search(self, query: str) -> None:
        """Perform Brave search asynchronously."""
        try:
            self.log_llm(f"Searching for: '{query}'", "cyan")
            
            # Clear previous results
            table = self.query_one("#results-table", DataTable)
            table.clear()
            self.search_results.clear()
            self.selected_urls.clear()
            
            # Import search function from app
            from ..app import search_with_brave
            
            # Perform search
            result = await asyncio.to_thread(
                search_with_brave,
                query=query,
                count=10
            )
            
            if result.get("success") and result.get("results"):
                self.search_results = result["results"]
                self.display_results()
                
                # Enable action buttons
                self.query_one("#select-all-btn", Button).disabled = False
                self.query_one("#crawl-btn", Button).disabled = False
                
                # Simulate LLM selection logic
                await self.simulate_llm_selection(query)
            else:
                self.log_llm(f"No results found or error: {result.get('error', 'Unknown')}", "red")
        
        except Exception as e:
            self.log_llm(f"Search error: {str(e)}", "red")
    
    def display_results(self):
        """Display search results in table."""
        table = self.query_one("#results-table", DataTable)
        
        for i, result in enumerate(self.search_results):
            table.add_row(
                "☐",
                result.get("title", "No title")[:50] + "...",
                result.get("url", "")[:50] + "...",
                result.get("description", "")[:80] + "..."
            )
    
    async def simulate_llm_selection(self, query: str):
        """Simulate LLM reasoning for URL selection."""
        await asyncio.sleep(0.5)  # Simulate thinking
        
        self.log_llm("🤖 LLM Analysis Starting...", "yellow")
        await asyncio.sleep(0.3)
        
        self.log_llm(f"Query intent: Searching for information about '{query}'", "cyan")
        await asyncio.sleep(0.5)
        
        self.log_llm("Evaluating search results based on:", "")
        self.log_llm("  • Relevance to query", "dim")
        self.log_llm("  • Domain authority", "dim")
        self.log_llm("  • Content freshness", "dim")
        self.log_llm("  • Technical depth", "dim")
        
        await asyncio.sleep(0.5)
        
        # Auto-select top results
        table = self.query_one("#results-table", DataTable)
        selected_count = min(5, len(self.search_results))
        
        for i in range(selected_count):
            self.selected_urls.add(self.search_results[i]["url"])
            table.update_cell_at((i, 0), "☑")
            
            self.log_llm(
                f"✓ Selected: {self.search_results[i]['title'][:50]}... "
                f"(High relevance score)",
                "green"
            )
            await asyncio.sleep(0.2)
        
        self.log_llm(f"\n🎯 Selected {selected_count} most relevant URLs for crawling", "green")
    
    @on(DataTable.RowSelected)
    def handle_row_selected(self, event: DataTable.RowSelected) -> None:
        """Handle row selection in results table."""
        if event.row_index < len(self.search_results):
            url = self.search_results[event.row_index]["url"]
            table = self.query_one("#results-table", DataTable)
            
            if url in self.selected_urls:
                self.selected_urls.remove(url)
                table.update_cell_at((event.row_index, 0), "☐")
            else:
                self.selected_urls.add(url)
                table.update_cell_at((event.row_index, 0), "☑")
    
    @on(Button.Pressed, "#select-all-btn")
    def handle_select_all(self) -> None:
        """Handle select all button."""
        table = self.query_one("#results-table", DataTable)
        
        if len(self.selected_urls) == len(self.search_results):
            # Deselect all
            self.selected_urls.clear()
            for i in range(len(self.search_results)):
                table.update_cell_at((i, 0), "☐")
        else:
            # Select all
            for i, result in enumerate(self.search_results):
                self.selected_urls.add(result["url"])
                table.update_cell_at((i, 0), "☑")
    
    @on(Button.Pressed, "#crawl-btn")
    def handle_crawl_selected(self) -> None:
        """Handle crawl selected button."""
        if not self.selected_urls:
            self.log_llm("No URLs selected for crawling", "red")
            return
        
        # Disable buttons during crawling
        self.query_one("#search-btn", Button).disabled = True
        self.query_one("#select-all-btn", Button).disabled = True
        self.query_one("#crawl-btn", Button).disabled = True
        self.query_one("#cancel-btn", Button).disabled = False
        
        # Show progress bar
        progress_bar = self.query_one("#progress-bar", ProgressBar)
        progress_label = self.query_one("#progress-label", Label)
        progress_bar.visible = True
        
        self.crawl_worker = self.crawl_selected_urls(list(self.selected_urls))
    
    @work(exclusive=True)
    async def crawl_selected_urls(self, urls: List[str]) -> None:
        """Submit batch crawl jobs to Redis queue and monitor progress."""
        progress_bar = self.query_one("#progress-bar", ProgressBar)
        progress_label = self.query_one("#progress-label", Label)

        try:
            from tui.app import smart_crawl_url

            total_urls = len(urls)
            self.active_jobs.clear()

            # Phase 1: Submit all jobs to queue
            self.log_llm(f"Submitting {total_urls} crawl jobs to queue...", "cyan")
            progress_label.update(f"Submitting jobs: 0/{total_urls}")

            for i, url in enumerate(urls):
                if self.crawl_worker.is_cancelled:
                    break

                progress = (i / total_urls) * 0.3  # 30% for job submission
                progress_bar.update(progress=progress)
                progress_label.update(f"Submitting job {i+1}/{total_urls}: {url[:40]}...")

                try:
                    result = await asyncio.to_thread(smart_crawl_url, url=url)

                    if result.get("success") and result.get("job_id"):
                        job_id = result["job_id"]
                        self.active_jobs[job_id] = url
                        self.log_llm(f"✓ Job submitted for: {url[:50]}... (ID: {job_id[:8]})", "green")
                    else:
                        error = result.get("error", "Unknown error")
                        self.log_llm(f"✗ Failed to submit job for: {url[:50]}... - {error}", "red")

                except Exception as e:
                    self.log_llm(f"✗ Error submitting job for {url[:50]}...: {str(e)}", "red")

            if not self.active_jobs:
                progress_label.update("No jobs were submitted successfully")
                return

            # Phase 2: Monitor all jobs
            self.log_llm(f"Monitoring {len(self.active_jobs)} active jobs...", "cyan")
            self.status_worker = self.monitor_batch_jobs()

        except asyncio.CancelledError:
            progress_label.update("Job submission cancelled")
            raise

        except Exception as e:
            self.log_llm(f"Error in batch crawl: {str(e)}", "red")
            progress_label.update(f"Error: {str(e)}")

        finally:
            # Re-enable buttons if no monitoring is active
            if not self.status_worker or self.status_worker.is_finished:
                self.reset_ui()

    @work(exclusive=True)
    async def monitor_batch_jobs(self) -> None:
        """Monitor multiple jobs until all complete."""
        progress_bar = self.query_one("#progress-bar", ProgressBar)
        progress_label = self.query_one("#progress-label", Label)

        try:
            from tui.app import get_crawl_job_status, cancel_crawl_job

            completed_jobs = set()
            failed_jobs = set()
            total_jobs = len(self.active_jobs)

            self.log_llm(f"Monitoring {total_jobs} jobs...", "cyan")

            while len(completed_jobs) + len(failed_jobs) < total_jobs:
                if self.status_worker.is_cancelled:
                    break

                # Check status of all active jobs
                for job_id, url in self.active_jobs.items():
                    if job_id in completed_jobs or job_id in failed_jobs:
                        continue

                    try:
                        status_result = await asyncio.to_thread(get_crawl_job_status, job_id)

                        if not status_result.get("success"):
                            continue

                        job_status = status_result.get("status", "unknown")

                        if job_status == "completed":
                            completed_jobs.add(job_id)
                            job_result = status_result.get("result", {})
                            pages = job_result.get("total_pages_crawled", 0)
                            chunks = job_result.get("total_chunks_stored", 0)
                            self.log_llm(f"✓ Completed: {url[:40]}... ({pages} pages, {chunks} chunks)", "green")

                        elif job_status == "failed":
                            failed_jobs.add(job_id)
                            error = status_result.get("error", "Unknown error")
                            self.log_llm(f"✗ Failed: {url[:40]}... - {error}", "red")

                    except Exception as e:
                        self.log_llm(f"Error checking status for {url[:40]}...: {str(e)}", "yellow")

                # Update progress
                finished_count = len(completed_jobs) + len(failed_jobs)
                progress = 30 + (finished_count / total_jobs) * 70  # 30% base + 70% for completion
                progress_bar.update(progress=progress)
                progress_label.update(f"Progress: {finished_count}/{total_jobs} jobs finished")

                # Wait before next check
                await asyncio.sleep(3)

            # Final summary
            progress_bar.update(progress=100)
            self.log_llm(f"Batch crawl completed!", "cyan")
            self.log_llm(f"✓ Successful: {len(completed_jobs)}", "green")
            self.log_llm(f"✗ Failed: {len(failed_jobs)}", "red")
            progress_label.update(f"Completed: {len(completed_jobs)} success, {len(failed_jobs)} failed")

            # Refresh sources cache
            self.app.refresh_sources_cache()

        except asyncio.CancelledError:
            # Cancel all remaining jobs
            self.log_llm("Cancelling all active jobs...", "yellow")
            for job_id in self.active_jobs.keys():
                try:
                    cancel_result = await asyncio.to_thread(cancel_crawl_job, job_id)
                    if cancel_result.get("success"):
                        url = self.active_jobs[job_id]
                        self.log_llm(f"Cancelled job for: {url[:40]}...", "yellow")
                except Exception as e:
                    self.log_llm(f"Error cancelling job {job_id[:8]}: {str(e)}", "red")

            progress_label.update("All jobs cancelled")
            raise

        except Exception as e:
            self.log_llm(f"Error monitoring jobs: {str(e)}", "red")
            progress_label.update(f"Monitoring error: {str(e)}")

        finally:
            self.reset_ui()

    def reset_ui(self):
        """Reset UI to initial state."""
        self.query_one("#search-btn", Button).disabled = False
        self.query_one("#select-all-btn", Button).disabled = False
        self.query_one("#crawl-btn", Button).disabled = False
        self.query_one("#cancel-btn", Button).disabled = True
        self.active_jobs.clear()
        self.crawl_worker = None
        self.status_worker = None
    
    @on(Button.Pressed, "#cancel-btn")
    def handle_cancel(self) -> None:
        """Handle cancel button."""
        if self.status_worker:
            self.status_worker.cancel()
            self.log_llm("Cancelling batch jobs...", "yellow")
        elif self.crawl_worker:
            self.crawl_worker.cancel()
            self.log_llm("Cancelling job submission...", "yellow")