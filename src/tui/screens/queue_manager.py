"""Queue Management screen for monitoring Redis queue jobs."""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from textual.app import ComposeResult
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, DataTable, 
    RichLog, Footer, ProgressBar
)
from textual.containers import Container, Vertical, Horizontal
from textual import on, work
from textual.worker import Worker
from rich.text import Text


class QueueManagerScreen(Screen):
    """Screen for managing Redis queue jobs."""
    
    CSS = """
    QueueManagerScreen {
        align: center middle;
        padding: 2;
    }
    
    #main-container {
        width: 95%;
        height: 90%;
        background: $surface;
        border: solid $border;
        
        padding: 3;
    }
    
    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 2;
    }
    
    #stats-container {
        height: auto;
        margin-bottom: 2;
        padding: 2;
        background: $surface;
        
        border: solid $border;
    }
    
    #stats-grid {
        height: auto;
        grid-size: 3;
        grid-gutter: 2;
    }
    
    .stat-card {
        height: 4;
        background: $background;
        border: solid $border;
        
        padding: 1;
        text-align: center;
    }
    
    .stat-value {
        color: $accent;
        text-style: bold;
    }
    
    .stat-label {
        color: $text-secondary;
    }
    
    #jobs-container {
        height: 60%;
        margin-bottom: 2;
    }
    
    #jobs-table {
        width: 100%;
        height: 100%;
        border: solid $border;
        
    }
    
    #controls-container {
        height: auto;
        margin-bottom: 2;
    }
    
    #log-container {
        height: 20%;
        border: solid $border;
        
        padding: 1;
        background: $background;
    }
    
    .control-button {
        margin: 0 1;
        height: 3;
        min-width: 12;
    }
    
    #refresh-btn {
        background: $primary;
    }
    
    #cancel-selected-btn {
        background: $error;
    }
    
    #clear-completed-btn {
        background: $warning;
    }
    
    RichLog {
        background: $background;
        scrollbar-size: 1 1;
        
        padding: 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("🔄 Queue Management", id="title")
            
            # Statistics section
            with Container(id="stats-container"):
                yield Label("Queue Statistics:", classes="section-title")
                with Horizontal(id="stats-grid"):
                    with Container(classes="stat-card"):
                        yield Label("0", classes="stat-value", id="queued-count")
                        yield Label("Queued", classes="stat-label")
                    
                    with Container(classes="stat-card"):
                        yield Label("0", classes="stat-value", id="processing-count")
                        yield Label("Processing", classes="stat-label")
                    
                    with Container(classes="stat-card"):
                        yield Label("0", classes="stat-value", id="total-count")
                        yield Label("Total Active", classes="stat-label")
            
            # Jobs table section
            with Container(id="jobs-container"):
                yield Label("Active Jobs:")
                yield DataTable(id="jobs-table")
            
            # Control buttons
            with Horizontal(id="controls-container"):
                yield Button("🔄 Refresh", id="refresh-btn", classes="control-button")
                yield Button("❌ Cancel Selected", id="cancel-selected-btn", 
                           classes="control-button", disabled=True)
                yield Button("🧹 Clear Completed", id="clear-completed-btn", 
                           classes="control-button")
                yield Button("📊 Queue Stats", id="stats-btn", classes="control-button")
            
            # Activity log
            with Container(id="log-container"):
                yield Label("Activity Log:")
                yield RichLog(id="activity-log", highlight=True, markup=True)
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.refresh_worker: Optional[Worker] = None
        self.selected_jobs: set = set()
        self.jobs_data: List[Dict[str, Any]] = []
        
        # Setup jobs table
        table = self.query_one("#jobs-table", DataTable)
        table.add_columns("✓", "Job ID", "Status", "URL", "Started", "Duration")
        table.cursor_type = "row"
        
        # Start auto-refresh
        self.log_activity("Queue Manager initialized", "cyan")
        self.refresh_data()
    
    def log_activity(self, message: str, style: str = ""):
        """Add message to activity log."""
        log = self.query_one("#activity-log", RichLog)
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if style:
            log.write(f"[dim]{timestamp}[/dim] [{style}]{message}[/{style}]")
        else:
            log.write(f"[dim]{timestamp}[/dim] {message}")
    
    @on(Button.Pressed, "#refresh-btn")
    def handle_refresh(self) -> None:
        """Handle refresh button press."""
        self.refresh_data()
    
    @work(exclusive=True)
    async def refresh_data(self) -> None:
        """Refresh queue data from Redis."""
        try:
            self.log_activity("Refreshing queue data...", "cyan")
            
            # Import queue functions
            from tui.app import get_queue_stats
            
            # Get queue statistics
            stats_result = await asyncio.to_thread(get_queue_stats)
            
            if stats_result.get("success"):
                stats = stats_result
                
                # Update statistics display
                self.query_one("#queued-count", Label).update(str(stats.get("queued_jobs", 0)))
                self.query_one("#processing-count", Label).update(str(stats.get("processing_jobs", 0)))
                self.query_one("#total-count", Label).update(str(stats.get("total_active_jobs", 0)))
                
                self.log_activity(f"Queue stats updated: {stats.get('total_active_jobs', 0)} active jobs", "green")
            else:
                error = stats_result.get("error", "Unknown error")
                self.log_activity(f"Failed to get queue stats: {error}", "red")
        
        except Exception as e:
            self.log_activity(f"Error refreshing data: {str(e)}", "red")
    
    @on(DataTable.RowSelected, "#jobs-table")
    def handle_job_selection(self, event: DataTable.RowSelected) -> None:
        """Handle job row selection."""
        if event.row_index < len(self.jobs_data):
            job_id = self.jobs_data[event.row_index]["job_id"]
            table = self.query_one("#jobs-table", DataTable)
            
            if job_id in self.selected_jobs:
                self.selected_jobs.remove(job_id)
                table.update_cell_at((event.row_index, 0), "☐")
            else:
                self.selected_jobs.add(job_id)
                table.update_cell_at((event.row_index, 0), "☑")
            
            # Enable/disable cancel button based on selection
            cancel_btn = self.query_one("#cancel-selected-btn", Button)
            cancel_btn.disabled = len(self.selected_jobs) == 0
    
    @on(Button.Pressed, "#cancel-selected-btn")
    def handle_cancel_selected(self) -> None:
        """Handle cancel selected jobs button."""
        if not self.selected_jobs:
            self.log_activity("No jobs selected for cancellation", "yellow")
            return
        
        self.cancel_selected_jobs()
    
    @work(exclusive=True)
    async def cancel_selected_jobs(self) -> None:
        """Cancel selected jobs."""
        try:
            from tui.app import cancel_crawl_job
            
            cancelled_count = 0
            failed_count = 0
            
            for job_id in list(self.selected_jobs):
                try:
                    result = await asyncio.to_thread(cancel_crawl_job, job_id)
                    
                    if result.get("success"):
                        self.log_activity(f"Cancelled job: {job_id[:8]}...", "yellow")
                        cancelled_count += 1
                    else:
                        error = result.get("error", "Unknown error")
                        self.log_activity(f"Failed to cancel {job_id[:8]}...: {error}", "red")
                        failed_count += 1
                
                except Exception as e:
                    self.log_activity(f"Error cancelling {job_id[:8]}...: {str(e)}", "red")
                    failed_count += 1
            
            self.log_activity(f"Cancellation complete: {cancelled_count} cancelled, {failed_count} failed", "cyan")
            
            # Clear selection and refresh
            self.selected_jobs.clear()
            self.query_one("#cancel-selected-btn", Button).disabled = True
            self.refresh_data()
        
        except Exception as e:
            self.log_activity(f"Error in batch cancellation: {str(e)}", "red")
    
    @on(Button.Pressed, "#stats-btn")
    def handle_stats(self) -> None:
        """Handle queue stats button."""
        self.show_detailed_stats()
    
    @work(exclusive=True)
    async def show_detailed_stats(self) -> None:
        """Show detailed queue statistics."""
        try:
            from tui.app import get_queue_stats
            
            result = await asyncio.to_thread(get_queue_stats)
            
            if result.get("success"):
                self.log_activity("=== Detailed Queue Statistics ===", "cyan")
                self.log_activity(f"Queued Jobs: {result.get('queued_jobs', 0)}", "white")
                self.log_activity(f"Processing Jobs: {result.get('processing_jobs', 0)}", "white")
                self.log_activity(f"Total Active: {result.get('total_active_jobs', 0)}", "white")
                self.log_activity("================================", "cyan")
            else:
                error = result.get("error", "Unknown error")
                self.log_activity(f"Failed to get detailed stats: {error}", "red")
        
        except Exception as e:
            self.log_activity(f"Error getting detailed stats: {str(e)}", "red")
