"""Sources management screen with table display and deletion."""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from textual.app import ComposeResult
from textual.screen import Screen, ModalScreen
from textual.widgets import (
    Static, Button, Label, Input, 
    DataTable, Footer, LoadingIndicator
)
from textual.containers import Container, Vertical, Horizontal, Grid
from textual import on, work
from textual.validation import Function


class PasswordModal(ModalScreen):
    """Modal dialog for password confirmation."""
    
    CSS = """
    PasswordModal {
        align: center middle;
    }
    
    #dialog {
        width: 50;
        height: 15;
        background: $surface;
        border: thick $error;
        padding: 2;
    }
    
    #password-input {
        width: 100%;
        margin: 1 0;
    }
    
    .modal-button {
        margin: 0 1;
    }
    """
    
    def __init__(self, domains: List[str]):
        super().__init__()
        self.domains = domains
        self.result = False
    
    def compose(self) -> ComposeResult:
        """Create modal widgets."""
        with Container(id="dialog"):
            yield Label("⚠️  Delete Confirmation", classes="title")
            yield Label(f"You are about to delete {len(self.domains)} source(s).")
            yield Label("Enter admin password to confirm:")
            yield Input(
                password=True,
                placeholder="Admin password",
                id="password-input"
            )
            with Horizontal(classes="button-container"):
                yield Button("Delete", variant="error", 
                           classes="modal-button", id="confirm-btn")
                yield Button("Cancel", variant="secondary", 
                           classes="modal-button", id="cancel-btn")
    
    def on_mount(self) -> None:
        """Focus password input on mount."""
        self.set_focus(self.query_one("#password-input"))
    
    @on(Button.Pressed, "#confirm-btn")
    def handle_confirm(self) -> None:
        """Handle confirm button."""
        password_input = self.query_one("#password-input", Input)
        entered_password = password_input.value
        
        # Check password
        if entered_password == self.app.settings.get("admin_password", "admin123"):
            self.result = True
            self.dismiss(True)
        else:
            password_input.value = ""
            password_input.placeholder = "Incorrect password! Try again..."
            self.set_focus(password_input)
    
    @on(Button.Pressed, "#cancel-btn")
    def handle_cancel(self) -> None:
        """Handle cancel button."""
        self.dismiss(False)


class SourcesScreen(Screen):
    """Screen for viewing and managing crawled sources."""
    
    CSS = """
    SourcesScreen {
        align: center middle;
    }
    
    #main-container {
        width: 90%;
        height: 90%;
        background: $surface;
        border: solid $primary;
        padding: 2;
    }
    
    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 1;
    }
    
    #controls-container {
        height: auto;
        margin-bottom: 1;
    }
    
    #search-input {
        width: 50%;
    }
    
    #table-container {
        height: 70%;
        border: solid $primary;
        margin-bottom: 1;
    }
    
    #sources-table {
        width: 100%;
        height: 100%;
    }
    
    #stats-container {
        height: auto;
        padding: 1;
        background: $background;
        border: solid $secondary;
    }
    
    #button-container {
        height: auto;
        align: center middle;
        margin-top: 1;
    }
    
    .action-button {
        margin: 0 1;
    }
    
    LoadingIndicator {
        margin: 0 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("📚 Crawled Sources", id="title")
            
            with Horizontal(id="controls-container"):
                yield Label("Search:")
                yield Input(
                    placeholder="Filter by domain...",
                    id="search-input"
                )
                yield Button("Refresh", variant="primary", id="refresh-btn")
                yield LoadingIndicator(id="loading")
            
            with Container(id="table-container"):
                yield DataTable(id="sources-table")
            
            with Container(id="stats-container"):
                yield Label("", id="stats-label")
            
            with Horizontal(id="button-container"):
                yield Button("Select All", variant="secondary", 
                           classes="action-button", id="select-all-btn")
                yield Button("Delete Selected", variant="error", 
                           classes="action-button", id="delete-btn", disabled=True)
                yield Button("Back to Chat", variant="primary", 
                           classes="action-button", id="chat-btn")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.sources: List[Dict[str, Any]] = []
        self.filtered_sources: List[Dict[str, Any]] = []
        self.selected_domains: set = set()
        
        # Hide loading indicator initially
        loading = self.query_one("#loading", LoadingIndicator)
        loading.display = False
        
        # Setup data table
        table = self.query_one("#sources-table", DataTable)
        table.add_columns("✓", "Domain", "Documents", "Last Crawled", "Size")
        table.cursor_type = "row"
        
        # Load sources
        self.refresh_sources()
    
    def update_stats(self):
        """Update statistics display."""
        total_sources = len(self.sources)
        total_docs = sum(s.get("document_count", 0) for s in self.sources)
        selected = len(self.selected_domains)
        
        stats_text = f"Total: {total_sources} sources, {total_docs} documents"
        if selected > 0:
            stats_text += f" | Selected: {selected}"
        
        self.query_one("#stats-label", Label).update(stats_text)
    
    @work(exclusive=True)
    async def refresh_sources(self) -> None:
        """Refresh sources list from server."""
        loading = self.query_one("#loading", LoadingIndicator)
        loading.visible = True
        
        try:
            result = await asyncio.to_thread(get_available_sources)
            
            if result.get("success"):
                self.sources = result.get("sources", [])
                self.app.sources_cache = self.sources  # Update app cache
                self.apply_filter()
            else:
                self.sources = []
                self.update_stats()
        
        except Exception as e:
            self.sources = []
            self.update_stats()
        
        finally:
            loading.visible = False
    
    def apply_filter(self):
        """Apply search filter to sources."""
        search_input = self.query_one("#search-input", Input)
        search_term = search_input.value.lower()
        
        if search_term:
            self.filtered_sources = [
                s for s in self.sources 
                if search_term in s.get("domain", "").lower()
            ]
        else:
            self.filtered_sources = self.sources.copy()
        
        self.display_sources()
    
    def display_sources(self):
        """Display filtered sources in table."""
        table = self.query_one("#sources-table", DataTable)
        table.clear()
        
        for source in self.filtered_sources:
            domain = source.get("domain", "Unknown")
            doc_count = source.get("document_count", 0)
            last_crawled = source.get("last_crawled", "Unknown")
            
            # Format last crawled date
            if last_crawled != "Unknown":
                try:
                    dt = datetime.fromisoformat(last_crawled.replace("Z", "+00:00"))
                    last_crawled = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    pass
            
            # Calculate approximate size
            avg_chunk_size = 1000  # bytes
            size_mb = (doc_count * avg_chunk_size) / (1024 * 1024)
            size_str = f"{size_mb:.1f} MB"
            
            # Check if selected
            check = "☑" if domain in self.selected_domains else "☐"
            
            table.add_row(check, domain, str(doc_count), last_crawled, size_str)
        
        self.update_stats()
        
        # Enable/disable delete button
        self.query_one("#delete-btn", Button).disabled = len(self.selected_domains) == 0
    
    @on(Input.Changed, "#search-input")
    def handle_search_changed(self) -> None:
        """Handle search input changes."""
        self.apply_filter()
    
    @on(Button.Pressed, "#refresh-btn")
    def handle_refresh(self) -> None:
        """Handle refresh button."""
        self.refresh_sources()
    
    @on(DataTable.RowSelected)
    def handle_row_selected(self, event: DataTable.RowSelected) -> None:
        """Handle row selection in table."""
        if event.row_index < len(self.filtered_sources):
            source = self.filtered_sources[event.row_index]
            domain = source.get("domain", "")
            table = self.query_one("#sources-table", DataTable)
            
            if domain in self.selected_domains:
                self.selected_domains.remove(domain)
                table.update_cell_at((event.row_index, 0), "☐")
            else:
                self.selected_domains.add(domain)
                table.update_cell_at((event.row_index, 0), "☑")
            
            self.update_stats()
            self.query_one("#delete-btn", Button).disabled = len(self.selected_domains) == 0
    
    @on(Button.Pressed, "#select-all-btn")
    def handle_select_all(self) -> None:
        """Handle select all button."""
        table = self.query_one("#sources-table", DataTable)
        
        if len(self.selected_domains) == len(self.filtered_sources):
            # Deselect all
            self.selected_domains.clear()
            for i in range(len(self.filtered_sources)):
                table.update_cell_at((i, 0), "☐")
        else:
            # Select all
            for i, source in enumerate(self.filtered_sources):
                domain = source.get("domain", "")
                self.selected_domains.add(domain)
                table.update_cell_at((i, 0), "☑")
        
        self.update_stats()
        self.query_one("#delete-btn", Button).disabled = len(self.selected_domains) == 0
    
    @on(Button.Pressed, "#delete-btn")
    async def handle_delete(self) -> None:
        """Handle delete button with password confirmation."""
        if not self.selected_domains:
            return
        
        # Show password modal
        modal = PasswordModal(list(self.selected_domains))
        confirmed = await self.app.push_screen_wait(modal)
        
        if confirmed:
            await self.delete_selected_sources()
    
    @work(exclusive=True)
    async def delete_selected_sources(self) -> None:
        """Delete selected sources asynchronously."""
        loading = self.query_one("#loading", LoadingIndicator)
        loading.visible = True
        
        try:
            deleted_count = 0
            
            for domain in list(self.selected_domains):
                try:
                    result = await asyncio.to_thread(
                        delete_source,
                        domain=domain
                    )
                    
                    if result.get("success"):
                        deleted_count += 1
                
                except Exception as e:
                    pass  # Continue with other deletions
            
            # Clear selections
            self.selected_domains.clear()
            
            # Show result in stats
            self.query_one("#stats-label", Label).update(
                f"Successfully deleted {deleted_count} source(s)"
            )
            
            # Refresh list
            await self.refresh_sources()
        
        finally:
            loading.visible = False
    
    @on(Button.Pressed, "#chat-btn")
    def handle_chat(self) -> None:
        """Handle back to chat button."""
        self.app.show_chat()