"""Settings screen with configuration management."""

import os
from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON>ult
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, Input, Select, Footer
)
from textual.containers import Container, Vertical, Horizontal
from textual import on
from rich.panel import Panel


class SettingsScreen(Screen):
    """Screen for managing application settings."""
    
    CSS = """
    SettingsScreen {
        align: center middle;
    }
    
    #main-container {
        width: 60%;
        height: auto;
        max-height: 80%;
        background: $surface;
        border: solid $primary;
        padding: 2;
    }
    
    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 2;
    }
    
    .setting-group {
        margin-bottom: 2;
        padding: 1;
        border: solid $secondary;
    }
    
    .setting-label {
        margin-bottom: 0.5;
        color: $accent;
    }
    
    .setting-input {
        width: 100%;
        margin-bottom: 1;
    }
    
    #model-select {
        width: 100%;
        margin-bottom: 1;
    }
    
    #button-container {
        height: auto;
        align: center middle;
        margin-top: 2;
    }
    
    .action-button {
        margin: 0 1;
    }
    
    #status-message {
        text-align: center;
        margin-top: 1;
    }
    """
    
    # Available OpenRouter models
    AVAILABLE_MODELS = [
        ("gpt-4o-mini", "GPT-4 Optimized Mini (Default)"),
        ("gpt-4o", "GPT-4 Optimized"),
        ("anthropic/claude-3-haiku", "Claude 3 Haiku (Fast)"),
        ("anthropic/claude-3-sonnet", "Claude 3 Sonnet (Balanced)"),
        ("anthropic/claude-3-opus", "Claude 3 Opus (Powerful)"),
        ("meta-llama/llama-3.1-8b-instruct", "Llama 3.1 8B (Cost-effective)"),
        ("meta-llama/llama-3.1-70b-instruct", "Llama 3.1 70B (Powerful)"),
        ("google/gemini-pro", "Gemini Pro"),
        ("mistralai/mistral-medium", "Mistral Medium"),
    ]
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("⚙️ Settings", id="title")
            
            with Vertical(classes="setting-group"):
                yield Label("OpenRouter Model Selection", classes="setting-label")
                yield Select(
                    options=[(label, value) for value, label in self.AVAILABLE_MODELS],
                    prompt="Select a model",
                    id="model-select",
                    classes="setting-input"
                )
                yield Label(
                    "This model will be used for query enhancement and chat responses.",
                    classes="dim"
                )
            
            with Vertical(classes="setting-group"):
                yield Label("Admin Password", classes="setting-label")
                yield Input(
                    password=True,
                    placeholder="Enter new admin password",
                    id="password-input",
                    classes="setting-input"
                )
                yield Label(
                    "This password is required for deleting sources. Leave blank to keep current.",
                    classes="dim"
                )
            
            with Vertical(classes="setting-group"):
                yield Label("Environment Configuration", classes="setting-label")
                yield Static(self._get_env_status(), id="env-status")
            
            with Horizontal(id="button-container"):
                yield Button("Save Settings", variant="primary", 
                           classes="action-button", id="save-btn")
                yield Button("Cancel", variant="secondary", 
                           classes="action-button", id="cancel-btn")
            
            yield Label("", id="status-message")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        # Set current values
        model_select = self.query_one("#model-select", Select)
        current_model = self.app.settings.get("openrouter_model", "gpt-4o-mini")
        
        # Find and set the current model
        for value, label in self.AVAILABLE_MODELS:
            if value == current_model:
                model_select.value = label
                break
    
    def _get_env_status(self) -> Panel:
        """Get environment configuration status panel."""
        env_vars = {
            "SUPABASE_URL": "✓" if os.getenv("SUPABASE_URL") else "✗",
            "SUPABASE_SERVICE_KEY": "✓" if os.getenv("SUPABASE_SERVICE_KEY") else "✗",
            "OPENAI_API_KEY": "✓" if os.getenv("OPENAI_API_KEY") else "✗",
            "OPENROUTER_API_KEY": "✓" if os.getenv("OPENROUTER_API_KEY") else "✗",
            "LLM_PROVIDER": os.getenv("LLM_PROVIDER", "openai"),
        }
        
        status_text = "Environment Variables:\n"
        for key, value in env_vars.items():
            if key == "LLM_PROVIDER":
                status_text += f"  {key}: {value}\n"
            else:
                status_text += f"  {key}: {value}\n"
        
        return Panel(
            status_text.strip(),
            title="Configuration Status",
            border_style="dim"
        )
    
    @on(Button.Pressed, "#save-btn")
    def handle_save(self) -> None:
        """Handle save button press."""
        model_select = self.query_one("#model-select", Select)
        password_input = self.query_one("#password-input", Input)
        status_message = self.query_one("#status-message", Label)
        
        # Update model setting
        if model_select.value:
            # Find the model value from the label
            for value, label in self.AVAILABLE_MODELS:
                if label == model_select.value:
                    self.app.settings["openrouter_model"] = value
                    # Also update environment variable
                    os.environ["QUERY_ENHANCEMENT_MODEL"] = value
                    break
        
        # Update password if provided
        new_password = password_input.value.strip()
        if new_password:
            self.app.settings["admin_password"] = new_password
            os.environ["ADMIN_PASSWORD"] = new_password
        
        # Show success message
        status_message.update("[green]✓ Settings saved successfully![/green]")
        
        # Clear password field for security
        password_input.value = ""
        
        # Go back to home after a short delay
        self.set_timer(2.0, self.app.action_go_home)
    
    @on(Button.Pressed, "#cancel-btn")
    def handle_cancel(self) -> None:
        """Handle cancel button press."""
        self.app.pop_screen()