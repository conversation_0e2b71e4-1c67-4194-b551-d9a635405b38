"""Home screen with gradient title and main menu."""

from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON>ult
from textual.screen import Screen
from textual.widgets import Static, Button, Label
from textual.containers import Container, Vertical, Horizontal
from textual import on
from rich.text import Text
from rich.console import Console
from rich.style import Style


class BlockTitle(Static):
    """Custom widget for big block letter title display."""

    def __init__(self):
        # Create simple, readable title
        title_text = Text()

        # Simple but bold title that's actually readable
        title_text.append("AY KNOWLEDGE BASE", style=Style(color="#ff0080", bold=True))
        title_text.append("\n")
        title_text.append("v0.1", style=Style(color="#ffffff", bold=False))

        super().__init__(title_text)



class HomeScreen(Screen):
    """Main home screen with menu options."""
    
    CSS = """
    /* SIMPLE CSS - FORCE EVERYTHING TO BE VISIBLE */

    HomeScreen {
        background: black;
        color: white;
    }

    #title-container {
        align: center middle;
        margin: 2;
    }

    #menu-container {
        align: center middle;
        width: 60;
        background: #1a1a1a;
        border: solid #ff0080;
        padding: 2;
    }

    #status-bar {
        dock: bottom;
        height: 3;
        background: #1a1a1a;
        color: white;
        padding: 1;
    }

    BlockTitle {
        color: #ff0080;
        text-style: bold;
        text-align: center;
    }

    .subtitle {
        color: #ff0080;
        text-align: center;
        text-style: italic;
    }

    .status-label {
        color: white;
        text-style: bold;
    }

    #connection-status {
        color: #cccccc;
    }

    /* SIMPLE BUTTON STYLING - NO VARIABLES */
    Button {
        width: 100%;
        height: 3;
        margin: 1 0;
        padding: 1;
        background: #333333;
        color: white;
        border: solid #ff0080;
        text-style: bold;
    }

    Button:hover {
        background: #ff0080;
        color: black;
        text-style: bold;
    }

    Button:focus {
        background: #ff0080;
        color: black;
        text-style: bold;
    }

    Button.-primary {
        background: #333333;
        color: white;
        border: solid #ff0080;
        text-style: bold;
    }

    /* Menu Labels - Should be clearly visible */
    #menu-container Label {
        color: white;
        text-style: bold;
        text-align: center;
        background: #333333;
        border: solid #ff0080;
        padding: 1;
        margin: 1 0;
        width: 100%;
        height: 3;
    }

    .instruction {
        color: #ff0080;
        text-style: italic;
        text-align: center;
        margin: 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        with Container(id="title-container"):
            yield BlockTitle()
            yield Label("Intelligent RAG-powered Knowledge Management",
                       classes="subtitle")
        
        with Vertical(id="menu-container"):
            # Using Labels temporarily to ensure text is visible
            yield Label("1. Crawl by URL", id="menu-1")
            yield Label("2. Crawl by Search", id="menu-2")
            yield Label("3. Queue Manager", id="menu-3")
            yield Label("4. Chat/Query", id="menu-4")
            yield Label("5. View Sources", id="menu-5")
            yield Label("6. Settings", id="menu-6")
            yield Label("7. Help", id="menu-7")
            yield Label("")
            yield Label("Press number key to select option", classes="instruction")
        
        with Horizontal(id="status-bar"):
            yield Label("Status: ", classes="status-label")
            yield Label("", id="connection-status")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.update_status()
        # No need to set focus since we're using keyboard navigation now
    
    def update_status(self):
        """Update connection status display."""
        status_label = self.query_one("#connection-status", Label)
        status_label.update(self.app.get_connection_status())
    
    # Removed old button handlers - now using keyboard navigation

    def on_key(self, event) -> None:
        """Handle keyboard input for menu navigation."""
        if event.key == "1":
            self.app.show_crawl_url()
        elif event.key == "2":
            self.app.show_crawl_search()
        elif event.key == "3":
            self.app.show_queue_manager()
        elif event.key == "4":
            self.app.show_chat()
        elif event.key == "5":
            self.app.show_sources()
        elif event.key == "6":
            self.app.show_settings()
        elif event.key == "7":
            self.app.show_help()