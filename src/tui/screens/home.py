"""Home screen with gradient title and main menu."""

from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON>ult
from textual.screen import Screen
from textual.widgets import Static, Button, Label
from textual.containers import Container, Vertical, Horizontal
from textual import on
from rich.text import Text
from rich.console import Console
from rich.style import Style


class BlockTitle(Static):
    """Custom widget for big block letter title display."""

    def __init__(self):
        # Create simple, readable title
        title_text = Text()

        # Simple but bold title that's actually readable
        title_text.append("AY KNOWLEDGE BASE", style=Style(color="#ff0080", bold=True))
        title_text.append("\n")
        title_text.append("v0.1", style=Style(color="#ffffff", bold=False))

        super().__init__(title_text)



class HomeScreen(Screen):
    """Main home screen with menu options."""
    
    CSS = """
    HomeScreen {
        align: center middle;
        padding: 2;
        background: $background;
    }

    #title-container {
        width: auto;
        height: auto;
        margin-bottom: 2;
        align: center middle;
    }

    #menu-container {
        width: 80;
        height: auto;
        align: center middle;
        background: $surface;
        border: solid $primary;
        padding: 3;
    }

    #status-bar {
        dock: bottom;
        height: 3;
        background: $surface;
        border-top: solid $border;
        padding: 1;
        align: center middle;
    }

    BlockTitle {
        text-style: bold;
        text-align: center;
        padding: 2;
        margin-bottom: 2;
        color: $primary;
    }

    .subtitle {
        text-align: center;
        color: $accent;
        text-style: italic;
        margin-bottom: 2;
    }

    .status-label {
        color: $text-primary;
        text-style: bold;
    }

    #connection-status {
        color: $text-secondary;
    }

    /* FORCE VISIBLE Button Text */
    Button {
        background: #1a1a1a;
        color: #ffffff;
        text-style: bold;
        border: solid #ff0080;
        width: 100%;
        height: 4;
        margin: 1 0;
        text-align: center;
        padding: 1 2;
    }

    Button:hover {
        background: #ff0080;
        color: #000000;
        text-style: bold;
        border: solid #ff0080;
    }

    Button:focus {
        background: #ff0080;
        color: #000000;
        text-style: bold;
        border: solid #ffffff;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        with Container(id="title-container"):
            yield BlockTitle()
            yield Label("Intelligent RAG-powered Knowledge Management",
                       classes="subtitle")
        
        with Vertical(id="menu-container"):
            yield Button("🌐 Crawl by URL", id="crawl-url")
            yield Button("🔍 Crawl by Search", id="crawl-search")
            yield Button("🔄 Queue Manager", id="queue-manager")
            yield Button("💬 Chat/Query", id="chat")
            yield Button("📚 View Crawled Sources", id="sources")
            yield Button("⚙️  Settings", id="settings")
            yield Button("❓ Help", id="help")
        
        with Horizontal(id="status-bar"):
            yield Label("Status: ", classes="status-label")
            yield Label("", id="connection-status")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.update_status()
        self.set_focus(self.query_one("#crawl-url"))
    
    def update_status(self):
        """Update connection status display."""
        status_label = self.query_one("#connection-status", Label)
        status_label.update(self.app.get_connection_status())
    
    @on(Button.Pressed, "#crawl-url")
    def handle_crawl_url(self) -> None:
        """Handle crawl by URL button press."""
        self.app.show_crawl_url()
    
    @on(Button.Pressed, "#crawl-search")
    def handle_crawl_search(self) -> None:
        """Handle crawl by search button press."""
        self.app.show_crawl_search()

    @on(Button.Pressed, "#queue-manager")
    def handle_queue_manager(self) -> None:
        """Handle queue manager button press."""
        self.app.show_queue_manager()

    @on(Button.Pressed, "#chat")
    def handle_chat(self) -> None:
        """Handle chat button press."""
        self.app.show_chat()
    
    @on(Button.Pressed, "#sources")
    def handle_sources(self) -> None:
        """Handle sources button press."""
        self.app.show_sources()
    
    @on(Button.Pressed, "#settings")
    def handle_settings(self) -> None:
        """Handle settings button press."""
        self.app.show_settings()
    
    @on(Button.Pressed, "#help")
    def handle_help(self) -> None:
        """Handle help button press."""
        self.app.show_help()