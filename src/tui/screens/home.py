"""Home screen with gradient title and main menu."""

from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON>ult
from textual.screen import Screen
from textual.widgets import Static, Button, Label
from textual.containers import Container, Vertical, Horizontal
from textual import on
from rich.text import Text
from rich.console import Console
from rich.style import Style


class GradientTitle(Static):
    """Custom widget for gradient title display."""
    
    def __init__(self):
        # Create gradient text
        title_text = "AY KNOWLEDGE BASE v0.1"
        gradient_text = Text()
        
        # Define gradient colors from blue to cyan
        colors = [
            "#0077b6", "#0096c7", "#00b4d8", "#48cae4", "#90e0ef", "#ade8f4"
        ]
        
        # Apply gradient to each character
        for i, char in enumerate(title_text):
            color_idx = int(i * len(colors) / len(title_text))
            gradient_text.append(char, style=Style(color=colors[color_idx], bold=True))
        
        super().__init__(gradient_text)



class HomeScreen(Screen):
    """Main home screen with menu options."""
    
    CSS = """
    HomeScreen {
        align: center middle;
    }

    #title-container {
        width: auto;
        height: auto;
        margin-bottom: 1;
        align: center middle;
    }

    #menu-container {
        width: 60;
        height: auto;
        align: center middle;
        background: $surface;
        border: solid $primary;
        padding: 2;
    }

    #status-bar {
        dock: bottom;
        height: 3;
        background: $surface;
        padding: 1;
        align: center middle;
        color: white;
    }

    GradientTitle {
        text-style: bold;
        text-align: center;
        padding: 1;
    }

    .subtitle {
        text-align: center;
        color: $accent;
        text-style: italic;
        margin-bottom: 2;
    }

    .status-label {
        color: white;
        text-style: bold;
    }

    Button {
        width: 100%;
        height: 3;
        margin: 1 0;
        color: white;
        text-style: bold;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        with Container(id="title-container"):
            yield GradientTitle()
            yield Label("Intelligent RAG-powered Knowledge Management", 
                       classes="subtitle")
        
        with Vertical(id="menu-container"):
            yield Button("🌐 Crawl by URL", id="crawl-url")
            yield Button("🔍 Crawl by Search", id="crawl-search")
            yield Button("💬 Chat/Query", id="chat")
            yield Button("📚 View Crawled Sources", id="sources")
            yield Button("⚙️  Settings", id="settings")
            yield Button("❓ Help", id="help")
        
        with Horizontal(id="status-bar"):
            yield Label("Status: ", classes="status-label")
            yield Label("", id="connection-status")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.update_status()
        self.set_focus(self.query_one("#crawl-url"))
    
    def update_status(self):
        """Update connection status display."""
        status_label = self.query_one("#connection-status", Label)
        status_label.update(self.app.get_connection_status())
    
    @on(Button.Pressed, "#crawl-url")
    def handle_crawl_url(self) -> None:
        """Handle crawl by URL button press."""
        self.app.show_crawl_url()
    
    @on(Button.Pressed, "#crawl-search")
    def handle_crawl_search(self) -> None:
        """Handle crawl by search button press."""
        self.app.show_crawl_search()
    
    @on(Button.Pressed, "#chat")
    def handle_chat(self) -> None:
        """Handle chat button press."""
        self.app.show_chat()
    
    @on(Button.Pressed, "#sources")
    def handle_sources(self) -> None:
        """Handle sources button press."""
        self.app.show_sources()
    
    @on(Button.Pressed, "#settings")
    def handle_settings(self) -> None:
        """Handle settings button press."""
        self.app.show_settings()
    
    @on(Button.Pressed, "#help")
    def handle_help(self) -> None:
        """Handle help button press."""
        self.app.show_help()