"""Home screen with gradient title and main menu."""

from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON>ult
from textual.screen import Screen
from textual.widgets import Static, Button, Label
from textual.containers import Container, Vertical, Horizontal
from textual import on
from rich.text import Text
from rich.console import Console
from rich.style import Style


class GradientTitle(Static):
    """Custom widget for gradient title display."""
    
    def __init__(self):
        # Create gradient text
        title_text = "AY KNOWLEDGE BASE v0.1"
        gradient_text = Text()
        
        # Define modern gradient colors - blue to cyan
        colors = [
            "#2563eb", "#3b82f6", "#06b6d4", "#22d3ee", "#67e8f9", "#a5f3fc"
        ]
        
        # Apply gradient to each character
        for i, char in enumerate(title_text):
            color_idx = int(i * len(colors) / len(title_text))
            gradient_text.append(char, style=Style(color=colors[color_idx], bold=True))
        
        super().__init__(gradient_text)



class HomeScreen(Screen):
    """Main home screen with menu options."""
    
    CSS = """
    HomeScreen {
        align: center middle;
        padding: 2;
    }

    #title-container {
        width: auto;
        height: auto;
        margin-bottom: 3;
        align: center middle;
    }

    #menu-container {
        width: 70;
        height: auto;
        align: center middle;
        background: $surface;
        border: solid $border;
        
        padding: 3;
    }

    #status-bar {
        dock: bottom;
        height: 3;
        background: $surface;
        border-top: solid $border;
        padding: 1;
        align: center middle;
    }

    GradientTitle {
        text-style: bold;
        text-align: center;
        padding: 1;
        margin-bottom: 1;
    }

    .subtitle {
        text-align: center;
        color: $text-secondary;
        text-style: italic;
        margin-bottom: 2;
    }

    .status-label {
        color: $text-primary;
        text-style: bold;
    }

    #connection-status {
        color: $text-secondary;
    }

    /* Modern Menu Button Styling */
    #menu-container Button {
        width: 100%;
        height: 4;
        margin: 1 0;
        background: $surface;
        color: $text-primary;
        text-style: bold;
        border: solid $border;
        
        text-align: left;
        padding: 1 3;
    }

    #menu-container Button:hover {
        background: $primary;
        border: solid $primary;
        color: $text-primary;
    }

    #menu-container Button:focus {
        background: $primary;
        border: solid $accent;
        color: $text-primary;
    }

    /* Icon spacing for menu buttons */
    #menu-container Button {
        content-align: left middle;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        with Container(id="title-container"):
            yield GradientTitle()
            yield Label("Intelligent RAG-powered Knowledge Management", 
                       classes="subtitle")
        
        with Vertical(id="menu-container"):
            yield Button("🌐 Crawl by URL", id="crawl-url")
            yield Button("🔍 Crawl by Search", id="crawl-search")
            yield Button("🔄 Queue Manager", id="queue-manager")
            yield Button("💬 Chat/Query", id="chat")
            yield Button("📚 View Crawled Sources", id="sources")
            yield Button("⚙️  Settings", id="settings")
            yield Button("❓ Help", id="help")
        
        with Horizontal(id="status-bar"):
            yield Label("Status: ", classes="status-label")
            yield Label("", id="connection-status")
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.update_status()
        self.set_focus(self.query_one("#crawl-url"))
    
    def update_status(self):
        """Update connection status display."""
        status_label = self.query_one("#connection-status", Label)
        status_label.update(self.app.get_connection_status())
    
    @on(Button.Pressed, "#crawl-url")
    def handle_crawl_url(self) -> None:
        """Handle crawl by URL button press."""
        self.app.show_crawl_url()
    
    @on(Button.Pressed, "#crawl-search")
    def handle_crawl_search(self) -> None:
        """Handle crawl by search button press."""
        self.app.show_crawl_search()

    @on(Button.Pressed, "#queue-manager")
    def handle_queue_manager(self) -> None:
        """Handle queue manager button press."""
        self.app.show_queue_manager()

    @on(Button.Pressed, "#chat")
    def handle_chat(self) -> None:
        """Handle chat button press."""
        self.app.show_chat()
    
    @on(Button.Pressed, "#sources")
    def handle_sources(self) -> None:
        """Handle sources button press."""
        self.app.show_sources()
    
    @on(Button.Pressed, "#settings")
    def handle_settings(self) -> None:
        """Handle settings button press."""
        self.app.show_settings()
    
    @on(Button.Pressed, "#help")
    def handle_help(self) -> None:
        """Handle help button press."""
        self.app.show_help()