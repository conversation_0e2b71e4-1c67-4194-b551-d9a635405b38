"""URL crawling screen with input and progress display."""

import asyncio
from datetime import datetime
from textual.app import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, Input, 
    ProgressBar, RichLog, Footer
)
from textual.containers import Container, Vertical, Horizontal
from textual import on, work
from textual.worker import Worker, WorkerState
from rich.text import Text


class CrawlURLScreen(Screen):
    """Screen for crawling URLs with progress tracking."""
    
    CSS = """
    CrawlURLScreen {
        align: center middle;
        padding: 2;
    }

    #main-container {
        width: 85%;
        height: 90%;
        background: $surface;
        border: solid $border;
        
        padding: 3;
    }

    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 2;
    }

    #input-container {
        height: auto;
        margin-bottom: 3;
        padding: 2;
        background: $surface;
        
        border: solid $border;
    }

    #input-container Label {
        color: $text-secondary;
        margin-bottom: 1;
        text-style: bold;
    }

    #url-input {
        width: 100%;
        margin-bottom: 1;
        background: $background;
        border: solid $border;
        
        padding: 1;
    }

    #url-input:focus {
        border: solid $accent;
        background: $surface;
    }

    #button-container {
        height: auto;
        align: center middle;
        margin-bottom: 3;
    }

    #progress-container {
        height: auto;
        margin-bottom: 3;
        padding: 2;
        background: $surface;
        
        border: solid $border;
    }

    #progress-label {
        color: $text-secondary;
        text-style: bold;
        margin-bottom: 1;
    }

    #status-label {
        color: $text-muted;
        margin-top: 1;
        text-align: center;
    }

    #log-container {
        height: 1fr;
        border: solid $border;
        
        padding: 1;
        background: $background;
    }

    .crawl-button {
        margin: 0 1;
        height: 3;
        min-width: 16;
        
    }

    #start-btn {
        background: $success;
        color: $text-primary;
    }

    #start-btn:hover {
        background: $success;
    }

    #cancel-btn {
        background: $error;
        color: $text-primary;
    }

    #cancel-btn:hover {
        background: $error;
    }

    ProgressBar {
        width: 100%;
        margin-bottom: 1;
        background: $surface;
        
    }

    ProgressBar > .bar--complete {
        background: $accent;
    }

    RichLog {
        background: $background;
        scrollbar-size: 1 1;
        
        padding: 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("🌐 Crawl by URL", id="title")
            
            with Vertical(id="input-container"):
                yield Label("Enter URL to crawl:")
                yield Input(
                    placeholder="https://example.com or sitemap.xml",
                    id="url-input"
                )
            
            with Horizontal(id="button-container"):
                yield Button("Start Crawling", variant="primary", 
                           classes="crawl-button", id="start-btn")
                yield Button("Cancel", variant="error", 
                           classes="crawl-button", id="cancel-btn", disabled=True)
            
            with Vertical(id="progress-container"):
                yield Label("Progress:", id="progress-label")
                yield ProgressBar(id="progress-bar", show_percentage=True)
                yield Label("", id="status-label")
            
            with Container(id="log-container"):
                yield RichLog(id="crawl-log", highlight=True, markup=True)
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.crawl_worker = None
        self.status_worker = None
        self.current_job_id = None
        self.set_focus(self.query_one("#url-input"))
        self.update_progress(0, "Ready to crawl")
    
    def update_progress(self, progress: float, status: str):
        """Update progress bar and status."""
        progress_bar = self.query_one("#progress-bar", ProgressBar)
        status_label = self.query_one("#status-label", Label)
        
        progress_bar.update(progress=progress)
        status_label.update(status)
    
    def log_message(self, message: str, style: str = ""):
        """Add message to log."""
        log = self.query_one("#crawl-log", RichLog)
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if style:
            log.write(f"[dim]{timestamp}[/dim] [{style}]{message}[/{style}]")
        else:
            log.write(f"[dim]{timestamp}[/dim] {message}")
    
    @on(Button.Pressed, "#start-btn")
    def handle_start_crawl(self) -> None:
        """Handle start crawling button."""
        url_input = self.query_one("#url-input", Input)
        url = url_input.value.strip()
        
        if not url:
            self.log_message("Please enter a URL", "red")
            return
        
        # Disable start button, enable cancel
        self.query_one("#start-btn", Button).disabled = True
        self.query_one("#cancel-btn", Button).disabled = False
        
        # Start crawling
        self.crawl_worker = self.crawl_url(url)
    
    @on(Button.Pressed, "#cancel-btn")
    def handle_cancel_crawl(self) -> None:
        """Handle cancel crawling button."""
        if self.status_worker:
            self.status_worker.cancel()
            self.log_message("Cancelling job...", "yellow")
        elif self.crawl_worker:
            self.crawl_worker.cancel()
            self.log_message("Cancelling job submission...", "yellow")
            self.reset_ui()
    
    @work(exclusive=True)
    async def crawl_url(self, url: str) -> None:
        """Submit crawl job to Redis queue and monitor progress."""
        try:
            self.log_message(f"Submitting crawl job for: {url}", "cyan")
            self.update_progress(0.1, "Submitting job to queue...")

            # Import the crawling function
            from tui.app import smart_crawl_url

            # Submit job to Redis queue
            result = await asyncio.to_thread(smart_crawl_url, url=url)

            if result.get("success") and result.get("job_id"):
                job_id = result["job_id"]
                self.current_job_id = job_id
                self.log_message(f"Job submitted successfully! Job ID: {job_id}", "green")
                self.log_message(f"URL: {result.get('url', url)}", "cyan")

                # Start monitoring job status
                self.status_worker = self.monitor_job_status(job_id)

            else:
                error = result.get("error", "Failed to submit job")
                self.log_message(f"Job submission failed: {error}", "red")
                self.update_progress(0, f"Failed: {error}")
                self.reset_ui()

        except asyncio.CancelledError:
            self.log_message("Job submission cancelled", "yellow")
            self.update_progress(0, "Cancelled")
            raise

        except Exception as e:
            self.log_message(f"Error submitting job: {str(e)}", "red")
            self.update_progress(0, f"Error: {str(e)}")
            self.reset_ui()

    @work(exclusive=True)
    async def monitor_job_status(self, job_id: str) -> None:
        """Monitor job status with polling."""
        try:
            from tui.app import get_crawl_job_status

            self.log_message("Monitoring job status...", "cyan")
            poll_count = 0

            while True:
                poll_count += 1

                # Get job status
                status_result = await asyncio.to_thread(get_crawl_job_status, job_id)

                if not status_result.get("success"):
                    error = status_result.get("error", "Failed to get job status")
                    self.log_message(f"Status check failed: {error}", "red")
                    self.update_progress(0, f"Status error: {error}")
                    break

                job_status = status_result.get("status", "unknown")
                self.log_message(f"Job status: {job_status} (check #{poll_count})", "yellow")

                if job_status == "queued":
                    self.update_progress(0.2, "Job queued, waiting for worker...")

                elif job_status == "processing":
                    started_at = status_result.get("started_at", "")
                    self.update_progress(0.5, f"Processing... (started: {started_at})")

                elif job_status == "completed":
                    # Job completed successfully
                    completed_at = status_result.get("completed_at", "")
                    duration = status_result.get("duration_seconds", 0)

                    self.log_message(f"Job completed successfully!", "green")
                    self.log_message(f"Completed at: {completed_at}", "cyan")
                    self.log_message(f"Duration: {duration} seconds", "cyan")

                    # Extract results
                    job_result = status_result.get("result", {})
                    if job_result:
                        pages_crawled = job_result.get("total_pages_crawled", 0)
                        chunks_stored = job_result.get("total_chunks_stored", 0)
                        sources = job_result.get("sources_crawled", [])

                        self.log_message(f"Pages crawled: {pages_crawled}", "green")
                        self.log_message(f"Chunks stored: {chunks_stored}", "green")
                        if sources:
                            self.log_message(f"Sources: {', '.join(sources)}", "green")

                    self.update_progress(1.0, "Crawling completed successfully!")

                    # Refresh sources cache
                    self.app.refresh_sources_cache()
                    break

                elif job_status == "failed":
                    # Job failed
                    error = status_result.get("error", "Unknown error")
                    retry_count = status_result.get("retry_count", 0)

                    self.log_message(f"Job failed: {error}", "red")
                    if retry_count > 0:
                        self.log_message(f"Retries attempted: {retry_count}", "yellow")

                    self.update_progress(0, f"Failed: {error}")
                    break

                elif job_status == "cancelled":
                    self.log_message("Job was cancelled", "yellow")
                    self.update_progress(0, "Job cancelled")
                    break

                # Wait before next poll (5 seconds)
                await asyncio.sleep(5)

        except asyncio.CancelledError:
            # User cancelled monitoring
            if self.current_job_id:
                self.log_message("Attempting to cancel job...", "yellow")
                try:
                    from tui.app import cancel_crawl_job
                    cancel_result = await asyncio.to_thread(cancel_crawl_job, self.current_job_id)
                    if cancel_result.get("success"):
                        self.log_message("Job cancelled successfully", "yellow")
                    else:
                        self.log_message(f"Failed to cancel job: {cancel_result.get('error', 'Unknown error')}", "red")
                except Exception as e:
                    self.log_message(f"Error cancelling job: {str(e)}", "red")

            self.update_progress(0, "Monitoring cancelled")
            raise

        except Exception as e:
            self.log_message(f"Error monitoring job: {str(e)}", "red")
            self.update_progress(0, f"Monitoring error: {str(e)}")

        finally:
            self.reset_ui()
    
    def reset_ui(self):
        """Reset UI to initial state."""
        self.query_one("#start-btn", Button).disabled = False
        self.query_one("#cancel-btn", Button).disabled = True
        self.query_one("#url-input", Input).value = ""
        self.current_job_id = None
        self.crawl_worker = None
        self.status_worker = None
        self.set_focus(self.query_one("#url-input"))