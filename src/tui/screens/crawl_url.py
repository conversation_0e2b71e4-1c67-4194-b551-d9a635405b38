"""URL crawling screen with input and progress display."""

import asyncio
from datetime import datetime
from textual.app import ComposeResult
from textual.screen import Screen
from textual.widgets import (
    Static, Button, Label, Input, 
    ProgressBar, RichLog, Footer
)
from textual.containers import Container, Vertical, Horizontal
from textual import on, work
from textual.worker import Worker, WorkerState
from rich.text import Text


class CrawlURLScreen(Screen):
    """Screen for crawling URLs with progress tracking."""
    
    CSS = """
    CrawlURLScreen {
        align: center middle;
    }
    
    #main-container {
        width: 80%;
        height: 90%;
        background: $surface;
        border: solid $primary;
        padding: 2;
    }
    
    #title {
        text-align: center;
        color: $accent;
        text-style: bold;
        margin-bottom: 1;
    }
    
    #input-container {
        height: auto;
        margin-bottom: 2;
    }
    
    #url-input {
        width: 100%;
        margin-bottom: 1;
    }
    
    #button-container {
        height: auto;
        align: center middle;
        margin-bottom: 2;
    }
    
    #progress-container {
        height: auto;
        margin-bottom: 2;
    }
    
    #log-container {
        height: 1fr;
        border: solid $primary;
        padding: 1;
    }
    
    .crawl-button {
        margin: 0 1;
    }
    
    ProgressBar {
        width: 100%;
        margin-bottom: 1;
    }
    
    RichLog {
        background: $background;
        scrollbar-size: 1 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create child widgets."""
        yield Footer()
        
        with Container(id="main-container"):
            yield Label("🌐 Crawl by URL", id="title")
            
            with Vertical(id="input-container"):
                yield Label("Enter URL to crawl:")
                yield Input(
                    placeholder="https://example.com or sitemap.xml",
                    id="url-input"
                )
            
            with Horizontal(id="button-container"):
                yield Button("Start Crawling", variant="primary", 
                           classes="crawl-button", id="start-btn")
                yield Button("Cancel", variant="error", 
                           classes="crawl-button", id="cancel-btn", disabled=True)
            
            with Vertical(id="progress-container"):
                yield Label("Progress:", id="progress-label")
                yield ProgressBar(id="progress-bar", show_percentage=True)
                yield Label("", id="status-label")
            
            with Container(id="log-container"):
                yield RichLog(id="crawl-log", highlight=True, markup=True)
    
    def on_mount(self) -> None:
        """Initialize screen on mount."""
        self.crawl_worker = None
        self.set_focus(self.query_one("#url-input"))
        self.update_progress(0, "Ready to crawl")
    
    def update_progress(self, progress: float, status: str):
        """Update progress bar and status."""
        progress_bar = self.query_one("#progress-bar", ProgressBar)
        status_label = self.query_one("#status-label", Label)
        
        progress_bar.update(progress=progress)
        status_label.update(status)
    
    def log_message(self, message: str, style: str = ""):
        """Add message to log."""
        log = self.query_one("#crawl-log", RichLog)
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if style:
            log.write(f"[dim]{timestamp}[/dim] [{style}]{message}[/{style}]")
        else:
            log.write(f"[dim]{timestamp}[/dim] {message}")
    
    @on(Button.Pressed, "#start-btn")
    def handle_start_crawl(self) -> None:
        """Handle start crawling button."""
        url_input = self.query_one("#url-input", Input)
        url = url_input.value.strip()
        
        if not url:
            self.log_message("Please enter a URL", "red")
            return
        
        # Disable start button, enable cancel
        self.query_one("#start-btn", Button).disabled = True
        self.query_one("#cancel-btn", Button).disabled = False
        
        # Start crawling
        self.crawl_worker = self.crawl_url(url)
    
    @on(Button.Pressed, "#cancel-btn")
    def handle_cancel_crawl(self) -> None:
        """Handle cancel crawling button."""
        if self.crawl_worker:
            self.crawl_worker.cancel()
            self.log_message("Crawling cancelled by user", "yellow")
            self.reset_ui()
    
    @work(exclusive=True)
    async def crawl_url(self, url: str) -> None:
        """Crawl the specified URL asynchronously."""
        try:
            self.log_message(f"Starting crawl for: {url}", "cyan")
            self.update_progress(0.1, "Connecting...")
            
            # Import the crawling function
            from ay_rag_mcp import smart_crawl_url
            
            self.update_progress(0.3, "Analyzing URL type...")
            
            # Determine if it's a sitemap or regular URL
            if url.endswith('.xml') or 'sitemap' in url:
                self.log_message("Detected sitemap URL", "green")
                crawl_type = "sitemap"
            elif url.endswith('.txt'):
                self.log_message("Detected text file with URLs", "green")
                crawl_type = "txt_file"
            else:
                self.log_message("Regular webpage detected", "green")
                crawl_type = "single"
            
            self.update_progress(0.5, f"Crawling {crawl_type}...")
            
            # Perform the crawl
            result = await asyncio.to_thread(
                smart_crawl_url, 
                url=url
            )
            
            if result.get("success"):
                self.update_progress(0.9, "Processing results...")
                
                # Log success details
                pages_crawled = result.get("pages_crawled", 0)
                chunks_created = result.get("total_chunks", 0)
                
                self.log_message(f"Successfully crawled {pages_crawled} pages", "green")
                self.log_message(f"Created {chunks_created} content chunks", "green")
                
                if result.get("urls"):
                    self.log_message("Crawled URLs:", "cyan")
                    for crawled_url in result["urls"][:10]:  # Show first 10
                        self.log_message(f"  • {crawled_url}")
                    if len(result["urls"]) > 10:
                        self.log_message(f"  ... and {len(result['urls']) - 10} more")
                
                self.update_progress(1.0, "Crawling completed successfully!")
                
                # Refresh sources cache
                self.app.refresh_sources_cache()
                
            else:
                error = result.get("error", "Unknown error")
                self.log_message(f"Crawling failed: {error}", "red")
                self.update_progress(0, f"Failed: {error}")
        
        except asyncio.CancelledError:
            self.log_message("Crawling cancelled", "yellow")
            self.update_progress(0, "Cancelled")
            raise
        
        except Exception as e:
            self.log_message(f"Error during crawling: {str(e)}", "red")
            self.update_progress(0, f"Error: {str(e)}")
        
        finally:
            self.reset_ui()
    
    def reset_ui(self):
        """Reset UI to initial state."""
        self.query_one("#start-btn", Button).disabled = False
        self.query_one("#cancel-btn", Button).disabled = True
        self.query_one("#url-input", Input).value = ""
        self.set_focus(self.query_one("#url-input"))