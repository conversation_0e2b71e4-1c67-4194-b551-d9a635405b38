"""
MCP Client wrapper for TUI applications.

This module provides a proper interface between the TUI and MCP server,
handling the async/sync boundary and context management correctly.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import aiohttp
import os
from contextlib import asynccontextmanager


class MCPClient:
    """
    Client for communicating with the MCP server from TUI applications.
    
    This class handles the HTTP/SSE communication with the MCP server,
    manages the async/sync boundary, and provides simple synchronous
    methods for the TUI to use.
    """
    
    def __init__(self, server_url: str = None):
        """
        Initialize the MCP client.
        
        Args:
            server_url: URL of the MCP server (default: http://localhost:8051)
        """
        self.server_url = server_url or os.getenv("MCP_SERVER_URL", "http://localhost:8051")
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger(__name__)
        self._is_connected = False
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    async def connect(self):
        """Establish connection to the MCP server."""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test connection with a health check
            await self._health_check()
            self._is_connected = True
            self.logger.info(f"Connected to MCP server at {self.server_url}")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server: {e}")
            self._is_connected = False
            if self.session:
                await self.session.close()
                self.session = None
            raise
    
    async def disconnect(self):
        """Close connection to the MCP server."""
        if self.session:
            await self.session.close()
            self.session = None
        self._is_connected = False
        self.logger.info("Disconnected from MCP server")
    
    async def _health_check(self):
        """Perform a health check against the MCP server."""
        if not self.session:
            raise RuntimeError("Session not initialized")
        
        try:
            async with self.session.get(f"{self.server_url}/health") as response:
                if response.status != 200:
                    raise RuntimeError(f"Health check failed with status {response.status}")
        except aiohttp.ClientError as e:
            raise RuntimeError(f"Failed to reach MCP server: {e}")
    
    async def _call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Call a tool on the MCP server.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool
            
        Returns:
            Tool response as dictionary
        """
        if not self.session or not self._is_connected:
            raise RuntimeError("Not connected to MCP server")
        
        if arguments is None:
            arguments = {}
        
        payload = {
            "tool": tool_name,
            "arguments": arguments
        }
        
        try:
            async with self.session.post(
                f"{self.server_url}/tools/{tool_name}",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    return result
                else:
                    error_text = await response.text()
                    self.logger.error(f"Tool call failed: {error_text}")
                    return {
                        "success": False,
                        "error": f"Server error: {response.status} - {error_text}"
                    }
                    
        except Exception as e:
            self.logger.error(f"Error calling tool {tool_name}: {e}")
            return {
                "success": False,
                "error": f"Connection error: {str(e)}"
            }
    
    # Synchronous wrapper methods for TUI use
    def get_available_sources(self) -> Dict[str, Any]:
        """
        Get available sources synchronously.
        
        Returns:
            Dictionary with sources information
        """
        try:
            return asyncio.run(self._get_available_sources_async())
        except Exception as e:
            self.logger.error(f"Error getting sources: {e}")
            return {
                "success": False,
                "error": str(e),
                "sources": []
            }
    
    async def _get_available_sources_async(self) -> Dict[str, Any]:
        """Async implementation of get_available_sources."""
        if not self._is_connected:
            await self.connect()
        
        result = await self._call_tool("get_available_sources")
        
        # Parse JSON response if needed
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                return {"success": False, "error": "Invalid JSON response", "sources": []}
        
        # Ensure consistent format
        if result.get("success", False):
            sources = result.get("sources", [])
            return {
                "success": True,
                "sources": sources
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Unknown error"),
                "sources": []
            }
    
    def perform_rag_query(self, query: str, source: str = None, match_count: int = 5) -> Dict[str, Any]:
        """
        Perform RAG query synchronously.
        
        Args:
            query: Search query
            source: Optional source filter
            match_count: Number of results to return
            
        Returns:
            Dictionary with query results
        """
        try:
            return asyncio.run(self._perform_rag_query_async(query, source, match_count))
        except Exception as e:
            self.logger.error(f"Error performing RAG query: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "",
                "sources": []
            }
    
    async def _perform_rag_query_async(self, query: str, source: str = None, match_count: int = 5) -> Dict[str, Any]:
        """Async implementation of perform_rag_query."""
        if not self._is_connected:
            await self.connect()
        
        arguments = {
            "query": query,
            "match_count": match_count
        }
        
        if source:
            arguments["source"] = source
        
        result = await self._call_tool("perform_rag_query", arguments)
        
        # Parse JSON response if needed
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "error": "Invalid JSON response",
                    "response": "",
                    "sources": []
                }
        
        # Ensure consistent format
        if result.get("success", False):
            return {
                "success": True,
                "response": result.get("response", "No response generated"),
                "sources": result.get("sources", [])
            }
        else:
            return {
                "success": False,
                "error": result.get("error", "Unknown error"),
                "response": "",
                "sources": []
            }
    
    def crawl_single_page(self, url: str) -> Dict[str, Any]:
        """
        Crawl a single page synchronously.
        
        Args:
            url: URL to crawl
            
        Returns:
            Dictionary with crawl results
        """
        try:
            return asyncio.run(self._crawl_single_page_async(url))
        except Exception as e:
            self.logger.error(f"Error crawling page: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _crawl_single_page_async(self, url: str) -> Dict[str, Any]:
        """Async implementation of crawl_single_page."""
        if not self._is_connected:
            await self.connect()
        
        arguments = {"url": url}
        result = await self._call_tool("crawl_single_page", arguments)
        
        # Parse JSON response if needed
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                return {"success": False, "error": "Invalid JSON response"}
        
        return result
    
    def smart_crawl_url(self, url: str, max_depth: int = 3, max_concurrent: int = 10, 
                       chunk_size: int = 5000, force_direct: bool = False) -> Dict[str, Any]:
        """
        Smart crawl URL synchronously.
        
        Args:
            url: URL to crawl
            max_depth: Maximum crawl depth
            max_concurrent: Maximum concurrent requests
            chunk_size: Chunk size for content
            force_direct: Force direct crawling
            
        Returns:
            Dictionary with crawl results
        """
        try:
            return asyncio.run(self._smart_crawl_url_async(
                url, max_depth, max_concurrent, chunk_size, force_direct
            ))
        except Exception as e:
            self.logger.error(f"Error smart crawling: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _smart_crawl_url_async(self, url: str, max_depth: int = 3, 
                                    max_concurrent: int = 10, chunk_size: int = 5000,
                                    force_direct: bool = False) -> Dict[str, Any]:
        """Async implementation of smart_crawl_url."""
        if not self._is_connected:
            await self.connect()
        
        arguments = {
            "url": url,
            "max_depth": max_depth,
            "max_concurrent": max_concurrent,
            "chunk_size": chunk_size,
            "force_direct": force_direct
        }
        
        result = await self._call_tool("smart_crawl_url", arguments)
        
        # Parse JSON response if needed
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                return {"success": False, "error": "Invalid JSON response"}
        
        return result
    
    def search_code_examples(self, query: str, source_id: str = None, match_count: int = 5) -> Dict[str, Any]:
        """
        Search code examples synchronously.
        
        Args:
            query: Search query
            source_id: Optional source filter
            match_count: Number of results
            
        Returns:
            Dictionary with search results
        """
        try:
            return asyncio.run(self._search_code_examples_async(query, source_id, match_count))
        except Exception as e:
            self.logger.error(f"Error searching code examples: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    async def _search_code_examples_async(self, query: str, source_id: str = None, 
                                         match_count: int = 5) -> Dict[str, Any]:
        """Async implementation of search_code_examples."""
        if not self._is_connected:
            await self.connect()
        
        arguments = {
            "query": query,
            "match_count": match_count
        }
        
        if source_id:
            arguments["source_id"] = source_id
        
        result = await self._call_tool("search_code_examples", arguments)
        
        # Parse JSON response if needed  
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except json.JSONDecodeError:
                return {"success": False, "error": "Invalid JSON response", "results": []}
        
        return result
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to MCP server."""
        return self._is_connected
    
    def test_connection(self) -> bool:
        """Test connection to MCP server synchronously."""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.connect())
                return True
            finally:
                loop.run_until_complete(self.disconnect())
                loop.close()
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False


# Global MCP client instance for TUI use
_global_client: Optional[MCPClient] = None


def get_mcp_client() -> MCPClient:
    """
    Get or create global MCP client instance.
    
    Returns:
        MCPClient instance
    """
    global _global_client
    if _global_client is None:
        _global_client = MCPClient()
    return _global_client


# Backward compatibility functions for existing TUI code
def get_available_sources() -> Dict[str, Any]:
    """Backward compatibility wrapper."""
    client = get_mcp_client()
    return client.get_available_sources()


def perform_rag_query(query: str, source: str = None, top_k: int = 5) -> Dict[str, Any]:
    """Backward compatibility wrapper."""
    client = get_mcp_client()
    return client.perform_rag_query(query, source, top_k)


def crawl_single_page(url: str) -> Dict[str, Any]:
    """Backward compatibility wrapper."""
    client = get_mcp_client()
    return client.crawl_single_page(url)


def smart_crawl_url(url: str, max_depth: int = 3, max_concurrent: int = 10,
                   chunk_size: int = 5000, force_direct: bool = False) -> Dict[str, Any]:
    """Backward compatibility wrapper."""
    client = get_mcp_client()
    return client.smart_crawl_url(url, max_depth, max_concurrent, chunk_size, force_direct)


def search_code_examples(query: str, source_id: str = None, match_count: int = 5) -> Dict[str, Any]:
    """Backward compatibility wrapper."""
    client = get_mcp_client()
    return client.search_code_examples(query, source_id, match_count)