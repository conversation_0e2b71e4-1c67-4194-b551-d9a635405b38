"""AY Knowledge Base TUI Application v0.1

A Terminal User Interface for the AY RAG MCP Server that provides:
- URL and search-based crawling
- Interactive chat with knowledge base
- Source management and deletion
- Configuration settings
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from textual import on
from textual.app import App, ComposeResult
from textual.binding import Binding
from textual.screen import Screen
from textual.widgets import Header, Footer
from textual.css.query import NoMatches
from rich.console import Console
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import MCP client wrapper
from .mcp_client import (
    get_mcp_client,
    get_available_sources,
    perform_rag_query,
    crawl_single_page,
    smart_crawl_url,
    search_code_examples,
    get_crawl_job_status,
    cancel_crawl_job,
    get_queue_stats
)

# Simple Brave search implementation for TUI
def search_with_brave(query: str, count: int = 10) -> Dict[str, Any]:
    """
    Simple Brave search implementation for TUI.
    In a production environment, this would use the actual Brave Search API.
    """
    # For demo purposes, return mock results
    # In production, implement actual Brave Search API call
    return {
        "success": True,
        "results": [
            {
                "title": f"Result {i+1} for: {query}",
                "url": f"https://example{i+1}.com/page",
                "description": f"This is a sample result for '{query}'. In production, this would be actual search results from Brave Search API."
            }
            for i in range(min(count, 5))
        ]
    }

# Import screens
from .screens.home import HomeScreen
from .screens.crawl_url import CrawlURLScreen
from .screens.crawl_search import CrawlSearchScreen
from .screens.queue_manager import QueueManagerScreen
from .screens.chat import ChatScreen
from .screens.sources import SourcesScreen
from .screens.settings import SettingsScreen
from .screens.help import HelpScreen

# Load environment variables
load_dotenv()


class AYKnowledgeBaseTUI(App):
    """Main TUI Application for AY Knowledge Base"""
    
    CSS = """
    /* Black, Grey, White & Neon Pink Color Scheme 🧠 */
    $primary: #ff0080;        /* Neon pink - vibrant primary */
    $primary-hover: #ff33a1;  /* Lighter neon pink for hover */
    $primary-active: #cc0066; /* Darker neon pink for active */

    $secondary: #666666;      /* Medium grey for secondary elements */
    $secondary-light: #999999; /* Light grey for subtle elements */
    $secondary-dark: #333333;  /* Dark grey for borders */

    $accent: #ff0080;         /* Neon pink accent - same as primary */
    $accent-bright: #ff66b3;  /* Bright pink for highlights */
    $accent-glow: #ff0080;    /* Glowing pink effect */

    $background: #000000;     /* Pure black background */
    $surface: #1a1a1a;       /* Dark grey surface */
    $surface-light: #2d2d2d; /* Light grey surface for cards */

    $text-primary: #ffffff;   /* Pure white text */
    $text-secondary: #cccccc; /* Light grey text */
    $text-muted: #888888;     /* Medium grey muted text */

    $error: #ff0080;          /* Neon pink for errors (consistent) */
    $warning: #ffaa00;        /* Orange for warnings */
    $success: #00ff80;        /* Neon green for success */

    $border: #333333;         /* Dark grey border */
    $border-light: #555555;   /* Lighter grey border */

    /* Global Screen Styling */
    Screen {
        background: $background;
        color: $text-primary;
    }

    /* Header and Footer Modern Styling */
    Header {
        background: $surface;
        color: $text-primary;
        border-bottom: solid $border;
    }

    Footer {
        background: $surface;
        color: $text-secondary;
        border-top: solid $border;
    }

    /* SIMPLE BUTTON STYLING - NO CONFLICTS */
    Button {
        background: #333333;
        color: white;
        border: solid #ff0080;
        padding: 1 2;
        margin: 0 1;
        text-style: bold;
    }

    Button:hover {
        background: #ff0080;
        color: black;
        text-style: bold;
    }

    Button:focus {
        background: #ff0080;
        color: black;
        text-style: bold;
    }

    Button.-primary {
        background: $primary;
        color: $background;
        border: solid $primary;
    }

    Button.-secondary {
        background: $secondary;
        color: $text-primary;
        border: solid $secondary;
    }

    Button.-error {
        background: $error;
        color: $background;
        border: solid $error;
    }

    Button:disabled {
        background: $surface;
        color: $text-muted;
        border: solid $secondary-dark;
        opacity: 0.6;
    }

    /* Modern Input Styling */
    Input {
        background: $surface;
        color: $text-primary;
        border: solid $border;
        padding: 1;
    }

    Input:focus {
        border: solid $accent;
        background: $surface-light;
    }

    /* Progress Bar Modern Styling */
    ProgressBar {
        background: $surface;
        color: $accent;
    }

    /* Modern Label Styling */
    Label {
        color: $text-primary;
    }

    .text-muted {
        color: $text-muted;
    }

    .text-secondary {
        color: $text-secondary;
    }

    /* Modern Log Styling */
    RichLog {
        background: $surface;
        border: solid $border;
        padding: 1;
    }
    """
    
    TITLE = "AY Knowledge Base v0.1"
    SUB_TITLE = "Intelligent RAG-powered Knowledge Management"
    
    BINDINGS = [
        Binding("ctrl+h", "go_home", "Home", priority=True),
        Binding("ctrl+q", "quit", "Quit"),
    ]
    
    def __init__(self):
        super().__init__()
        self.console = Console()
        
        # Session state
        self.chat_history: list[Dict[str, Any]] = []
        self.sources_cache: Optional[list] = None
        self.settings = {
            "openrouter_model": os.getenv("QUERY_ENHANCEMENT_MODEL", "gpt-4o-mini"),
            "admin_password": os.getenv("ADMIN_PASSWORD", "admin123")
        }
        
        # Connection status
        self.mcp_connected = False
        self.db_connected = False
        
    def compose(self) -> ComposeResult:
        """Create child widgets for the app."""
        yield Header()
        yield Footer()
        
    def on_mount(self) -> None:
        """Called when app starts."""
        self.check_connections()
        self.push_screen(HomeScreen())
        
    def check_connections(self):
        """Check MCP and database connections."""
        try:
            # Test MCP connection by testing actual connectivity
            mcp_client = get_mcp_client()
            self.mcp_connected = mcp_client.test_connection()
            
            # Test database connection will happen on first operation
            self.db_connected = True
            
        except Exception as e:
            self.console.print(f"[red]Connection check failed: {e}[/red]")
            self.mcp_connected = False
            self.db_connected = False
            
    def action_go_home(self) -> None:
        """Navigate to home screen."""
        # Clear all screens except the base
        while len(self.screen_stack) > 1:
            self.pop_screen()
        
        # Push home screen if not already there
        if not isinstance(self.screen, HomeScreen):
            self.pop_screen()
            self.push_screen(HomeScreen())
            
    def action_quit(self) -> None:
        """Quit the application."""
        self.exit()
        
    # Navigation methods
    def show_crawl_url(self):
        """Navigate to URL crawling screen."""
        self.push_screen(CrawlURLScreen())
        
    def show_crawl_search(self):
        """Navigate to search crawling screen."""
        self.push_screen(CrawlSearchScreen())

    def show_queue_manager(self):
        """Navigate to queue manager screen."""
        self.push_screen(QueueManagerScreen())

    def show_chat(self):
        """Navigate to chat screen."""
        self.push_screen(ChatScreen())
        
    def show_sources(self):
        """Navigate to sources management screen."""
        self.push_screen(SourcesScreen())
        
    def show_settings(self):
        """Navigate to settings screen."""
        self.push_screen(SettingsScreen())
        
    def show_help(self):
        """Navigate to help screen."""
        self.push_screen(HelpScreen())
        
    # Utility methods
    def get_connection_status(self) -> str:
        """Get current connection status string."""
        if self.mcp_connected and self.db_connected:
            return "[green]● Connected[/green]"
        elif self.mcp_connected:
            return "[yellow]● MCP Only[/yellow]"
        else:
            return "[red]● Disconnected[/red]"
            
    def refresh_sources_cache(self):
        """Refresh the cached sources list."""
        try:
            result = get_available_sources()
            if result.get("success"):
                self.sources_cache = result.get("sources", [])
        except Exception as e:
            self.console.print(f"[red]Failed to refresh sources: {e}[/red]")


def run():
    """Run the TUI application."""
    app = AYKnowledgeBaseTUI()
    app.run()


if __name__ == "__main__":
    run()