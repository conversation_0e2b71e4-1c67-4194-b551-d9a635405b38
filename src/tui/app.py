"""AY Knowledge Base TUI Application v0.1

A Terminal User Interface for the AY RAG MCP Server that provides:
- URL and search-based crawling
- Interactive chat with knowledge base
- Source management and deletion
- Configuration settings
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from textual import on
from textual.app import App, ComposeResult
from textual.binding import Binding
from textual.screen import Screen
from textual.widgets import Header, Footer
from textual.css.query import NoMatches
from rich.console import Console
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import MCP client wrapper
from .mcp_client import (
    get_mcp_client,
    get_available_sources,
    perform_rag_query,
    crawl_single_page,
    smart_crawl_url,
    search_code_examples
)

# Simple Brave search implementation for TUI
def search_with_brave(query: str, count: int = 10) -> Dict[str, Any]:
    """
    Simple Brave search implementation for TUI.
    In a production environment, this would use the actual Brave Search API.
    """
    # For demo purposes, return mock results
    # In production, implement actual Brave Search API call
    return {
        "success": True,
        "results": [
            {
                "title": f"Result {i+1} for: {query}",
                "url": f"https://example{i+1}.com/page",
                "description": f"This is a sample result for '{query}'. In production, this would be actual search results from Brave Search API."
            }
            for i in range(min(count, 5))
        ]
    }

# Import screens
from .screens.home import HomeScreen
from .screens.crawl_url import CrawlURLScreen
from .screens.crawl_search import CrawlSearchScreen
from .screens.chat import ChatScreen
from .screens.sources import SourcesScreen
from .screens.settings import SettingsScreen
from .screens.help import HelpScreen

# Load environment variables
load_dotenv()


class AYKnowledgeBaseTUI(App):
    """Main TUI Application for AY Knowledge Base"""
    
    CSS = """
    $primary: #00b4d8;
    $secondary: #0077b6;
    $accent: #90e0ef;
    $background: #03045e;
    $surface: #023e8a;
    $error: #ff006e;
    $success: #06ffa5;
    
    Screen {
        background: $background;
    }
    
    Header {
        background: $surface;
        color: $accent;
    }
    
    Footer {
        background: $surface;
        color: $accent;
    }
    """
    
    TITLE = "AY Knowledge Base v0.1"
    SUB_TITLE = "Intelligent RAG-powered Knowledge Management"
    
    BINDINGS = [
        Binding("ctrl+h", "go_home", "Home", priority=True),
        Binding("ctrl+q", "quit", "Quit"),
    ]
    
    def __init__(self):
        super().__init__()
        self.console = Console()
        
        # Session state
        self.chat_history: list[Dict[str, Any]] = []
        self.sources_cache: Optional[list] = None
        self.settings = {
            "openrouter_model": os.getenv("QUERY_ENHANCEMENT_MODEL", "gpt-4o-mini"),
            "admin_password": os.getenv("ADMIN_PASSWORD", "admin123")
        }
        
        # Connection status
        self.mcp_connected = False
        self.db_connected = False
        
    def compose(self) -> ComposeResult:
        """Create child widgets for the app."""
        yield Header()
        yield Footer()
        
    def on_mount(self) -> None:
        """Called when app starts."""
        self.check_connections()
        self.push_screen(HomeScreen())
        
    def check_connections(self):
        """Check MCP and database connections."""
        try:
            # Test MCP connection by testing actual connectivity
            mcp_client = get_mcp_client()
            self.mcp_connected = mcp_client.test_connection()
            
            # Test database connection will happen on first operation
            self.db_connected = True
            
        except Exception as e:
            self.console.print(f"[red]Connection check failed: {e}[/red]")
            self.mcp_connected = False
            self.db_connected = False
            
    def action_go_home(self) -> None:
        """Navigate to home screen."""
        # Clear all screens except the base
        while len(self.screen_stack) > 1:
            self.pop_screen()
        
        # Push home screen if not already there
        if not isinstance(self.screen, HomeScreen):
            self.pop_screen()
            self.push_screen(HomeScreen())
            
    def action_quit(self) -> None:
        """Quit the application."""
        self.exit()
        
    # Navigation methods
    def show_crawl_url(self):
        """Navigate to URL crawling screen."""
        self.push_screen(CrawlURLScreen())
        
    def show_crawl_search(self):
        """Navigate to search crawling screen."""
        self.push_screen(CrawlSearchScreen())
        
    def show_chat(self):
        """Navigate to chat screen."""
        self.push_screen(ChatScreen())
        
    def show_sources(self):
        """Navigate to sources management screen."""
        self.push_screen(SourcesScreen())
        
    def show_settings(self):
        """Navigate to settings screen."""
        self.push_screen(SettingsScreen())
        
    def show_help(self):
        """Navigate to help screen."""
        self.push_screen(HelpScreen())
        
    # Utility methods
    def get_connection_status(self) -> str:
        """Get current connection status string."""
        if self.mcp_connected and self.db_connected:
            return "[green]● Connected[/green]"
        elif self.mcp_connected:
            return "[yellow]● MCP Only[/yellow]"
        else:
            return "[red]● Disconnected[/red]"
            
    def refresh_sources_cache(self):
        """Refresh the cached sources list."""
        try:
            result = get_available_sources()
            if result.get("success"):
                self.sources_cache = result.get("sources", [])
        except Exception as e:
            self.console.print(f"[red]Failed to refresh sources: {e}[/red]")


def run():
    """Run the TUI application."""
    app = AYKnowledgeBaseTUI()
    app.run()


if __name__ == "__main__":
    run()