"""
Content Type Classification System for Adaptive Code Detection

This module implements intelligent content type detection and adaptive filtering
strategies based on page types to optimize code detection accuracy.
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from urllib.parse import urlparse
from functools import lru_cache
# Temporarily disable optimized patterns to fix container deployment
# from optimized_patterns import get_pattern_matcher


class ContentType(Enum):
    """Types of content pages detected by the classifier"""
    MARKETING = "marketing"
    DOCUMENTATION = "documentation"
    TUTORIAL = "tutorial"
    GITHUB_REPO = "github_repo"
    BLOG = "blog"
    API_REFERENCE = "api_reference"
    UNKNOWN = "unknown"


class FilterStrategy(Enum):
    """Filtering strategies based on content type"""
    STRICT = "strict"        # Heavy filtering for marketing pages
    BALANCED = "balanced"    # Standard filtering for documentation
    LENIENT = "lenient"      # Light filtering for tutorials
    CONSISTENT = "consistent" # Uniform filtering for repos


@dataclass
class ContentClassification:
    """Result of content type classification"""
    content_type: ContentType
    confidence_score: float
    strategy: FilterStrategy
    indicators: List[str]
    metrics: Dict[str, float]


@dataclass
class FilterConfiguration:
    """Configuration for filtering based on content type"""
    confidence_threshold: float
    min_code_indicators: int
    syntax_weight: float
    structure_weight: float
    navigation_penalty: float


class ContentTypeClassifier:
    """Classifies content types and determines adaptive filtering strategies"""
    
    def __init__(self):
        # Content type detection patterns
        self.marketing_patterns = {
            'keywords': [
                'pricing', 'buy now', 'purchase', 'subscribe', 'sign up', 'get started',
                'contact us', 'our team', 'about us', 'testimonials', 'case studies',
                'enterprise', 'solutions', 'services', 'customers', 'partners'
            ],
            'phrases': [
                'why choose us', 'our mission', 'our vision', 'trusted by',
                'industry leader', 'award winning', 'best in class',
                'contact sales', 'request demo', 'free trial'
            ],
            'structural': [
                r'<button[^>]*>.*?(buy|purchase|subscribe|contact).*?</button>',
                r'class=".*?(cta|call-to-action|pricing|hero).*?"',
                r'<form[^>]*>.*?(email|phone|company).*?</form>'
            ]
        }
        
        self.documentation_patterns = {
            'keywords': [
                'api', 'documentation', 'docs', 'reference', 'guide', 'manual',
                'tutorial', 'installation', 'configuration', 'usage', 'examples',
                'parameters', 'returns', 'arguments', 'methods', 'functions'
            ],
            'phrases': [
                'getting started', 'quick start', 'installation guide',
                'api reference', 'user guide', 'developer guide',
                'code example', 'example usage', 'configuration options'
            ],
            'structural': [
                r'```[a-zA-Z]*\n[\s\S]*?```',  # Code blocks
                r'<code>[\s\S]*?</code>',
                r'^\s*#+ [A-Z].*$',  # Headers
                r'^\s*[-*+] .*$'     # Lists
            ]
        }
        
        self.tutorial_patterns = {
            'keywords': [
                'step', 'lesson', 'chapter', 'tutorial', 'walkthrough', 'exercise',
                'practice', 'learn', 'build', 'create', 'implement', 'develop',
                'beginners', 'beginner', 'introduction', 'intro', 'basics', 'getting started'
            ],
            'phrases': [
                'step by step', 'let\'s build', 'in this tutorial', 'you will learn',
                'by the end', 'follow along', 'hands on', 'code along',
                'next step', 'previous step', 'continue to', 'for beginners',
                'tutorial content', 'learn how to', 'how to build'
            ],
            'structural': [
                r'step \d+', r'chapter \d+', r'lesson \d+',
                r'exercise \d+', r'part \d+',
                r'<!-- step.*?-->', r'<h[1-6][^>]*>step',
                r'```[a-zA-Z]*\n[\s\S]*?```.*?explanation'
            ]
        }
        
        self.github_patterns = {
            'keywords': [
                'readme', 'license', 'contributing', 'changelog', 'issues',
                'pull request', 'commit', 'branch', 'fork', 'clone', 'repository'
            ],
            'phrases': [
                'git clone', 'npm install', 'pip install', 'cargo install',
                'go get', 'composer install', 'yarn add', 'gem install'
            ],
            'structural': [
                r'github\.com', r'gitlab\.com', r'bitbucket\.org',
                r'\.git$', r'README\.md', r'package\.json',
                r'requirements\.txt', r'Cargo\.toml', r'pom\.xml'
            ]
        }
        
        self.blog_patterns = {
            'keywords': [
                'published', 'author', 'blog', 'post', 'article', 'thoughts',
                'opinion', 'review', 'experience', 'share', 'discuss'
            ],
            'phrases': [
                'published on', 'written by', 'in this post', 'my experience',
                'i think', 'in my opinion', 'what i learned', 'lessons learned'
            ],
            'structural': [
                r'<time', r'<article', r'<meta.*?author',
                r'<meta.*?published', r'class=".*?author.*?"',
                r'class=".*?(post|article|blog).*?"'
            ]
        }
        
        # URL-based indicators
        self.url_indicators = {
            ContentType.MARKETING: [
                r'/(pricing|contact|about|team|enterprise)',
                r'/(solutions|services|customers|case-studies)',
                r'\.(com|org)/(home|index|landing)'
            ],
            ContentType.DOCUMENTATION: [
                r'/(docs|documentation|api|reference|guide)',
                r'/(manual|help|support)',
                r'docs\.',
                r'api\.'
            ],
            ContentType.TUTORIAL: [
                r'/(tutorial|learn|course|training)',
                r'/(walkthrough|guide|lesson|step)',
                r'/(workshop|bootcamp|academy)',
                r'tutorial\w*\.com',  # Sites like pythontutorial.com
                r'learn\w*\.com',     # Sites like learncoding.com
                r'/tutorial',         # Path contains tutorial
                r'/learn'             # Path contains learn
            ],
            ContentType.GITHUB_REPO: [
                r'github\.com/[^/]+/[^/]+/?$',
                r'gitlab\.com/[^/]+/[^/]+/?$',
                r'bitbucket\.org/[^/]+/[^/]+/?$'
            ],
            ContentType.BLOG: [
                r'/(blog|news|articles|posts)',
                r'/\d{4}/\d{2}/',  # Date-based URLs
                r'medium\.com', r'dev\.to', r'hashnode\.com'
            ]
        }
        
        # Pre-computed empty score/indicator dicts for efficiency
        self._empty_scores = {content_type: 0.0 for content_type in ContentType}
        self._empty_indicators = {content_type: [] for content_type in ContentType}
        
        # Pre-compile regex patterns for performance
        self._compiled_patterns = self._compile_regex_patterns()
        
        # Filter configurations per content type
        self.filter_configs = {
            ContentType.MARKETING: FilterConfiguration(
                confidence_threshold=0.8,  # Very strict
                min_code_indicators=4,
                syntax_weight=0.9,
                structure_weight=0.1,
                navigation_penalty=0.9
            ),
            ContentType.DOCUMENTATION: FilterConfiguration(
                confidence_threshold=0.4,  # Balanced
                min_code_indicators=2,
                syntax_weight=0.6,
                structure_weight=0.4,
                navigation_penalty=0.5
            ),
            ContentType.TUTORIAL: FilterConfiguration(
                confidence_threshold=0.2,  # Lenient
                min_code_indicators=1,
                syntax_weight=0.4,
                structure_weight=0.6,
                navigation_penalty=0.3
            ),
            ContentType.GITHUB_REPO: FilterConfiguration(
                confidence_threshold=0.3,  # Consistent
                min_code_indicators=2,
                syntax_weight=0.5,
                structure_weight=0.5,
                navigation_penalty=0.4
            ),
            ContentType.BLOG: FilterConfiguration(
                confidence_threshold=0.4,  # Balanced
                min_code_indicators=2,
                syntax_weight=0.6,
                structure_weight=0.4,
                navigation_penalty=0.5
            ),
            ContentType.API_REFERENCE: FilterConfiguration(
                confidence_threshold=0.3,  # Lenient for API docs
                min_code_indicators=1,
                syntax_weight=0.7,
                structure_weight=0.3,
                navigation_penalty=0.3
            ),
            ContentType.UNKNOWN: FilterConfiguration(
                confidence_threshold=0.4,  # Default balanced
                min_code_indicators=2,
                syntax_weight=0.5,
                structure_weight=0.5,
                navigation_penalty=0.5
            )
        }
    
    def _compile_regex_patterns(self) -> Dict[str, re.Pattern]:
        """Pre-compile regex patterns for better performance"""
        patterns = {
            'code_blocks': re.compile(r'```[\s\S]*?```'),
            'inline_code': re.compile(r'`[^`]+`'),
            'headers': re.compile(r'^#+\s', re.MULTILINE),
            'bullet_points': re.compile(r'^\s*[-*+]\s', re.MULTILINE),
            'numbered_lists': re.compile(r'^\s*\d+\.\s', re.MULTILINE),
            'buttons': re.compile(r'<button|class=".*?btn', re.IGNORECASE),
            'forms': re.compile(r'<form', re.IGNORECASE),
            'api_methods': re.compile(r'\b(GET|POST|PUT|DELETE|PATCH)\b', re.IGNORECASE),
            'http_methods': re.compile(r'GET\s+/\w+|POST\s+/\w+|PUT\s+/\w+|DELETE\s+/\w+', re.IGNORECASE)
        }
        
        # Compile marketing structural patterns
        patterns.update({
            f'marketing_struct_{i}': re.compile(pattern, re.IGNORECASE | re.DOTALL)
            for i, pattern in enumerate([
                r'<button[^>]*>.*?(buy|purchase|subscribe|contact).*?</button>',
                r'class=".*?(cta|call-to-action|pricing|hero).*?"',
                r'<form[^>]*>.*?(email|phone|company).*?</form>'
            ])
        })
        
        # Compile URL patterns
        for content_type, url_patterns in self.url_indicators.items():
            for i, pattern in enumerate(url_patterns):
                patterns[f'url_{content_type.value}_{i}'] = re.compile(pattern)
        
        return patterns
    
    def classify_content(self, content: str, url: str = "", metadata: Dict[str, Any] = None) -> ContentClassification:
        """
        Classify content type and determine appropriate filtering strategy
        
        Args:
            content: Page content to classify
            url: URL of the page (optional)
            metadata: Additional metadata (optional)
            
        Returns:
            ContentClassification with type, confidence, strategy, and metrics
        """
        if metadata is None:
            metadata = {}
        
        # Multi-factor scoring
        scores = {}
        indicators = {}
        metrics = {}
        
        # Factor 1: URL-based classification
        url_scores, url_indicators = self._analyze_url(url)
        
        # Factor 2: Content pattern analysis
        content_scores, content_indicators = self._analyze_content_patterns(content)
        
        # Factor 3: Structural analysis
        structure_scores, structure_indicators = self._analyze_content_structure(content)
        
        # Factor 4: Code density analysis
        code_density = self._calculate_code_density(content)
        
        # Combine scores with weights
        weights = {
            'url': 0.3,
            'content': 0.4,
            'structure': 0.2,
            'code_density': 0.1
        }
        
        for content_type in ContentType:
            scores[content_type] = (
                url_scores.get(content_type, 0) * weights['url'] +
                content_scores.get(content_type, 0) * weights['content'] +
                structure_scores.get(content_type, 0) * weights['structure'] +
                self._adjust_for_code_density(content_type, code_density) * weights['code_density']
            )
            
            indicators[content_type] = (
                url_indicators.get(content_type, []) +
                content_indicators.get(content_type, []) +
                structure_indicators.get(content_type, [])
            )
        
        # Determine primary content type with tie-breaking
        max_score = max(scores.values())
        
        # Get all types with the maximum score
        top_types = [content_type for content_type, score in scores.items() if score == max_score]
        
        # Tie-breaking priority order (most specific to least specific)
        tie_breaking_order = [
            ContentType.API_REFERENCE,
            ContentType.TUTORIAL, 
            ContentType.GITHUB_REPO,
            ContentType.DOCUMENTATION,
            ContentType.BLOG,
            ContentType.MARKETING,
            ContentType.UNKNOWN
        ]
        
        # Select the highest priority type among tied types
        primary_type = ContentType.UNKNOWN  # fallback
        for preferred_type in tie_breaking_order:
            if preferred_type in top_types:
                primary_type = preferred_type
                break
        
        confidence = scores[primary_type]
        
        # Determine filtering strategy
        strategy = self._determine_strategy(primary_type, confidence)
        
        # Collect metrics
        metrics = {
            'url_score': url_scores.get(primary_type, 0),
            'content_score': content_scores.get(primary_type, 0),
            'structure_score': structure_scores.get(primary_type, 0),
            'code_density': code_density,
            'total_indicators': len(indicators.get(primary_type, [])),
            'confidence_raw': confidence
        }
        
        return ContentClassification(
            content_type=primary_type,
            confidence_score=min(confidence, 1.0),
            strategy=strategy,
            indicators=indicators.get(primary_type, []),
            metrics=metrics
        )
    
    @lru_cache(maxsize=256)
    def _analyze_url_cached(self, url: str) -> Tuple[tuple, tuple]:
        """Cached URL analysis for repeated URLs"""
        scores, indicators = self._analyze_url_impl(url)
        # Convert to tuples for caching (immutable)
        return (
            tuple((ct.value, score) for ct, score in scores.items()),
            tuple((ct.value, tuple(ind)) for ct, ind in indicators.items())
        )
    
    def _analyze_url_impl(self, url: str) -> Tuple[Dict[ContentType, float], Dict[ContentType, List[str]]]:
        """Implementation of URL analysis"""
        scores = self._empty_scores.copy()
        indicators = {ct: [] for ct in ContentType}
        
        if not url:
            return scores, indicators
        
        url_lower = url.lower()
        
        # Use pre-compiled patterns for faster matching
        for content_type, patterns in self.url_indicators.items():
            for i, pattern in enumerate(patterns):
                pattern_key = f'url_{content_type.value}_{i}'
                if pattern_key in self._compiled_patterns:
                    if self._compiled_patterns[pattern_key].search(url_lower):
                        scores[content_type] += 0.3
                        indicators[content_type].append(f"url_pattern:{pattern}")
                else:
                    # Fallback for patterns not pre-compiled
                    if re.search(pattern, url_lower):
                        scores[content_type] += 0.3
                        indicators[content_type].append(f"url_pattern:{pattern}")
        
        return scores, indicators
    
    def _analyze_url(self, url: str) -> Tuple[Dict[ContentType, float], Dict[ContentType, List[str]]]:
        """Analyze URL patterns for content type indicators"""
        if not url:
            return self._empty_scores.copy(), {ct: [] for ct in ContentType}
        
        # Use cached analysis
        cached_scores, cached_indicators = self._analyze_url_cached(url)
        
        # Convert back to proper format
        scores = {ContentType(ct): score for ct, score in cached_scores}
        indicators = {ContentType(ct): list(ind) for ct, ind in cached_indicators}
        
        return scores, indicators
    
    def _analyze_content_patterns(self, content: str) -> Tuple[Dict[ContentType, float], Dict[ContentType, List[str]]]:
        """Analyze content for type-specific patterns"""
        scores = {content_type: 0.0 for content_type in ContentType}
        indicators = {content_type: [] for content_type in ContentType}
        
        content_lower = content.lower()
        
        # Marketing patterns
        marketing_score = 0
        for keyword in self.marketing_patterns['keywords']:
            if keyword in content_lower:
                marketing_score += 0.1
                indicators[ContentType.MARKETING].append(f"keyword:{keyword}")
        
        for phrase in self.marketing_patterns['phrases']:
            if phrase in content_lower:
                marketing_score += 0.2
                indicators[ContentType.MARKETING].append(f"phrase:{phrase}")
        
        for pattern in self.marketing_patterns['structural']:
            if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                marketing_score += 0.3
                indicators[ContentType.MARKETING].append(f"structure:{pattern[:20]}...")
        
        scores[ContentType.MARKETING] = min(marketing_score, 1.0)
        
        # Documentation patterns
        doc_score = 0
        for keyword in self.documentation_patterns['keywords']:
            if keyword in content_lower:
                doc_score += 0.1
                indicators[ContentType.DOCUMENTATION].append(f"keyword:{keyword}")
        
        for phrase in self.documentation_patterns['phrases']:
            if phrase in content_lower:
                doc_score += 0.2
                indicators[ContentType.DOCUMENTATION].append(f"phrase:{phrase}")
        
        # Count code blocks for documentation
        code_blocks = len(re.findall(r'```[\s\S]*?```', content))
        if code_blocks > 0:
            doc_score += min(code_blocks * 0.1, 0.5)
            indicators[ContentType.DOCUMENTATION].append(f"code_blocks:{code_blocks}")
        
        scores[ContentType.DOCUMENTATION] = min(doc_score, 1.0)
        
        # Tutorial patterns
        tutorial_score = 0
        for keyword in self.tutorial_patterns['keywords']:
            if keyword in content_lower:
                tutorial_score += 0.1
                indicators[ContentType.TUTORIAL].append(f"keyword:{keyword}")
        
        for phrase in self.tutorial_patterns['phrases']:
            if phrase in content_lower:
                tutorial_score += 0.2
                indicators[ContentType.TUTORIAL].append(f"phrase:{phrase}")
        
        # Step-by-step indicators
        steps = len(re.findall(r'step \d+|chapter \d+|lesson \d+', content_lower))
        if steps > 0:
            tutorial_score += min(steps * 0.15, 0.6)
            indicators[ContentType.TUTORIAL].append(f"steps:{steps}")
        
        scores[ContentType.TUTORIAL] = min(tutorial_score, 1.0)
        
        # GitHub patterns
        github_score = 0
        for keyword in self.github_patterns['keywords']:
            if keyword in content_lower:
                github_score += 0.1
                indicators[ContentType.GITHUB_REPO].append(f"keyword:{keyword}")
        
        for phrase in self.github_patterns['phrases']:
            if phrase in content_lower:
                github_score += 0.2
                indicators[ContentType.GITHUB_REPO].append(f"phrase:{phrase}")
        
        scores[ContentType.GITHUB_REPO] = min(github_score, 1.0)
        
        # Blog patterns
        blog_score = 0
        for keyword in self.blog_patterns['keywords']:
            if keyword in content_lower:
                blog_score += 0.1
                indicators[ContentType.BLOG].append(f"keyword:{keyword}")
        
        for phrase in self.blog_patterns['phrases']:
            if phrase in content_lower:
                blog_score += 0.2
                indicators[ContentType.BLOG].append(f"phrase:{phrase}")
        
        scores[ContentType.BLOG] = min(blog_score, 1.0)
        
        # API Reference detection (more specific patterns)
        api_keywords = ['endpoint', 'parameters', 'response', 'request', 'method', 'http', 'rest', 'graphql']
        api_patterns = [
            r'GET\s+/\w+', r'POST\s+/\w+', r'PUT\s+/\w+', r'DELETE\s+/\w+',
            r'## (GET|POST|PUT|DELETE|PATCH)', r'### (Parameters|Response|Request Body)',
            r'curl\s+-X\s+(GET|POST|PUT|DELETE)', r'Authorization:\s+Bearer',
            r'/api/\w+', r'HTTP/\d\.\d'
        ]
        
        api_score = sum(0.15 for keyword in api_keywords if keyword in content_lower)
        
        # Boost score for API-specific patterns
        for pattern in api_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                api_score += 0.3
                indicators[ContentType.API_REFERENCE].append(f"api_pattern:{pattern[:15]}...")
        
        # Extra boost for multiple HTTP methods
        http_methods = len(re.findall(r'\b(GET|POST|PUT|DELETE|PATCH)\b', content, re.IGNORECASE))
        if http_methods >= 2:
            api_score += 0.4
            indicators[ContentType.API_REFERENCE].append(f"http_methods:{http_methods}")
        
        scores[ContentType.API_REFERENCE] = min(api_score, 1.0)
        
        return scores, indicators
    
    def _score_pattern_group(self, content_lower: str, content: str, patterns: Dict, 
                           content_type: ContentType, indicators: Dict, 
                           struct_prefix: str = None) -> float:
        """Optimized pattern scoring for a content type group"""
        score = 0
        
        # Score keywords efficiently using set intersection
        found_keywords = {kw for kw in patterns['keywords'] if kw in content_lower}
        score += len(found_keywords) * 0.1
        for keyword in found_keywords:
            indicators[content_type].append(f"keyword:{keyword}")
        
        # Score phrases efficiently
        found_phrases = {phrase for phrase in patterns['phrases'] if phrase in content_lower}
        score += len(found_phrases) * 0.2
        for phrase in found_phrases:
            indicators[content_type].append(f"phrase:{phrase}")
        
        # Score structural patterns using pre-compiled patterns if available
        if struct_prefix and 'structural' in patterns:
            for i, pattern in enumerate(patterns['structural']):
                pattern_key = f'{struct_prefix}{i}'
                if pattern_key in self._compiled_patterns:
                    if self._compiled_patterns[pattern_key].search(content):
                        score += 0.3
                        indicators[content_type].append(f"structure:{pattern[:20]}...")
        
        return score
    
    def _analyze_content_structure(self, content: str) -> Tuple[Dict[ContentType, float], Dict[ContentType, List[str]]]:
        """Analyze content structure for type indicators (optimized)"""
        scores = self._empty_scores.copy()
        indicators = {ct: [] for ct in ContentType}
        
        if not content:
            return scores, indicators
        
        # Use pre-compiled patterns for faster matching
        headers = len(self._compiled_patterns['headers'].findall(content))
        code_blocks = len(self._compiled_patterns['code_blocks'].findall(content))
        bullet_points = len(self._compiled_patterns['bullet_points'].findall(content))
        numbered_lists = len(self._compiled_patterns['numbered_lists'].findall(content))
        buttons = len(self._compiled_patterns['buttons'].findall(content))
        forms = len(self._compiled_patterns['forms'].findall(content))
        
        # Apply scoring rules with early termination for efficiency
        if headers > 3 and code_blocks > 1:
            scores[ContentType.DOCUMENTATION] += 0.4
            indicators[ContentType.DOCUMENTATION].append("structured_docs")
        
        if numbered_lists > 2 or (headers > 2 and bullet_points > 5):
            scores[ContentType.TUTORIAL] += 0.3
            indicators[ContentType.TUTORIAL].append("step_structure")
        
        if buttons > 2 or forms > 1:
            scores[ContentType.MARKETING] += 0.5
            indicators[ContentType.MARKETING].append("cta_elements")
        
        return scores, indicators
    
    def _calculate_code_density(self, content: str) -> float:
        """Calculate the density of code content in the page (optimized)"""
        if not content:
            return 0.0
        
        # Use pre-compiled patterns
        code_blocks = self._compiled_patterns['code_blocks'].findall(content)
        inline_code = self._compiled_patterns['inline_code'].findall(content)
        
        # Calculate total code characters more efficiently
        code_chars = sum(len(block) for block in code_blocks) + sum(len(code) for code in inline_code)
        
        # Calculate density
        return code_chars / len(content) if content else 0.0
    
    def _adjust_for_code_density(self, content_type: ContentType, code_density: float) -> float:
        """Adjust scoring based on code density expectations for content type"""
        # Expected code density ranges
        expected_density = {
            ContentType.MARKETING: 0.0,      # Marketing should have minimal code
            ContentType.DOCUMENTATION: 0.2,  # Docs should have moderate code
            ContentType.TUTORIAL: 0.3,       # Tutorials should have more code
            ContentType.GITHUB_REPO: 0.4,    # Repos should have high code density
            ContentType.BLOG: 0.1,          # Blogs have minimal code
            ContentType.API_REFERENCE: 0.3,  # API docs have moderate code
            ContentType.UNKNOWN: 0.1
        }
        
        expected = expected_density.get(content_type, 0.1)
        
        # Score based on how well the density matches expectations
        if code_density == 0 and expected == 0:
            return 1.0  # Perfect match for marketing
        elif code_density == 0:
            return 0.0  # No code when expected
        else:
            # Calculate how close the density is to expected
            ratio = min(code_density, expected) / max(code_density, expected)
            return ratio
    
    def _determine_strategy(self, content_type: ContentType, confidence: float) -> FilterStrategy:
        """Determine filtering strategy based on content type and confidence"""
        if confidence < 0.1:
            return FilterStrategy.BALANCED  # Default for extremely low confidence
        
        strategy_mapping = {
            ContentType.MARKETING: FilterStrategy.STRICT,
            ContentType.DOCUMENTATION: FilterStrategy.BALANCED,
            ContentType.TUTORIAL: FilterStrategy.LENIENT,
            ContentType.GITHUB_REPO: FilterStrategy.CONSISTENT,
            ContentType.BLOG: FilterStrategy.BALANCED,
            ContentType.API_REFERENCE: FilterStrategy.LENIENT,
            ContentType.UNKNOWN: FilterStrategy.BALANCED
        }
        
        return strategy_mapping.get(content_type, FilterStrategy.BALANCED)
    
    def get_filter_config(self, classification: ContentClassification) -> FilterConfiguration:
        """Get filter configuration for a content classification"""
        base_config = self.filter_configs[classification.content_type]
        
        # Adjust configuration based on confidence
        if classification.confidence_score < 0.3:
            # Very low confidence -> use balanced default to avoid over-filtering
            adjusted_config = FilterConfiguration(
                confidence_threshold=0.4,  # Balanced default
                min_code_indicators=2,     # Balanced default
                syntax_weight=0.6,
                structure_weight=0.4,
                navigation_penalty=0.5
            )
            return adjusted_config
        elif classification.confidence_score < 0.5:
            # Low confidence -> slightly more lenient than base config
            adjusted_config = FilterConfiguration(
                confidence_threshold=max(base_config.confidence_threshold - 0.1, 0.2),
                min_code_indicators=max(base_config.min_code_indicators - 1, 1),
                syntax_weight=base_config.syntax_weight,
                structure_weight=base_config.structure_weight,
                navigation_penalty=max(base_config.navigation_penalty - 0.1, 0.1)
            )
            return adjusted_config
        
        return base_config
    
    def should_apply_strict_filtering(self, classification: ContentClassification) -> bool:
        """Determine if strict filtering should be applied"""
        return (
            classification.content_type == ContentType.MARKETING and 
            classification.confidence_score > 0.6
        )
    
    def get_performance_metrics(self, classifications: List[ContentClassification]) -> Dict[str, Any]:
        """Calculate performance metrics for content classifications"""
        if not classifications:
            return {}
        
        # Count by content type
        type_counts = {}
        strategy_counts = {}
        confidence_stats = []
        
        for classification in classifications:
            type_name = classification.content_type.value
            strategy_name = classification.strategy.value
            
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
            strategy_counts[strategy_name] = strategy_counts.get(strategy_name, 0) + 1
            confidence_stats.append(classification.confidence_score)
        
        # Calculate statistics
        avg_confidence = sum(confidence_stats) / len(confidence_stats)
        min_confidence = min(confidence_stats)
        max_confidence = max(confidence_stats)
        
        return {
            'total_classifications': len(classifications),
            'content_type_distribution': type_counts,
            'strategy_distribution': strategy_counts,
            'confidence_stats': {
                'average': avg_confidence,
                'minimum': min_confidence,
                'maximum': max_confidence
            },
            'strict_filtering_rate': strategy_counts.get('strict', 0) / len(classifications),
            'high_confidence_rate': len([c for c in confidence_stats if c > 0.7]) / len(classifications)
        }


class AdaptiveFilterManager:
    """Manages adaptive filtering based on content type classification"""
    
    def __init__(self, classifier: ContentTypeClassifier = None):
        self.classifier = classifier or ContentTypeClassifier()
        self.performance_history = []
    
    def get_adaptive_filter_config(self, content: str, url: str = "", metadata: Dict[str, Any] = None) -> Tuple[ContentClassification, FilterConfiguration]:
        """Get adaptive filter configuration for content"""
        classification = self.classifier.classify_content(content, url, metadata)
        filter_config = self.classifier.get_filter_config(classification)
        
        # Store for performance tracking
        self.performance_history.append(classification)
        
        return classification, filter_config
    
    def should_review_manually(self, classification: ContentClassification, filter_result: Any) -> bool:
        """Determine if a filtering result should be queued for manual review"""
        # Queue for manual review if:
        # 1. Low confidence classification
        # 2. Borderline filter results
        # 3. Unexpected patterns
        
        if classification.confidence_score < 0.3:
            return True
        
        # Check if filter result conflicts with content type expectations
        if hasattr(filter_result, 'is_valid_code') and hasattr(filter_result, 'confidence_score'):
            if (classification.content_type == ContentType.TUTORIAL and 
                not filter_result.is_valid_code and 
                filter_result.confidence_score > 0.2):
                return True
        
        return False
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report for adaptive filtering"""
        if not self.performance_history:
            return {"status": "no_data"}
        
        # Get base metrics
        metrics = self.classifier.get_performance_metrics(self.performance_history)
        
        # Add adaptive filtering specific metrics
        recent_classifications = self.performance_history[-100:]  # Last 100
        
        metrics['recent_performance'] = {
            'classifications_count': len(recent_classifications),
            'avg_confidence': sum(c.confidence_score for c in recent_classifications) / len(recent_classifications),
            'strategy_effectiveness': self._calculate_strategy_effectiveness(recent_classifications)
        }
        
        return metrics
    
    def _calculate_strategy_effectiveness(self, classifications: List[ContentClassification]) -> Dict[str, float]:
        """Calculate effectiveness of different filtering strategies"""
        strategy_scores = {}
        
        for classification in classifications:
            strategy = classification.strategy.value
            confidence = classification.confidence_score
            
            if strategy not in strategy_scores:
                strategy_scores[strategy] = []
            strategy_scores[strategy].append(confidence)
        
        # Calculate average confidence per strategy
        effectiveness = {}
        for strategy, scores in strategy_scores.items():
            effectiveness[strategy] = sum(scores) / len(scores) if scores else 0.0
        
        return effectiveness