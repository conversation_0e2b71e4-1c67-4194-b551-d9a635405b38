"""
Enhanced Content Filtering System for AY RAG MCP Server

This module implements high-accuracy content filtering that removes navigation,
boilerplate, and low-quality content during the crawling process. It integrates
with the existing ContentTypeClassifier and false positive filters to provide
comprehensive content quality enhancement.

Features:
- Navigation pattern detection and removal
- Boilerplate content filtering
- Content density analysis
- Adaptive filtering based on content type
- Integration with Crawl4AI CrawlerRunConfig
- Lightweight and fast processing
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass
from enum import Enum
from urllib.parse import urlparse

# Import existing components
from content_type_classifier import (
    ContentTypeClassifier, 
    ContentClassification, 
    ContentType, 
    FilterStrategy, 
    AdaptiveFilterManager
)
from false_positive_filters import (
    FalsePositiveFilterSystem, 
    NavigationLinkFilter, 
    ContentStructureFilter
)

logger = logging.getLogger(__name__)


class FilteringLevel(Enum):
    """Content filtering intensity levels"""
    MINIMAL = "minimal"      # Basic navigation removal only
    STANDARD = "standard"    # Navigation + boilerplate removal
    AGGRESSIVE = "aggressive"  # Maximum filtering for clean content
    ADAPTIVE = "adaptive"    # Use content type to determine level


@dataclass
class FilteringResult:
    """Result of content filtering operation"""
    filtered_content: str
    original_length: int
    filtered_length: int
    reduction_percentage: float
    filters_applied: List[str]
    content_classification: Optional[ContentClassification] = None
    quality_score: float = 0.0
    
    def is_navigation_removed(self) -> bool:
        """Check if navigation removal filter was applied"""
        return "navigation_removal" in self.filters_applied


@dataclass
class NavigationPattern:
    """Navigation pattern configuration"""
    selectors: List[str]
    keywords: Set[str]
    patterns: List[re.Pattern]


class EnhancedContentFilter:
    """
    High-accuracy content filtering system that removes navigation, 
    boilerplate, and low-quality content while preserving valuable information.
    """
    
    def __init__(self, 
                 content_classifier: Optional[ContentTypeClassifier] = None,
                 false_positive_filter: Optional[FalsePositiveFilterSystem] = None):
        self.content_classifier = content_classifier or ContentTypeClassifier()
        self.adaptive_manager = AdaptiveFilterManager(self.content_classifier)
        self.false_positive_filter = false_positive_filter or FalsePositiveFilterSystem()
        self.nav_filter = NavigationLinkFilter()
        self.structure_filter = ContentStructureFilter()
        
        # Navigation patterns for different content types
        self.navigation_patterns = self._initialize_navigation_patterns()
        
        # Content density thresholds
        self.density_thresholds = {
            'min_content_density': 0.3,  # Minimum ratio of content to markup
            'min_paragraph_length': 50,   # Minimum meaningful paragraph length
            'max_nav_ratio': 0.4,        # Maximum navigation/total content ratio
        }
        
        # Boilerplate patterns
        self.boilerplate_patterns = self._initialize_boilerplate_patterns()
        
        # Pre-compiled regex patterns for performance
        self._compiled_patterns = self._compile_patterns()
    
    def _initialize_navigation_patterns(self) -> Dict[str, NavigationPattern]:
        """Initialize navigation patterns for different content types"""
        return {
            'common': NavigationPattern(
                selectors=[
                    'nav', 'header', 'footer', 'sidebar', 'menu',
                    '.navigation', '.nav', '.menu', '.sidebar',
                    '.header', '.footer', '.breadcrumb', '.pagination',
                    '[role="navigation"]', '[role="banner"]', '[role="contentinfo"]'
                ],
                keywords={
                    'home', 'about', 'contact', 'blog', 'news', 'events',
                    'services', 'products', 'portfolio', 'team', 'careers',
                    'privacy', 'terms', 'sitemap', 'login', 'signup', 'register',
                    'dashboard', 'profile', 'settings', 'help', 'support',
                    'documentation', 'docs', 'api', 'download', 'pricing'
                },
                patterns=[
                    re.compile(r'(?:next|previous|back|forward)\s*(?:page|chapter|section)', re.IGNORECASE),
                    re.compile(r'(?:page|go to)\s*\d+', re.IGNORECASE),
                    re.compile(r'(?:skip to|jump to)\s*(?:content|navigation|main)', re.IGNORECASE)
                ]
            ),
            'documentation': NavigationPattern(
                selectors=[
                    '.toc', '.table-of-contents', '.sidebar-nav',
                    '.doc-nav', '.api-nav', '.version-selector'
                ],
                keywords={
                    'contents', 'index', 'reference', 'api', 'guide',
                    'tutorial', 'quickstart', 'getting started', 'examples'
                },
                patterns=[
                    re.compile(r'table\s*of\s*contents', re.IGNORECASE),
                    re.compile(r'(?:api|method|function)\s*reference', re.IGNORECASE)
                ]
            ),
            'tutorial': NavigationPattern(
                selectors=[
                    '.lesson-nav', '.chapter-nav', '.step-nav',
                    '.tutorial-nav', '.course-nav'
                ],
                keywords={
                    'chapter', 'lesson', 'step', 'exercise', 'quiz',
                    'previous lesson', 'next lesson', 'course outline'
                },
                patterns=[
                    re.compile(r'(?:lesson|chapter|step)\s*\d+', re.IGNORECASE),
                    re.compile(r'(?:previous|next)\s*(?:lesson|chapter|step)', re.IGNORECASE)
                ]
            )
        }
    
    def _initialize_boilerplate_patterns(self) -> Dict[str, List[re.Pattern]]:
        """Initialize boilerplate content patterns"""
        return {
            'social_media': [
                re.compile(r'(?:follow us on|connect with us|share on)\s*(?:twitter|facebook|linkedin|instagram)', re.IGNORECASE),
                re.compile(r'(?:like|share|tweet|pin)\s*this\s*(?:post|article|page)', re.IGNORECASE),
                re.compile(r'social\s*media\s*(?:links|icons|buttons)', re.IGNORECASE)
            ],
            'advertisements': [
                re.compile(r'(?:advertisement|sponsored|promoted)\s*(?:content|post|link)', re.IGNORECASE),
                re.compile(r'(?:ads by|powered by|brought to you by)', re.IGNORECASE),
                re.compile(r'(?:click here|learn more|sign up now|buy now|get started)', re.IGNORECASE)
            ],
            'legal_notices': [
                re.compile(r'(?:privacy policy|terms of service|cookie policy|disclaimer)', re.IGNORECASE),
                re.compile(r'(?:copyright|all rights reserved|\u00a9|\u00ae)', re.IGNORECASE),
                re.compile(r'(?:this website uses cookies|we use cookies)', re.IGNORECASE)
            ],
            'contact_info': [
                re.compile(r'(?:contact us|get in touch|reach out)', re.IGNORECASE),
                re.compile(r'(?:phone|email|address):\s*[^\n]+', re.IGNORECASE),
                re.compile(r'(?:business hours|office hours|opening times)', re.IGNORECASE)
            ],
            'newsletter': [
                re.compile(r'(?:subscribe|sign up)\s*(?:for|to)\s*(?:our\s*)?newsletter', re.IGNORECASE),
                re.compile(r'(?:email updates|weekly digest|monthly newsletter)', re.IGNORECASE),
                re.compile(r'enter your email\s*(?:address)?', re.IGNORECASE)
            ]
        }
    
    def _compile_patterns(self) -> Dict[str, re.Pattern]:
        """Pre-compile regex patterns for better performance"""
        patterns = {}
        
        # Navigation detection patterns
        patterns['navigation_lists'] = re.compile(
            r'(?:^|\n)\s*(?:[-*+•]|\d+\.)\s*(?:' + 
            '|'.join(self.navigation_patterns['common'].keywords) + 
            r')\s*(?:\n|$)', 
            re.IGNORECASE | re.MULTILINE
        )
        
        # Boilerplate content patterns
        patterns['repeated_headers'] = re.compile(r'^(#{1,6}\s*.+)(?:\n.*)*?\n\1', re.MULTILINE)
        patterns['empty_sections'] = re.compile(r'#{1,6}\s*[^\n]*\n\s*(?=#{1,6}|\Z)', re.MULTILINE)
        patterns['excessive_whitespace'] = re.compile(r'\n\s*\n\s*\n', re.MULTILINE)
        patterns['url_lists'] = re.compile(r'(?:^|\n)(?:\s*[-*+•]\s*)?https?://[^\s\n]+(?:\s*\n|$)', re.MULTILINE)
        
        # Content quality patterns
        patterns['meaningful_paragraph'] = re.compile(r'[^\n]{50,}', re.MULTILINE)
        patterns['code_blocks'] = re.compile(r'```[\s\S]*?```|`[^`\n]+`')
        patterns['headers'] = re.compile(r'^#{1,6}\s+.+$', re.MULTILINE)
        
        return patterns
    
    def filter_content(self, 
                      content: str, 
                      url: str = "", 
                      filtering_level: FilteringLevel = FilteringLevel.ADAPTIVE,
                      preserve_code: bool = True) -> FilteringResult:
        """
        Apply content filtering to remove navigation, boilerplate, and low-quality content.
        
        Args:
            content: Raw content to filter
            url: URL of the content (for adaptive filtering)
            filtering_level: Intensity of filtering to apply
            preserve_code: Whether to preserve code blocks during filtering
            
        Returns:
            FilteringResult with filtered content and metadata
        """
        if not content or len(content.strip()) < 100:
            return FilteringResult(
                filtered_content=content,
                original_length=len(content),
                filtered_length=len(content),
                reduction_percentage=0.0,
                filters_applied=[],
                quality_score=0.0
            )
        
        original_length = len(content)
        filtered_content = content
        filters_applied = []
        
        # Step 1: Content type classification for adaptive filtering
        classification = None
        if filtering_level == FilteringLevel.ADAPTIVE:
            classification = self.content_classifier.classify_content(content, url)
            filtering_level = self._determine_filtering_level(classification)
        
        # Step 2: Preserve code blocks if requested
        code_blocks = []
        code_placeholders = {}
        if preserve_code:
            filtered_content, code_blocks, code_placeholders = self._extract_and_preserve_code(filtered_content)
            if code_blocks:
                filters_applied.append("code_preservation")
        
        # Step 3: Apply filtering based on level - be more conservative
        if filtering_level == FilteringLevel.AGGRESSIVE:
            # Remove navigation patterns
            filtered_content = self._remove_navigation_patterns(filtered_content, classification)
            filters_applied.append("navigation_removal")
            
            # Remove boilerplate content
            filtered_content = self._remove_boilerplate_content(filtered_content, filtering_level)
            filters_applied.append("boilerplate_removal")
        elif filtering_level == FilteringLevel.STANDARD:
            # Light navigation removal only for standard level
            filtered_content = self._remove_navigation_patterns(filtered_content, classification)
            filters_applied.append("navigation_removal")
        
        if filtering_level == FilteringLevel.AGGRESSIVE:
            # Additional aggressive filtering
            filtered_content = self._apply_aggressive_filtering(filtered_content)
            filters_applied.append("aggressive_filtering")
        
        # Step 4: Content quality improvement
        filtered_content = self._improve_content_quality(filtered_content)
        filters_applied.append("quality_improvement")
        
        # Step 5: Calculate metrics (before restoring code blocks)
        filtered_length_before_code = len(filtered_content)
        
        # Step 6: Restore code blocks AFTER all filtering
        if preserve_code and code_placeholders:
            filtered_content = self._restore_code_blocks(filtered_content, code_placeholders)
        
        # Step 7: Recalculate final metrics
        filtered_length = len(filtered_content)
        reduction_percentage = ((original_length - filtered_length) / original_length * 100) if original_length > 0 else 0
        quality_score = self._calculate_quality_score(filtered_content, classification)
        
        return FilteringResult(
            filtered_content=filtered_content,
            original_length=original_length,
            filtered_length=filtered_length,
            reduction_percentage=reduction_percentage,
            filters_applied=filters_applied,
            content_classification=classification,
            quality_score=quality_score
        )
    
    def _determine_filtering_level(self, classification: ContentClassification) -> FilteringLevel:
        """Determine appropriate filtering level based on content classification"""
        if classification.content_type == ContentType.MARKETING:
            return FilteringLevel.AGGRESSIVE
        elif classification.content_type == ContentType.DOCUMENTATION:
            return FilteringLevel.MINIMAL  # Be very conservative with documentation
        elif classification.content_type == ContentType.TUTORIAL:
            return FilteringLevel.STANDARD  # Moderate filtering for tutorials
        elif classification.content_type == ContentType.GITHUB_REPO:
            return FilteringLevel.MINIMAL
        else:
            return FilteringLevel.STANDARD
    
    def _extract_and_preserve_code(self, content: str) -> Tuple[str, List[str], Dict[str, str]]:
        """Extract and preserve code blocks during filtering"""
        code_blocks = []
        code_placeholders = {}
        
        # Find all code blocks
        for match in self._compiled_patterns['code_blocks'].finditer(content):
            code_block = match.group()
            placeholder = f"__CODE_BLOCK_{len(code_blocks)}__"
            code_blocks.append(code_block)
            code_placeholders[placeholder] = code_block
        
        # Replace code blocks with placeholders
        def replace_with_placeholder(match):
            code_block = match.group()
            try:
                index = code_blocks.index(code_block)
                return f"__CODE_BLOCK_{index}__"
            except ValueError:
                # Code block not found in list, add it
                code_blocks.append(code_block)
                placeholder = f"__CODE_BLOCK_{len(code_blocks)-1}__"
                code_placeholders[placeholder] = code_block
                return placeholder
        
        filtered_content = self._compiled_patterns['code_blocks'].sub(
            replace_with_placeholder, 
            content
        )
        
        return filtered_content, code_blocks, code_placeholders
    
    def _remove_navigation_patterns(self, content: str, classification: Optional[ContentClassification]) -> str:
        """Remove navigation patterns based on content type"""
        # Determine which navigation patterns to use
        patterns_to_use = ['common']
        if classification:
            if classification.content_type == ContentType.DOCUMENTATION:
                patterns_to_use.append('documentation')
            elif classification.content_type == ContentType.TUTORIAL:
                patterns_to_use.append('tutorial')
        
        # Apply navigation removal
        for pattern_type in patterns_to_use:
            if pattern_type in self.navigation_patterns:
                pattern = self.navigation_patterns[pattern_type]
                
                # Remove navigation keyword patterns
                for nav_pattern in pattern.patterns:
                    content = nav_pattern.sub('', content)
                
                # Remove navigation lists
                content = self._remove_navigation_lists(content, pattern.keywords)
        
        # Remove detected navigation using existing filters
        is_nav, _ = self.nav_filter.is_navigation_content(content)
        if is_nav:
            # If entire content is navigation, return minimal placeholder
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            if len(lines) <= 10:  # Small navigation block
                return ""
        
        return content
    
    def _remove_navigation_lists(self, content: str, nav_keywords: Set[str]) -> str:
        """Remove lists that appear to be navigation"""
        lines = content.split('\n')
        filtered_lines = []
        in_nav_list = False
        nav_list_items = 0
        
        for line in lines:
            stripped = line.strip()
            
            # Don't filter headers - they might be important section titles
            if re.match(r'^#{1,6}\s+', stripped):
                filtered_lines.append(line)
                in_nav_list = False
                continue
            
            # Check if this line starts a navigation list
            if re.match(r'^\s*[-*+•]\s+', line) or re.match(r'^\s*\d+\.\s+', line):
                # Extract the text after the bullet/number
                list_text = re.sub(r'^\s*(?:[-*+•]|\d+\.)\s+', '', stripped).lower()
                
                # Check if this contains navigation keywords (need multiple matches)
                nav_keyword_count = sum(1 for keyword in nav_keywords if keyword in list_text)
                if nav_keyword_count > 0 and len(list_text) < 50:  # Short navigation-like items
                    in_nav_list = True
                    nav_list_items = 1
                    continue
                elif in_nav_list and nav_list_items < 10:  # Limit nav list removal
                    nav_list_items += 1
                    continue
                else:
                    # This might be a content list, not navigation
                    in_nav_list = False
                    filtered_lines.append(line)
            elif in_nav_list:
                # Check if we're still in a navigation list
                if stripped == "":
                    # Empty line might continue the list
                    continue
                elif re.match(r'^\s*[-*+•]\s+', line) or re.match(r'^\s*\d+\.\s+', line):
                    # Another list item
                    nav_list_items += 1
                    if nav_list_items > 10:  # Too long to be simple navigation
                        in_nav_list = False
                        filtered_lines.append(line)
                    continue
                else:
                    # End of navigation list
                    in_nav_list = False
                    nav_list_items = 0
                    filtered_lines.append(line)
            else:
                # Keep non-navigation lines
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def _remove_boilerplate_content(self, content: str, filtering_level: FilteringLevel) -> str:
        """Remove boilerplate content like social media, ads, legal notices"""
        for category, patterns in self.boilerplate_patterns.items():
            for pattern in patterns:
                if filtering_level == FilteringLevel.AGGRESSIVE or category in ['social_media', 'advertisements']:
                    content = pattern.sub('', content)
        
        # Remove repetitive sections
        content = self._compiled_patterns['repeated_headers'].sub(r'\1', content)
        content = self._compiled_patterns['empty_sections'].sub('', content)
        
        # Remove URL lists that are likely navigation
        if filtering_level == FilteringLevel.AGGRESSIVE:
            content = self._compiled_patterns['url_lists'].sub('', content)
        
        return content
    
    def _apply_aggressive_filtering(self, content: str) -> str:
        """Apply aggressive filtering for maximum content quality"""
        lines = content.split('\n')
        filtered_lines = []
        
        for line in lines:
            stripped = line.strip()
            
            # Preserve code block placeholders
            if stripped.startswith('__CODE_BLOCK_') and stripped.endswith('__'):
                filtered_lines.append(line)
                continue
            
            # Skip very short lines that are likely navigation or boilerplate
            if len(stripped) < 10:
                continue
            
            # Skip lines that are mostly punctuation or special characters
            if len(re.sub(r'[^\w\s]', '', stripped)) < len(stripped) * 0.5:
                continue
            
            # Skip lines with high ratio of capital letters (likely headings/navigation)
            if len(re.findall(r'[A-Z]', stripped)) > len(stripped) * 0.5:
                continue
            
            filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def _improve_content_quality(self, content: str) -> str:
        """Improve content quality by removing excessive whitespace and empty sections"""
        # Remove excessive whitespace
        content = self._compiled_patterns['excessive_whitespace'].sub('\n\n', content)
        
        # Remove lines with only whitespace
        lines = [line.rstrip() for line in content.split('\n')]
        
        # Remove empty sections (headers with no content) - but be more conservative
        filtered_lines = []
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # If this is a header
            if re.match(r'^#{1,6}\s+', line):
                # Look ahead to see if there's meaningful content
                j = i + 1
                has_content = False
                content_lines = 0
                while j < len(lines) and not re.match(r'^#{1,6}\s+', lines[j].strip()):
                    if lines[j].strip():
                        content_lines += 1
                        if len(lines[j].strip()) > 10:  # Lower threshold for meaningful content
                            has_content = True
                            break
                    j += 1
                
                # Keep header if it has content OR if it's a major header (fewer #)
                header_level = len(line.split()[0])  # Count # characters
                if has_content or header_level <= 2 or content_lines > 0:
                    filtered_lines.append(lines[i])
                i += 1
            else:
                filtered_lines.append(lines[i])
                i += 1
        
        return '\n'.join(filtered_lines).strip()
    
    def _restore_code_blocks(self, content: str, code_placeholders: Dict[str, str]) -> str:
        """Restore preserved code blocks"""
        for placeholder, code_block in code_placeholders.items():
            content = content.replace(placeholder, code_block)
        return content
    
    def _calculate_quality_score(self, content: str, classification: Optional[ContentClassification]) -> float:
        """Calculate a quality score for the filtered content"""
        if not content:
            return 0.0
        
        score = 0.0
        
        # Content length score (normalized)
        length_score = min(len(content) / 5000, 1.0) * 0.2
        score += length_score
        
        # Meaningful paragraph ratio
        meaningful_paragraphs = len(self._compiled_patterns['meaningful_paragraph'].findall(content))
        total_lines = len([line for line in content.split('\n') if line.strip()])
        if total_lines > 0:
            paragraph_ratio = min(meaningful_paragraphs / total_lines, 1.0) * 0.3
            score += paragraph_ratio
        
        # Code content ratio (if applicable)
        code_blocks = len(self._compiled_patterns['code_blocks'].findall(content))
        if code_blocks > 0:
            code_score = min(code_blocks / 10, 1.0) * 0.2
            score += code_score
        
        # Header structure score
        headers = len(self._compiled_patterns['headers'].findall(content))
        if headers > 0 and total_lines > 0:
            header_ratio = headers / total_lines
            if 0.1 <= header_ratio <= 0.3:  # Good header ratio
                score += 0.2
            else:
                score += 0.1
        
        # Content type bonus
        if classification:
            if classification.confidence_score > 0.7:
                score += 0.1
        
        return min(score, 1.0)
    
    def get_enhanced_crawler_config(self, 
                                   base_config: Optional[Dict[str, Any]] = None,
                                   filtering_level: FilteringLevel = FilteringLevel.STANDARD,
                                   content_type: Optional[ContentType] = None) -> Dict[str, Any]:
        """
        Get enhanced CrawlerRunConfig parameters for better content extraction.
        
        This integrates with Crawl4AI's configuration to improve content quality
        at the crawling stage, reducing the need for post-processing.
        """
        config = base_config or {}
        
        # Base extraction parameters for better content quality
        enhanced_params = {
            # CSS selectors to remove during crawling - CONSERVATIVE approach
            'excluded_tags': ['script', 'style', 'noscript'],  # Only exclude non-content elements
            
            # Additional CSS selectors to exclude based on common patterns
            'css_selector_to_exclude': self._get_exclusion_selectors(content_type, filtering_level),
            
            # Word count threshold for meaningful content - LOWERED to preserve content
            'word_count_threshold': 10 if filtering_level == FilteringLevel.AGGRESSIVE else 5,
            
            # Remove navigation and boilerplate during extraction
            'remove_overlay_elements': True,
            
            # Enhanced text extraction
            'only_text': False,  # Keep some markup for better parsing
            
            # Image and media handling
            'exclude_external_images': True,
            'exclude_social_media_links': True,
        }
        
        # Content type specific configurations - MINIMAL additions for documentation
        if content_type == ContentType.DOCUMENTATION:
            # Don't add extra selectors for documentation - preserve all content
            enhanced_params.update({
                'word_count_threshold': 3,  # Very low threshold for documentation
            })
        elif content_type == ContentType.TUTORIAL:
            # Minimal filtering for tutorials
            enhanced_params.update({
                'word_count_threshold': 3,  # Very low threshold for tutorials
            })
        elif content_type == ContentType.MARKETING:
            enhanced_params.update({
                'css_selector_to_exclude': enhanced_params['css_selector_to_exclude'] + [
                    '.cta', '.call-to-action', '.pricing-table', '.testimonial-section'
                ],
                'word_count_threshold': 20,  # Still low but higher for marketing
            })
        
        # Merge with base config
        config.update(enhanced_params)
        return config
    
    def _get_exclusion_selectors(self, 
                               content_type: Optional[ContentType], 
                               filtering_level: FilteringLevel) -> List[str]:
        """Get CSS selectors to exclude during crawling - CONSERVATIVE approach to preserve main content"""
        
        # MINIMAL filtering for documentation to preserve main content
        if content_type in [ContentType.DOCUMENTATION, ContentType.TUTORIAL] or filtering_level == FilteringLevel.MINIMAL:
            return [
                # Only exclude clearly non-content elements
                '.advertisement', '.ad-banner', '.popup', '.modal',
                '.social-share-buttons', '.cookie-banner'
            ]
        
        # CONSERVATIVE base selectors - only exclude obvious navigation/boilerplate
        base_selectors = [
            # Navigation - but avoid overly broad selectors
            '.top-nav', '.main-nav', '.site-nav', '.primary-nav',
            '.breadcrumbs', '.pagination-nav',
            '[role="navigation"][aria-label*="main"]',
            
            # Clear footer/header elements (but not all headers/footers)
            '.site-header', '.page-header', '.site-footer', '.page-footer',
            '[role="banner"]', '[role="contentinfo"]',
            
            # Social media - specific selectors only
            '.social-media-links', '.share-buttons', '.social-sharing',
            '.facebook-like', '.twitter-share', '.linkedin-share',
            
            # Clear advertisements
            '.advertisement', '.ad-banner', '.sponsored-content',
            '.google-ads', '.banner-ad',
            
            # Newsletter/signup - specific selectors only
            '.newsletter-signup', '.email-signup', '.subscribe-form'
        ]
        
        # AGGRESSIVE mode - add more selectors but still preserve main content areas
        if filtering_level == FilteringLevel.AGGRESSIVE:
            base_selectors.extend([
                '.related-articles', '.recommended-reading',
                '.comment-section', '.user-comments',
                '.author-bio', '.article-metadata',
                '.tag-cloud', '.category-links'
            ])
        
        return base_selectors
    
    def get_filter_statistics(self) -> Dict[str, Any]:
        """Get performance statistics for the filtering system"""
        return {
            'navigation_patterns_count': sum(len(p.keywords) for p in self.navigation_patterns.values()),
            'boilerplate_patterns_count': sum(len(patterns) for patterns in self.boilerplate_patterns.values()),
            'compiled_patterns_count': len(self._compiled_patterns),
            'supported_content_types': [ct.value for ct in ContentType],
            'filtering_levels': [level.value for level in FilteringLevel]
        }


# Integration functions for backward compatibility and easy adoption

def create_enhanced_crawler_config(url: str = "",
                                 filtering_level: FilteringLevel = FilteringLevel.STANDARD,
                                 preserve_code: bool = True) -> Dict[str, Any]:
    """
    Create an enhanced CrawlerRunConfig with content filtering parameters.
    
    This is a convenience function for easy integration with existing code.
    """
    filter_system = EnhancedContentFilter()
    
    # Classify content type if URL is provided
    content_type = None
    if url:
        # Simple URL-based content type detection
        parsed = urlparse(url.lower())
        domain = parsed.netloc
        path = parsed.path
        
        if any(indicator in domain + path for indicator in ['docs', 'documentation', 'api']):
            content_type = ContentType.DOCUMENTATION
        elif any(indicator in domain + path for indicator in ['tutorial', 'learn', 'guide']):
            content_type = ContentType.TUTORIAL
        elif 'github.com' in domain:
            content_type = ContentType.GITHUB_REPO
    
    return filter_system.get_enhanced_crawler_config(
        filtering_level=filtering_level,
        content_type=content_type
    )


def filter_crawled_content(content: str,
                          url: str = "",
                          filtering_level: FilteringLevel = FilteringLevel.ADAPTIVE,
                          preserve_code: bool = True) -> FilteringResult:
    """
    Apply enhanced content filtering to crawled content.
    
    This is a convenience function for easy integration with existing code.
    """
    filter_system = EnhancedContentFilter()
    return filter_system.filter_content(
        content=content,
        url=url,
        filtering_level=filtering_level,
        preserve_code=preserve_code
    )


# Export main classes and functions
__all__ = [
    'EnhancedContentFilter',
    'FilteringLevel',
    'FilteringResult',
    'NavigationPattern',
    'create_enhanced_crawler_config',
    'filter_crawled_content'
]