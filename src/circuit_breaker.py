"""
Simple circuit breaker implementation for service resilience.
Follows KISS principle - basic but effective pattern.
"""
import time
import logging
from typing import Any, Callable, Optional
from enum import Enum

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class SimpleCircuitBreaker:
    """Simple circuit breaker for preventing cascading failures."""
    
    def __init__(
        self, 
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        success_threshold: int = 3
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        
    def _reset(self):
        """Reset circuit breaker to closed state."""
        self.failure_count = 0
        self.success_count = 0
        self.state = CircuitState.CLOSED
        logger.info("Circuit breaker reset to CLOSED")
    
    def _record_success(self):
        """Record successful operation."""
        self.failure_count = 0
        
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.success_threshold:
                self._reset()
                logger.info("Circuit breaker recovered")
    
    def _record_failure(self):
        """Record failed operation."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        self.success_count = 0
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker OPEN after {self.failure_count} failures")
    
    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset from OPEN to HALF_OPEN."""
        if self.state == CircuitState.OPEN and self.last_failure_time:
            return time.time() - self.last_failure_time >= self.recovery_timeout
        return False
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        
        # Check if we should attempt recovery
        if self._should_attempt_reset():
            self.state = CircuitState.HALF_OPEN
            logger.info("Circuit breaker moved to HALF_OPEN for testing")
        
        # Reject if circuit is open
        if self.state == CircuitState.OPEN:
            raise Exception(f"Circuit breaker is OPEN. Service unavailable.")
        
        try:
            # Execute the function
            result = await func(*args, **kwargs)
            self._record_success()
            return result
            
        except Exception as e:
            self._record_failure()
            logger.error(f"Circuit breaker recorded failure: {e}")
            raise
    
    def can_execute(self) -> bool:
        """Check if circuit allows execution."""
        if self.state == CircuitState.CLOSED or self.state == CircuitState.HALF_OPEN:
            return True
        elif self.state == CircuitState.OPEN:
            return self._should_attempt_reset()
        return False
    
    def record_success(self):
        """Record a successful operation."""
        self._record_success()
    
    def record_failure(self):
        """Record a failed operation."""
        self._record_failure()
    
    def get_state(self) -> dict:
        """Get current circuit breaker state."""
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time,
            "next_attempt_time": (
                self.last_failure_time + self.recovery_timeout 
                if self.last_failure_time and self.state == CircuitState.OPEN 
                else None
            )
        }


# Global circuit breakers for different services
_circuit_breakers = {}

# Export CircuitBreaker alias for backward compatibility
CircuitBreaker = SimpleCircuitBreaker


def get_circuit_breaker(service_name: str, **kwargs) -> SimpleCircuitBreaker:
    """Get or create circuit breaker for a service."""
    if service_name not in _circuit_breakers:
        _circuit_breakers[service_name] = SimpleCircuitBreaker(**kwargs)
    return _circuit_breakers[service_name]