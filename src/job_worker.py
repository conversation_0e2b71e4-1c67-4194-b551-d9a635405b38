"""
Background Job Worker for AY RAG MCP Server
Processes crawling jobs asynchronously from Redis queue
"""

import asyncio
import logging
import signal
import traceback
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

try:
    # Try relative import first (when imported as module)
    from .job_queue import RedisJobQueue, JobRequest, JobStatus, get_job_queue
    from .ay_rag_mcp import smart_chunk_markdown, extract_section_info
    from .utils import create_embedding, process_crawl_results_to_supabase
except ImportError:
    # Fall back to direct import (when run standalone or from tests)
    from job_queue import RedisJobQueue, JobRequest, JobStatus, get_job_queue
    from ay_rag_mcp import smart_chunk_markdown, extract_section_info
    from utils import create_embedding, process_crawl_results_to_supabase

logger = logging.getLogger(__name__)

class CrawlJobWorker:
    """Background worker for processing crawl jobs"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 worker_id: str = "worker-1", concurrency: int = 3):
        self.redis_url = redis_url
        self.worker_id = worker_id
        self.concurrency = concurrency
        self.job_queue: Optional[RedisJobQueue] = None
        self.running = False
        self.worker_tasks = []
        
        # Import crawling components (lazy import to avoid circular deps)
        self._crawler = None
        self._supabase = None
    
    async def initialize(self):
        """Initialize worker components"""
        # Create worker-specific job queue using the configured Redis URL
        self.job_queue = RedisJobQueue(self.redis_url)
        await self.job_queue.connect()
        
        # Initialize crawler and supabase (import from main module)
        try:
            from crawl4ai import AsyncWebCrawler
            from crawl4ai.extraction_strategy import LLMExtractionStrategy
            from crawl4ai.chunking_strategy import RegexChunking, NlpSentenceChunking
            from crawl4ai import CrawlerRunConfig, CacheMode
            
            from supabase import create_client, Client
            import os
            
            # Initialize Supabase
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
            if supabase_url and supabase_key:
                self._supabase = create_client(supabase_url, supabase_key)
            else:
                raise RuntimeError("Supabase credentials not configured")
            
            logger.info(f"Worker {self.worker_id} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize worker {self.worker_id}: {e}")
            raise
    
    async def start(self):
        """Start worker processing"""
        if self.running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return
        
        self.running = True
        logger.info(f"Starting worker {self.worker_id} with {self.concurrency} concurrent tasks")
        
        # Start worker tasks
        self.worker_tasks = [
            asyncio.create_task(self._worker_loop(i))
            for i in range(self.concurrency)
        ]
        
        # Start cleanup task
        cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.worker_tasks.append(cleanup_task)
        
        # Wait for all tasks
        try:
            await asyncio.gather(*self.worker_tasks)
        except asyncio.CancelledError:
            logger.info(f"Worker {self.worker_id} tasks cancelled")
        except Exception as e:
            logger.error(f"Worker {self.worker_id} error: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown worker"""
        if not self.running:
            return
        
        self.running = False
        logger.info(f"Shutting down worker {self.worker_id}")
        
        # Cancel all worker tasks
        for task in self.worker_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        # Clean up resources
        if self.job_queue:
            await self.job_queue.disconnect()
        
        logger.info(f"Worker {self.worker_id} shutdown complete")
    
    async def _worker_loop(self, worker_num: int):
        """Main worker loop for processing jobs"""
        logger.info(f"Starting worker loop {worker_num} for {self.worker_id}")
        
        while self.running:
            try:
                # Get next job from queue
                job_request = await self.job_queue.dequeue_job()
                
                if job_request is None:
                    # No jobs available, wait before checking again
                    await asyncio.sleep(1)
                    continue
                
                logger.info(f"Worker {worker_num} processing job {job_request.job_id}")
                
                # Process the job
                await self._process_job(job_request)
                
            except asyncio.CancelledError:
                logger.info(f"Worker loop {worker_num} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker loop {worker_num} error: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _process_job(self, job_request: JobRequest):
        """Process a single crawl job"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Update job status to processing
            await self.job_queue.update_job_status(job_request.job_id, JobStatus.PROCESSING)
            
            # Route to appropriate handler based on job type
            if job_request.job_type == "smart_crawl":
                result = await self._process_smart_crawl(job_request)
            elif job_request.job_type == "single_crawl":
                result = await self._process_single_crawl(job_request)
            else:
                raise ValueError(f"Unknown job type: {job_request.job_type}")
            
            # Mark job as completed
            await self.job_queue.update_job_status(
                job_request.job_id, 
                JobStatus.COMPLETED, 
                result_data=result
            )
            
            duration = asyncio.get_event_loop().time() - start_time
            logger.info(f"Job {job_request.job_id} completed in {duration:.2f}s")
            
        except Exception as e:
            error_msg = f"Job processing failed: {str(e)}"
            logger.error(f"Job {job_request.job_id} failed: {error_msg}")
            logger.error(traceback.format_exc())
            
            # Mark job as failed
            await self.job_queue.update_job_status(
                job_request.job_id,
                JobStatus.FAILED,
                error_message=error_msg
            )
    
    async def _process_smart_crawl(self, job_request: JobRequest) -> Dict[str, Any]:
        """Process smart crawl job"""
        from crawl4ai import AsyncWebCrawler
        from crawl4ai import CrawlerRunConfig, CacheMode
        
        url = job_request.url
        params = job_request.parameters
        
        # Extract parameters
        max_depth = params.get("max_depth", 3)
        max_concurrent = params.get("max_concurrent", 10)
        chunk_size = params.get("chunk_size", 5000)
        
        logger.info(f"Smart crawling {url} with depth={max_depth}, concurrent={max_concurrent}")
        
        # Detect URL type and process accordingly
        total_pages = 0
        total_chunks = 0
        crawled_sources = set()
        
        try:
            # Create crawler configuration
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                stream=False,
                word_count_threshold=10,
                excluded_tags=['script', 'style', 'nav', 'footer', 'aside'],
                remove_overlay_elements=True,
                js_code="window.scrollTo(0, document.body.scrollHeight);"
            )
            
            async with AsyncWebCrawler(verbose=False) as crawler:
                # Determine crawl strategy based on URL
                if url.endswith('/sitemap.xml') or 'sitemap' in url.lower():
                    urls_to_crawl = await self._extract_sitemap_urls(url, crawler)
                elif url.endswith('.txt'):
                    urls_to_crawl = await self._extract_txt_urls(url, crawler)
                else:
                    # Regular page - just crawl this URL
                    urls_to_crawl = [url]
                
                # Limit URLs if too many
                if len(urls_to_crawl) > 100:
                    urls_to_crawl = urls_to_crawl[:100]
                    logger.warning(f"Limited crawl to first 100 URLs from {len(urls_to_crawl)} found")
                
                # Process URLs with concurrency control
                semaphore = asyncio.Semaphore(max_concurrent)
                tasks = []
                
                for target_url in urls_to_crawl:
                    task = asyncio.create_task(
                        self._crawl_single_url_with_semaphore(
                            target_url, crawler, run_config, chunk_size, semaphore
                        )
                    )
                    tasks.append(task)
                
                # Wait for all crawls to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Failed to crawl {urls_to_crawl[i]}: {result}")
                        continue
                    
                    if result:
                        chunks_count, source = result
                        total_chunks += chunks_count
                        crawled_sources.add(source)
                        total_pages += 1
        
        except Exception as e:
            logger.error(f"Smart crawl error for {url}: {e}")
            raise
        
        return {
            "success": True,
            "url": url,
            "total_pages_crawled": total_pages,
            "total_chunks_stored": total_chunks,
            "sources_crawled": list(crawled_sources),
            "crawl_type": "smart_crawl"
        }
    
    async def _process_single_crawl(self, job_request: JobRequest) -> Dict[str, Any]:
        """Process single page crawl job"""
        from crawl4ai import AsyncWebCrawler
        from crawl4ai import CrawlerRunConfig, CacheMode
        
        url = job_request.url
        params = job_request.parameters
        chunk_size = params.get("chunk_size", 5000)
        
        logger.info(f"Single page crawling {url}")
        
        try:
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                stream=False,
                word_count_threshold=10,
                excluded_tags=['script', 'style', 'nav', 'footer', 'aside'],
                remove_overlay_elements=True
            )
            
            async with AsyncWebCrawler(verbose=False) as crawler:
                result = await crawler.arun(url=url, config=run_config)
                
                if not result.success:
                    raise Exception(f"Crawl failed: {result.error_message}")
                
                # Process content
                chunks_count, source = await self._process_crawled_content(
                    result.markdown, url, chunk_size
                )
                
                return {
                    "success": True,
                    "url": url,
                    "pages_crawled": 1,
                    "chunks_stored": chunks_count,
                    "source": source,
                    "crawl_type": "single_crawl",
                    "content_length": len(result.markdown)
                }
        
        except Exception as e:
            logger.error(f"Single crawl error for {url}: {e}")
            raise
    
    async def _crawl_single_url_with_semaphore(self, url: str, crawler, run_config, 
                                             chunk_size: int, semaphore) -> Optional[tuple]:
        """Crawl single URL with semaphore for concurrency control"""
        async with semaphore:
            try:
                result = await crawler.arun(url=url, config=run_config)
                
                if not result.success or not result.markdown or len(result.markdown.strip()) < 100:
                    logger.warning(f"No content extracted from {url}")
                    return None
                
                chunks_count, source = await self._process_crawled_content(
                    result.markdown, url, chunk_size
                )
                
                return chunks_count, source
                
            except Exception as e:
                logger.error(f"Failed to crawl {url}: {e}")
                return None
    
    async def _process_crawled_content(self, markdown_content: str, url: str, 
                                     chunk_size: int) -> tuple:
        """Process crawled content into chunks and store in database"""
        try:
            from urllib.parse import urlparse
            import asyncio
            
            # Parse URL to get source information
            parsed_url = urlparse(url)
            source_id = parsed_url.netloc
            
            # Create chunks from markdown using the correct function
            chunk_texts = smart_chunk_markdown(markdown_content, chunk_size)
            
            if not chunk_texts:
                logger.warning(f"No chunks created from {url}")
                return 0, None
            
            # Process chunks and prepare data for Supabase
            chunks_data = []
            
            for i, chunk_text in enumerate(chunk_texts):
                try:
                    # Create embedding for chunk
                    embedding = create_embedding(chunk_text)
                    
                    # Extract section info
                    section_info = extract_section_info(chunk_text)
                    
                    chunk_data = {
                        'content': chunk_text,
                        'url': url,
                        'source_id': source_id,
                        'title': section_info.get('title', ''),
                        'section_header': section_info.get('section_header', ''),
                        'word_count': section_info.get('word_count', len(chunk_text.split())),
                        'embedding': embedding,
                        'chunk_index': i
                    }
                    chunks_data.append(chunk_data)
                    
                except Exception as e:
                    logger.error(f"Failed to process chunk {i} from {url}: {e}")
                    continue
            
            # Use the process_crawl_results_to_supabase function to insert
            success_count = await process_crawl_results_to_supabase(
                chunks_data, 
                source_id, 
                self._supabase
            )
            
            logger.info(f"Processed {url}: {len(chunk_texts)} chunks, {success_count} stored successfully")
            return success_count, source_id
            
        except Exception as e:
            logger.error(f"Failed to process content from {url}: {e}")
            raise
    
    async def _extract_sitemap_urls(self, sitemap_url: str, crawler) -> list:
        """Extract URLs from sitemap"""
        try:
            from crawl4ai import CrawlerRunConfig, CacheMode
            
            config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
            result = await crawler.arun(url=sitemap_url, config=config)
            
            if not result.success:
                logger.error(f"Failed to fetch sitemap: {result.error_message}")
                return []
            
            # Parse sitemap XML to extract URLs
            import re
            url_pattern = r'<loc>(https?://[^<]+)</loc>'
            urls = re.findall(url_pattern, result.html)
            
            logger.info(f"Found {len(urls)} URLs in sitemap {sitemap_url}")
            return urls
            
        except Exception as e:
            logger.error(f"Failed to extract sitemap URLs from {sitemap_url}: {e}")
            return []
    
    async def _extract_txt_urls(self, txt_url: str, crawler) -> list:
        """Extract URLs from text file"""
        try:
            from crawl4ai import CrawlerRunConfig, CacheMode
            
            config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
            result = await crawler.arun(url=txt_url, config=config)
            
            if not result.success:
                logger.error(f"Failed to fetch text file: {result.error_message}")
                return []
            
            # Extract URLs from text content
            import re
            url_pattern = r'https?://[^\s\n]+'
            urls = re.findall(url_pattern, result.markdown)
            
            logger.info(f"Found {len(urls)} URLs in text file {txt_url}")
            return urls
            
        except Exception as e:
            logger.error(f"Failed to extract URLs from text file {txt_url}: {e}")
            return []
    
    async def _cleanup_loop(self):
        """Periodic cleanup of expired jobs"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                if self.job_queue:
                    cleaned = await self.job_queue.cleanup_expired_jobs()
                    if cleaned > 0:
                        logger.info(f"Cleaned up {cleaned} expired jobs")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup error: {e}")

async def start_worker(redis_url: str = "redis://localhost:6379", 
                      worker_id: str = "worker-1", concurrency: int = 3):
    """Start a crawl job worker"""
    worker = CrawlJobWorker(redis_url, worker_id, concurrency)
    
    # Handle shutdown signals
    def signal_handler():
        logger.info(f"Received shutdown signal for worker {worker_id}")
        asyncio.create_task(worker.shutdown())
    
    # Register signal handlers
    for sig in [signal.SIGINT, signal.SIGTERM]:
        signal.signal(sig, lambda s, f: signal_handler())
    
    try:
        await worker.initialize()
        await worker.start()
    except Exception as e:
        logger.error(f"Worker {worker_id} failed: {e}")
        raise
    finally:
        await worker.shutdown()

if __name__ == "__main__":
    import os
    import sys
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Get configuration from environment
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    worker_id = os.getenv("WORKER_ID", "worker-1")
    concurrency = int(os.getenv("WORKER_CONCURRENCY", "3"))
    
    # Start worker
    try:
        asyncio.run(start_worker(redis_url, worker_id, concurrency))
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Worker failed: {e}")
        sys.exit(1)