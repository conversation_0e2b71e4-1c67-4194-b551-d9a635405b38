"""
Advanced hybrid scoring system for RAG queries.
Implements normalized scoring strategies from ref-rag-project.
"""
import math
import logging
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ScoringConfig:
    """Configuration for hybrid scoring system."""
    vector_weight: float = 0.5
    keyword_weight: float = 0.3
    rerank_weight: float = 0.2
    dual_match_boost: float = 1.2
    use_sigmoid_normalization: bool = True
    rerank_score_threshold: float = -2.0  # Filter out very low rerank scores
    
class HybridScorer:
    """
    Advanced scoring system that combines vector similarity, keyword matching,
    and cross-encoder reranking with proper normalization.
    
    Based on ref-rag-project strategies with additional optimizations.
    """
    
    def __init__(self, config: Optional[ScoringConfig] = None):
        self.config = config or ScoringConfig()
        
    def normalize_rerank_score(self, rerank_score: float) -> float:
        """
        Normalize rerank scores using sigmoid function.
        Converts cross-encoder scores from (-∞, +∞) to (0, 1) range.
        
        Args:
            rerank_score: Raw cross-encoder score
            
        Returns:
            Normalized score between 0 and 1
        """
        if not self.config.use_sigmoid_normalization:
            # Simple linear mapping (clip to reasonable range)
            return max(0, min(1, (rerank_score + 2) / 4))
        
        # Sigmoid normalization: 1 / (1 + e^(-x))
        try:
            return 1 / (1 + math.exp(-rerank_score))
        except OverflowError:
            # Handle extreme values
            return 0.0 if rerank_score < 0 else 1.0
    
    def combine_scores(
        self,
        vector_similarity: float,
        keyword_match: bool,
        rerank_score: Optional[float] = None,
        appears_in_both: bool = False
    ) -> float:
        """
        Combine multiple scoring signals into a final relevance score.
        
        Args:
            vector_similarity: Semantic similarity score (0-1)
            keyword_match: Whether text contains keyword match
            rerank_score: Optional cross-encoder score
            appears_in_both: Whether result appears in both vector and keyword search
            
        Returns:
            Combined relevance score (0-1)
        """
        # Start with vector similarity
        combined_score = vector_similarity
        
        # Apply dual-match boost if appears in both searches
        if appears_in_both:
            combined_score = min(1.0, combined_score * self.config.dual_match_boost)
        
        # Convert keyword match to numeric score
        keyword_score = 1.0 if keyword_match else 0.0
        
        # Combine scores based on weights
        if rerank_score is not None and rerank_score >= self.config.rerank_score_threshold:
            # Include reranking in the weighted combination
            normalized_rerank = self.normalize_rerank_score(rerank_score)
            
            final_score = (
                self.config.vector_weight * combined_score +
                self.config.keyword_weight * keyword_score +
                self.config.rerank_weight * normalized_rerank
            )
        else:
            # No reranking - redistribute weights
            vector_weight_adjusted = self.config.vector_weight / (
                self.config.vector_weight + self.config.keyword_weight
            )
            keyword_weight_adjusted = self.config.keyword_weight / (
                self.config.vector_weight + self.config.keyword_weight
            )
            
            final_score = (
                vector_weight_adjusted * combined_score +
                keyword_weight_adjusted * keyword_score
            )
        
        return max(0.0, min(1.0, final_score))
    
    def merge_hybrid_results(
        self,
        vector_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        match_count: int,
        content_key: str = "content"
    ) -> List[Dict[str, Any]]:
        """
        Intelligently merge vector and keyword search results.
        Implements ref-rag-project merging strategy with optimizations.
        
        Args:
            vector_results: Results from vector similarity search
            keyword_results: Results from keyword search
            match_count: Maximum number of results to return
            content_key: Key containing the text content for matching
            
        Returns:
            Merged and sorted results with enhanced scoring
        """
        seen_ids: Set[str] = set()
        combined_results: List[Dict[str, Any]] = []
        
        # Create lookup sets for efficient matching
        vector_ids = {r.get('id') for r in vector_results if r.get('id')}
        keyword_ids = {r.get('id') for r in keyword_results if r.get('id')}
        
        # Phase 1: Add items that appear in both searches (highest priority)
        dual_matches = []
        for vr in vector_results:
            result_id = vr.get('id')
            if result_id and result_id in keyword_ids and result_id not in seen_ids:
                # Find corresponding keyword result
                keyword_result = next(
                    (kr for kr in keyword_results if kr.get('id') == result_id),
                    None
                )
                
                if keyword_result:
                    # Calculate enhanced score for dual matches
                    # Ensure similarity is a float, not a string
                    similarity = vr.get('similarity', 0)
                    if isinstance(similarity, str):
                        try:
                            similarity = float(similarity)
                        except (ValueError, TypeError):
                            similarity = 0.0
                    elif not isinstance(similarity, (int, float)):
                        similarity = 0.0
                    
                    # Ensure rerank_score is properly typed
                    rerank_score = vr.get('rerank_score')
                    if rerank_score is not None:
                        if isinstance(rerank_score, str):
                            try:
                                rerank_score = float(rerank_score)
                            except (ValueError, TypeError):
                                rerank_score = None
                        elif not isinstance(rerank_score, (int, float)):
                            rerank_score = None
                    
                    enhanced_score = self.combine_scores(
                        vector_similarity=similarity,
                        keyword_match=True,
                        rerank_score=rerank_score,
                        appears_in_both=True
                    )
                    
                    result = vr.copy()
                    result['final_score'] = enhanced_score
                    result['match_type'] = 'hybrid'
                    result['appears_in_both'] = True
                    
                    dual_matches.append(result)
                    seen_ids.add(result_id)
        
        # Sort dual matches by enhanced score
        dual_matches.sort(key=lambda x: x.get('final_score', 0), reverse=True)
        combined_results.extend(dual_matches)
        
        # Phase 2: Add remaining vector results (semantic relevance)
        vector_only = []
        for vr in vector_results:
            result_id = vr.get('id')
            if result_id and result_id not in seen_ids and len(combined_results) < match_count:
                # Ensure similarity is a float, not a string
                similarity = vr.get('similarity', 0)
                if isinstance(similarity, str):
                    try:
                        similarity = float(similarity)
                    except (ValueError, TypeError):
                        similarity = 0.0
                elif not isinstance(similarity, (int, float)):
                    similarity = 0.0
                
                # Ensure rerank_score is properly typed
                rerank_score = vr.get('rerank_score')
                if rerank_score is not None:
                    if isinstance(rerank_score, str):
                        try:
                            rerank_score = float(rerank_score)
                        except (ValueError, TypeError):
                            rerank_score = None
                    elif not isinstance(rerank_score, (int, float)):
                        rerank_score = None
                
                score = self.combine_scores(
                    vector_similarity=similarity,
                    keyword_match=False,
                    rerank_score=rerank_score,
                    appears_in_both=False
                )
                
                result = vr.copy()
                result['final_score'] = score
                result['match_type'] = 'vector'
                result['appears_in_both'] = False
                
                vector_only.append(result)
                seen_ids.add(result_id)
        
        # Sort vector-only results by score
        vector_only.sort(key=lambda x: x.get('final_score', 0), reverse=True)
        remaining_slots = match_count - len(combined_results)
        combined_results.extend(vector_only[:remaining_slots])
        
        # Phase 3: Fill remaining slots with keyword-only matches
        if len(combined_results) < match_count:
            keyword_only = []
            for kr in keyword_results:
                result_id = kr.get('id')
                if result_id and result_id not in seen_ids:
                    # Convert keyword result to match vector result format
                    score = self.combine_scores(
                        vector_similarity=0.5,  # Default similarity for keyword matches
                        keyword_match=True,
                        rerank_score=kr.get('rerank_score'),
                        appears_in_both=False
                    )
                    
                    result = {
                        'id': kr['id'],
                        'url': kr.get('url', ''),
                        'chunk_number': kr.get('chunk_number', 0),
                        'content': kr.get(content_key, ''),
                        'metadata': kr.get('metadata', {}),
                        'source_id': kr.get('source_id', ''),
                        'similarity': 0.5,  # Default for keyword-only matches
                        'final_score': score,
                        'match_type': 'keyword',
                        'appears_in_both': False
                    }
                    
                    # Copy rerank score if available
                    if 'rerank_score' in kr:
                        result['rerank_score'] = kr['rerank_score']
                    
                    keyword_only.append(result)
                    seen_ids.add(result_id)
            
            # Sort keyword-only results by score
            keyword_only.sort(key=lambda x: x.get('final_score', 0), reverse=True)
            remaining_slots = match_count - len(combined_results)
            combined_results.extend(keyword_only[:remaining_slots])
        
        # Final sorting by combined score
        combined_results.sort(key=lambda x: x.get('final_score', 0), reverse=True)
        
        logger.info(
            f"Merged {len(vector_results)} vector + {len(keyword_results)} keyword results "
            f"into {len(combined_results)} final results "
            f"({len(dual_matches)} dual matches)"
        )
        
        return combined_results[:match_count]
    
    def apply_reranking_boost(
        self,
        results: List[Dict[str, Any]],
        rerank_scores: List[float]
    ) -> List[Dict[str, Any]]:
        """
        Apply reranking scores to search results with proper normalization.
        
        Args:
            results: Search results to rerank
            rerank_scores: Cross-encoder scores for each result
            
        Returns:
            Results with updated scores and rerank information
        """
        enhanced_results = []
        
        for result, rerank_score in zip(results, rerank_scores):
            result = result.copy()
            result['rerank_score'] = float(rerank_score)
            
            # Recalculate final score with reranking
            original_similarity = result.get('similarity', 0)
            keyword_match = result.get('match_type') in ['hybrid', 'keyword']
            appears_in_both = result.get('appears_in_both', False)
            
            final_score = self.combine_scores(
                vector_similarity=original_similarity,
                keyword_match=keyword_match,
                rerank_score=rerank_score,
                appears_in_both=appears_in_both
            )
            
            result['final_score'] = final_score
            enhanced_results.append(result)
        
        # Re-sort by final score
        enhanced_results.sort(key=lambda x: x.get('final_score', 0), reverse=True)
        
        return enhanced_results

def get_default_scorer() -> HybridScorer:
    """Get default hybrid scorer with standard configuration."""
    return HybridScorer()

def get_conservative_scorer() -> HybridScorer:
    """Get conservative scorer that emphasizes vector similarity."""
    config = ScoringConfig(
        vector_weight=0.6,
        keyword_weight=0.25,
        rerank_weight=0.15,
        dual_match_boost=1.1,
        rerank_score_threshold=-1.0
    )
    return HybridScorer(config)

def get_keyword_focused_scorer() -> HybridScorer:
    """Get scorer that emphasizes keyword matches for technical content."""
    config = ScoringConfig(
        vector_weight=0.4,
        keyword_weight=0.4,
        rerank_weight=0.2,
        dual_match_boost=1.3,
        rerank_score_threshold=-1.5
    )
    return HybridScorer(config)