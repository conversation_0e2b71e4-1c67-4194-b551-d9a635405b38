"""
HTTP API Wrapper for SSE MCP Server

This module provides an HTTP API interface that bridges between the dashboard's
HTTP requests and the MCP server's SSE (Server-Sent Events) protocol.
"""
import asyncio
import json
import logging
import uuid
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

import aiohttp
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class ToolCallRequest(BaseModel):
    """Request model for tool calls"""
    name: str
    arguments: Dict[str, Any] = {}

class MCPSSEClient:
    """Client for communicating with MCP SSE server"""
    
    def __init__(self, server_url: str = "http://localhost:8051"):
        self.server_url = server_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool via SSE protocol"""
        try:
            # Create SSE request
            request_id = str(uuid.uuid4())
            request_data = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            # Send request to SSE endpoint
            async with self.session.post(
                f"{self.server_url}/sse",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                # Read SSE response
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # Remove 'data: ' prefix
                        if data_str == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            # Check for result
                            if data.get('id') == request_id and 'result' in data:
                                result = data['result']
                                if 'content' in result and result['content']:
                                    # Parse content
                                    content = result['content'][0]
                                    if 'text' in content:
                                        try:
                                            return json.loads(content['text'])
                                        except json.JSONDecodeError:
                                            return {"text": content['text']}
                                return result
                                
                            # Check for error
                            elif data.get('id') == request_id and 'error' in data:
                                raise Exception(f"MCP error: {data['error']}")
                                
                        except json.JSONDecodeError:
                            continue
                
                raise Exception("No valid response received from MCP server")
                
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            raise

# Create FastAPI app
app = FastAPI(
    title="MCP HTTP Wrapper",
    description="HTTP API wrapper for SSE MCP server",
    version="1.0.0"
)

# Global MCP client
mcp_client: Optional[MCPSSEClient] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global mcp_client
    mcp_client = MCPSSEClient()
    yield
    mcp_client = None

app = FastAPI(lifespan=lifespan)

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "healthy", "service": "mcp-http-wrapper"}

@app.post("/tools/call")
async def call_tool(request: ToolCallRequest):
    """Call an MCP tool via HTTP"""
    if not mcp_client:
        raise HTTPException(status_code=503, detail="MCP client not initialized")
    
    try:
        async with mcp_client as client:
            result = await client.call_tool(request.name, request.arguments)
            
            # Format response to match expected format
            return {
                "content": [{
                    "text": json.dumps(result) if isinstance(result, dict) else str(result)
                }]
            }
    except Exception as e:
        logger.error(f"Tool call failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "MCP HTTP Wrapper",
        "endpoints": {
            "health": "/health",
            "tools": "/tools/call"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8052)