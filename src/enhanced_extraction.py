"""
Enhanced Code Extraction and Relevance Search for AY RAG MCP Server
Simple, accurate, and highly effective implementation for single-container deployment
"""
import re
import ast
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class CodeBlock:
    """Enhanced code block representation"""
    content: str
    language: str
    confidence: float
    context: str
    start_line: Optional[int] = None
    end_line: Optional[int] = None
    function_names: List[str] = None
    class_names: List[str] = None
    imports: List[str] = None
    complexity_score: float = 0.0

class EnhancedCodeExtractor:
    """Enhanced code extractor with improved accuracy"""
    
    def __init__(self, min_code_length: int = 50):
        self.min_code_length = min_code_length
        self.language_patterns = self._get_enhanced_patterns()
    
    def _get_enhanced_patterns(self) -> Dict[str, Dict]:
        """Enhanced language detection patterns with weights"""
        return {
            'python': {
                'keywords': ['def', 'class', 'import', 'from', 'async', 'await', '__init__'],
                'patterns': [
                    (r'\bdef\s+\w+\s*\(', 3.0),  # Function definitions (high weight)
                    (r'\bclass\s+\w+\s*[\(:]', 3.0),  # Class definitions (high weight)
                    (r'\bimport\s+[\w\.]+', 2.0),  # Import statements
                    (r'\bfrom\s+[\w\.]+\s+import', 2.0),  # From imports
                    (r'@\w+', 2.5),  # Decorators
                    (r'#.*$', 1.0),  # Comments
                    (r'\.py\b', 2.0),  # File extensions
                    (r'self\.\w+', 1.5),  # Instance methods
                ],
                'min_score': 4.0
            },
            'javascript': {
                'keywords': ['function', 'const', 'let', 'var', 'class', 'export', 'import'],
                'patterns': [
                    (r'\bfunction\s+\w+\s*\(', 3.0),
                    (r'\bconst\s+\w+\s*=', 2.0),
                    (r'=>', 2.5),  # Arrow functions
                    (r'console\.log', 2.0),
                    (r'export\s+(default\s+)?', 2.5),
                    (r'//.*$', 1.0),
                    (r'\.js\b|\.jsx\b', 2.0),
                ],
                'min_score': 3.5
            },
            'typescript': {
                'keywords': ['interface', 'type', 'enum', 'function', 'const', 'let'],
                'patterns': [
                    (r'\binterface\s+\w+\s*\{', 3.5),
                    (r'\btype\s+\w+\s*=', 3.0),
                    (r':\s*\w+(\[\])?', 2.0),  # Type annotations
                    (r'<\w+>', 2.0),  # Generics
                    (r'\.ts\b|\.tsx\b', 2.5),
                ],
                'min_score': 4.0
            }
        }
    
    def extract_code_blocks(self, content: str, context: str = "") -> List[CodeBlock]:
        """Extract code blocks with enhanced accuracy"""
        code_blocks = []
        
        # Find fenced code blocks
        fenced_pattern = r'```(\w+)?\n(.*?)\n```'
        for match in re.finditer(fenced_pattern, content, re.DOTALL):
            language = match.group(1) or 'unknown'
            code_content = match.group(2).strip()
            
            if len(code_content) >= self.min_code_length:
                confidence = self._calculate_language_confidence(code_content, language)
                block = self._create_enhanced_code_block(
                    code_content, language, confidence, context
                )
                code_blocks.append(block)
        
        # Find indented code blocks (common in documentation)
        indented_blocks = self._extract_indented_code(content)
        for block in indented_blocks:
            if len(block['content']) >= self.min_code_length:
                language, confidence = self._detect_language(block['content'])
                enhanced_block = self._create_enhanced_code_block(
                    block['content'], language, confidence, context
                )
                code_blocks.append(enhanced_block)
        
        return code_blocks
    
    def _extract_indented_code(self, content: str) -> List[Dict]:
        """Extract indented code blocks from content"""
        blocks = []
        lines = content.split('\n')
        current_block = []
        in_code_block = False
        
        for line in lines:
            # Check if line is indented (4+ spaces or tab)
            if re.match(r'^(\s{4,}|\t)', line) and line.strip():
                if not in_code_block:
                    in_code_block = True
                    current_block = []
                current_block.append(line)
            else:
                if in_code_block and current_block:
                    # End of code block
                    block_content = '\n'.join(current_block)
                    blocks.append({'content': block_content.strip()})
                    current_block = []
                in_code_block = False
        
        # Don't forget the last block
        if current_block:
            block_content = '\n'.join(current_block)
            blocks.append({'content': block_content.strip()})
        
        return blocks
    
    def _calculate_language_confidence(self, code: str, declared_lang: str) -> float:
        """Calculate confidence score for language detection"""
        if declared_lang in self.language_patterns:
            return self._score_language_match(code, declared_lang)
        return self._detect_language(code)[1]
    
    def _detect_language(self, code: str) -> Tuple[str, float]:
        """Detect programming language with confidence score"""
        scores = {}
        
        for lang, patterns in self.language_patterns.items():
            score = self._score_language_match(code, lang)
            if score >= patterns['min_score']:
                scores[lang] = score
        
        if scores:
            best_lang = max(scores, key=scores.get)
            return best_lang, scores[best_lang] / 10.0  # Normalize to 0-1
        
        return 'unknown', 0.1
    
    def _score_language_match(self, code: str, language: str) -> float:
        """Score how well code matches a language"""
        if language not in self.language_patterns:
            return 0.0
        
        lang_info = self.language_patterns[language]
        score = 0.0
        
        # Score keyword matches
        for keyword in lang_info['keywords']:
            if re.search(rf'\b{keyword}\b', code):
                score += 1.0
        
        # Score pattern matches with weights
        for pattern, weight in lang_info['patterns']:
            matches = len(re.findall(pattern, code, re.MULTILINE))
            score += matches * weight
        
        return score
    
    def _create_enhanced_code_block(self, content: str, language: str, 
                                   confidence: float, context: str) -> CodeBlock:
        """Create enhanced code block with metadata"""
        # Extract function names
        function_names = self._extract_function_names(content, language)
        
        # Extract class names
        class_names = self._extract_class_names(content, language)
        
        # Extract imports
        imports = self._extract_imports(content, language)
        
        # Calculate complexity
        complexity = self._calculate_complexity(content)
        
        return CodeBlock(
            content=content,
            language=language,
            confidence=confidence,
            context=context,
            function_names=function_names,
            class_names=class_names,
            imports=imports,
            complexity_score=complexity
        )
    
    def _extract_function_names(self, code: str, language: str) -> List[str]:
        """Extract function names from code"""
        functions = []
        
        if language == 'python':
            pattern = r'\bdef\s+(\w+)\s*\('
            functions = re.findall(pattern, code)
        elif language in ['javascript', 'typescript']:
            # Function declarations and arrow functions
            patterns = [
                r'\bfunction\s+(\w+)\s*\(',
                r'\b(\w+)\s*=\s*\([^)]*\)\s*=>',
                r'\b(\w+)\s*:\s*\([^)]*\)\s*=>'
            ]
            for pattern in patterns:
                functions.extend(re.findall(pattern, code))
        
        return list(set(functions))  # Remove duplicates
    
    def _extract_class_names(self, code: str, language: str) -> List[str]:
        """Extract class names from code"""
        classes = []
        
        if language == 'python':
            pattern = r'\bclass\s+(\w+)\s*[\(:]'
            classes = re.findall(pattern, code)
        elif language in ['javascript', 'typescript']:
            pattern = r'\bclass\s+(\w+)\s*[\{\s]'
            classes = re.findall(pattern, code)
        
        return list(set(classes))
    
    def _extract_imports(self, code: str, language: str) -> List[str]:
        """Extract import statements from code"""
        imports = []
        
        if language == 'python':
            patterns = [
                r'\bimport\s+([\w\.]+)',
                r'\bfrom\s+([\w\.]+)\s+import'
            ]
            for pattern in patterns:
                imports.extend(re.findall(pattern, code))
        elif language in ['javascript', 'typescript']:
            patterns = [
                r'\bimport\s+.*?\s+from\s+["\']([^"\']+)["\']',
                r'\bimport\s+["\']([^"\']+)["\']'
            ]
            for pattern in patterns:
                imports.extend(re.findall(pattern, code))
        
        return list(set(imports))
    
    def _calculate_complexity(self, code: str) -> float:
        """Calculate code complexity score"""
        complexity_indicators = [
            r'\bif\b', r'\belse\b', r'\belif\b', r'\bfor\b', r'\bwhile\b',
            r'\btry\b', r'\bcatch\b', r'\bexcept\b', r'\bswitch\b', r'\bcase\b'
        ]
        
        complexity = 1.0  # Base complexity
        for indicator in complexity_indicators:
            matches = len(re.findall(indicator, code, re.IGNORECASE))
            complexity += matches * 0.5
        
        # Normalize to 0-10 scale
        return min(complexity, 10.0)


class RelevanceSearchEnhancer:
    """Enhanced relevance search with improved accuracy"""
    
    def __init__(self):
        self.query_processors = {
            'code_focused': self._process_code_query,
            'concept_focused': self._process_concept_query,
            'implementation_focused': self._process_implementation_query
        }
    
    def enhance_query(self, query: str, context: str = "") -> Dict[str, Any]:
        """Enhance query for better search results"""
        query_type = self._classify_query_type(query)
        enhanced_query = self.query_processors[query_type](query, context)
        
        return {
            'original_query': query,
            'enhanced_query': enhanced_query['query'],
            'query_type': query_type,
            'search_filters': enhanced_query.get('filters', {}),
            'boost_terms': enhanced_query.get('boost_terms', []),
            'context_hints': enhanced_query.get('context_hints', [])
        }
    
    def _classify_query_type(self, query: str) -> str:
        """Classify query type for appropriate processing"""
        code_indicators = [
            'function', 'method', 'class', 'implementation', 'code',
            'example', 'snippet', 'syntax', 'usage', 'api'
        ]
        
        concept_indicators = [
            'concept', 'theory', 'explanation', 'understand', 'learn',
            'tutorial', 'guide', 'documentation', 'overview'
        ]
        
        implementation_indicators = [
            'how to', 'implement', 'create', 'build', 'setup',
            'configure', 'install', 'deploy', 'use'
        ]
        
        query_lower = query.lower()
        
        # Count indicators
        code_count = sum(1 for term in code_indicators if term in query_lower)
        concept_count = sum(1 for term in concept_indicators if term in query_lower)
        impl_count = sum(1 for term in implementation_indicators if term in query_lower)
        
        if code_count >= concept_count and code_count >= impl_count:
            return 'code_focused'
        elif impl_count > concept_count:
            return 'implementation_focused'
        else:
            return 'concept_focused'
    
    def _process_code_query(self, query: str, context: str) -> Dict[str, Any]:
        """Process code-focused queries"""
        # Extract potential function/class names
        code_terms = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\(', query)
        function_names = [term[:-1] for term in code_terms]  # Remove '('
        
        # Extract language hints
        language_hints = self._extract_language_hints(query)
        
        enhanced = query
        boost_terms = function_names + language_hints
        
        return {
            'query': enhanced,
            'filters': {'content_type': 'code'},
            'boost_terms': boost_terms,
            'context_hints': ['Look for function definitions', 'Check code examples']
        }
    
    def _process_concept_query(self, query: str, context: str) -> Dict[str, Any]:
        """Process concept-focused queries"""
        # Extract key concepts
        concept_terms = re.findall(r'\b[A-Z][a-z]+(?:[A-Z][a-z]+)*\b', query)  # CamelCase
        
        enhanced = query
        if context:
            enhanced = f"{query} in context of {context}"
        
        return {
            'query': enhanced,
            'filters': {'content_type': 'documentation'},
            'boost_terms': concept_terms,
            'context_hints': ['Look for explanatory content', 'Check documentation sections']
        }
    
    def _process_implementation_query(self, query: str, context: str) -> Dict[str, Any]:
        """Process implementation-focused queries"""
        # Extract action verbs and targets
        action_pattern = r'\b(how to|create|build|implement|setup|configure)\s+(\w+(?:\s+\w+)*)'
        matches = re.findall(action_pattern, query, re.IGNORECASE)
        
        targets = [match[1] for match in matches]
        
        enhanced = query
        
        return {
            'query': enhanced,
            'filters': {'content_type': 'tutorial'},
            'boost_terms': targets,
            'context_hints': ['Look for step-by-step guides', 'Check examples and tutorials']
        }
    
    def _extract_language_hints(self, query: str) -> List[str]:
        """Extract programming language hints from query"""
        languages = [
            'python', 'javascript', 'typescript', 'java', 'go', 'rust',
            'c++', 'c#', 'php', 'ruby', 'swift', 'kotlin'
        ]
        
        query_lower = query.lower()
        found_languages = [lang for lang in languages if lang in query_lower]
        
        return found_languages


# Usage functions for integration with main server
def extract_enhanced_code_examples(content: str, context: str = "", 
                                  min_code_length: int = 50) -> List[CodeBlock]:
    """Extract code examples with enhanced accuracy"""
    extractor = EnhancedCodeExtractor(min_code_length)
    return extractor.extract_code_blocks(content, context)


def enhance_search_query(query: str, context: str = "") -> Dict[str, Any]:
    """Enhance search query for better relevance"""
    enhancer = RelevanceSearchEnhancer()
    return enhancer.enhance_query(query, context)


def calculate_relevance_score(query: str, content: str, metadata: Dict) -> float:
    """Calculate relevance score for search results"""
    base_score = 0.5  # Base relevance
    
    query_lower = query.lower()
    content_lower = content.lower()
    
    # Exact phrase matches (high weight)
    if query_lower in content_lower:
        base_score += 0.3
    
    # Word overlap scoring
    query_words = set(query_lower.split())
    content_words = set(content_lower.split())
    overlap = len(query_words.intersection(content_words))
    
    if query_words:
        word_score = overlap / len(query_words)
        base_score += word_score * 0.2
    
    # Boost for code content if query seems code-related
    code_terms = ['function', 'method', 'class', 'code', 'example']
    if any(term in query_lower for term in code_terms):
        if metadata.get('content_type') == 'code':
            base_score += 0.15
    
    # Boost for title/header matches
    if 'title' in metadata:
        title_lower = metadata['title'].lower()
        if any(word in title_lower for word in query_words):
            base_score += 0.1
    
    return min(base_score, 1.0)  # Cap at 1.0