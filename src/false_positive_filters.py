"""
False Positive Filtering System for Code Detection

This module implements comprehensive filters to reduce false positive code block detection
by identifying navigation links, content structure patterns, and validating code syntax.
"""

import re
from typing import List, Dict, Any, Set, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class FilterReason(Enum):
    """Reasons why a code block was filtered out"""
    NAVIGATION_LINK = "navigation_link"
    BULLET_POINT_LIST = "bullet_point_list"
    INSUFFICIENT_COMPLEXITY = "insufficient_complexity"
    NO_CODE_SYNTAX = "no_code_syntax"
    PURE_MARKUP = "pure_markup"
    URL_PATTERN = "url_pattern"


@dataclass
class FilterResult:
    """Result of applying false positive filters"""
    is_valid_code: bool
    confidence_score: float
    filter_reasons: List[FilterReason]
    syntax_indicators: List[str]


class NavigationLinkFilter:
    """Filters out navigation links and URL patterns"""
    
    def __init__(self):
        # Markdown link patterns
        self.markdown_link_pattern = re.compile(
            r'\[([^\]]+)\]\(([^)]+)\)',
            re.IGNORECASE
        )
        
        # URL patterns (http/https/relative)
        self.url_patterns = [
            re.compile(r'https?://[^\s\])+]+', re.IGNORECASE),
            re.compile(r'/[a-zA-Z0-9_/-]+\.[a-zA-Z]{2,4}', re.IGNORECASE),
            re.compile(r'\./[a-zA-Z0-9_/-]+', re.IGNORECASE),
            re.compile(r'../[a-zA-Z0-9_/-]+', re.IGNORECASE)
        ]
        
        # Common navigation keywords
        self.nav_keywords = {
            'blog', 'contact', 'about', 'home', 'portfolio', 'services',
            'products', 'documentation', 'docs', 'support', 'help',
            'faq', 'privacy', 'terms', 'login', 'signup', 'register',
            'download', 'gallery', 'news', 'events', 'team', 'careers'
        }
        
        # Path-like patterns
        self.path_pattern = re.compile(
            r'^[/.]?([a-zA-Z0-9_-]+/)*[a-zA-Z0-9_-]*\.[a-zA-Z]{2,4}$'
        )
    
    def is_navigation_content(self, content: str) -> Tuple[bool, List[str]]:
        """
        Check if content appears to be navigation links
        
        Returns:
            Tuple of (is_navigation, reasons)
        """
        reasons = []
        content_lower = content.lower().strip()
        
        # Check for markdown links
        markdown_links = self.markdown_link_pattern.findall(content)
        if len(markdown_links) >= 2:  # Multiple links suggest navigation
            nav_link_count = 0
            for link_text, link_url in markdown_links:
                if any(keyword in link_text.lower() for keyword in self.nav_keywords):
                    nav_link_count += 1
            
            if nav_link_count >= 2:
                reasons.append("multiple_navigation_links")
        
        # Check for URL patterns
        url_matches = 0
        for pattern in self.url_patterns:
            if pattern.search(content):
                url_matches += 1
        
        if url_matches >= 2:
            reasons.append("multiple_urls")
        
        # Check for path-like content
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        path_lines = 0
        for line in lines:
            if self.path_pattern.match(line) or any(keyword in line.lower() for keyword in self.nav_keywords):
                path_lines += 1
        
        if path_lines >= len(lines) * 0.6:  # 60% of lines are path-like
            reasons.append("path_like_content")
        
        # Simple navigation list detection
        if (len(lines) >= 3 and 
            all(len(line) < 50 for line in lines) and  # Short lines
            any(keyword in content_lower for keyword in self.nav_keywords)):
            reasons.append("navigation_list")
        
        return len(reasons) > 0, reasons


class ContentStructureFilter:
    """Filters out bullet points and pure markup content"""
    
    def __init__(self):
        # Bullet point patterns
        self.bullet_patterns = [
            re.compile(r'^\s*[-*+]\s+', re.MULTILINE),
            re.compile(r'^\s*\d+\.\s+', re.MULTILINE),
            re.compile(r'^\s*[a-zA-Z]\.\s+', re.MULTILINE),
            re.compile(r'^\s*[ivxlc]+\.\s+', re.MULTILINE),  # Roman numerals
        ]
        
        # Markdown/HTML markup patterns
        self.markup_patterns = [
            re.compile(r'#{1,6}\s+'),  # Headers
            re.compile(r'\*\*[^*]+\*\*'),  # Bold
            re.compile(r'\*[^*]+\*'),  # Italic
            re.compile(r'`[^`]+`'),  # Inline code
            re.compile(r'<[^>]+>'),  # HTML tags
            re.compile(r'\[[^\]]+\]\([^)]+\)'),  # Links
        ]
    
    def is_bullet_point_list(self, content: str) -> Tuple[bool, List[str]]:
        """
        Check if content is primarily a bullet point list
        
        Returns:
            Tuple of (is_bullet_list, reasons)
        """
        reasons = []
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        if len(lines) < 2:
            return False, reasons
        
        bullet_lines = 0
        for pattern in self.bullet_patterns:
            matches = pattern.findall(content)
            bullet_lines += len(matches)
        
        bullet_ratio = bullet_lines / len(lines)
        
        if bullet_ratio >= 0.7:  # 70% of lines are bullet points
            reasons.append("high_bullet_ratio")
            
            # Additional check: no code syntax in bullet content
            has_code_syntax = False
            for line in lines:
                clean_line = re.sub(r'^\s*[-*+\d\.]\s+', '', line)
                if self._has_code_indicators(clean_line):
                    has_code_syntax = True
                    break
            
            if not has_code_syntax:
                reasons.append("no_code_in_bullets")
        
        return len(reasons) > 0, reasons
    
    def is_pure_markup(self, content: str) -> Tuple[bool, List[str]]:
        """
        Check if content is primarily markdown/HTML markup
        
        Returns:
            Tuple of (is_pure_markup, reasons)
        """
        reasons = []
        
        # Count markup vs plain text
        markup_chars = 0
        for pattern in self.markup_patterns:
            matches = pattern.findall(content)
            markup_chars += sum(len(match) for match in matches)
        
        markup_ratio = markup_chars / len(content) if content else 0
        
        if markup_ratio >= 0.4:  # 40% markup characters
            reasons.append("high_markup_ratio")
        
        # Check for common markdown structures
        if re.search(r'^\s*#{1,6}\s+.*$', content, re.MULTILINE):
            header_count = len(re.findall(r'^\s*#{1,6}\s+', content, re.MULTILINE))
            line_count = len([line for line in content.split('\n') if line.strip()])
            
            if header_count / line_count >= 0.5:  # 50% headers
                reasons.append("mostly_headers")
        
        return len(reasons) > 0, reasons
    
    def _has_code_indicators(self, text: str) -> bool:
        """Check if text has basic code indicators"""
        code_indicators = [
            r'[(){}[\]]',  # Brackets and braces
            r'[=<>!]=?',   # Operators
            r'\w+\(',      # Function calls
            r'[;:]$',      # Statement terminators
        ]
        
        return any(re.search(pattern, text) for pattern in code_indicators)


class CodeSyntaxValidator:
    """Validates actual code syntax and complexity"""
    
    def __init__(self):
        # Programming language keywords by category
        self.language_keywords = {
            'python': {
                'def', 'class', 'import', 'from', 'if', 'else', 'elif', 'for', 'while',
                'try', 'except', 'finally', 'with', 'return', 'yield', 'lambda',
                'async', 'await', 'and', 'or', 'not', 'in', 'is'
            },
            'javascript': {
                'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while',
                'return', 'async', 'await', 'class', 'extends', 'import', 'export',
                'try', 'catch', 'finally', 'new', 'this', 'super'
            },
            'java': {
                'public', 'private', 'protected', 'static', 'final', 'class', 'interface',
                'extends', 'implements', 'import', 'package', 'if', 'else', 'for',
                'while', 'try', 'catch', 'finally', 'return', 'new', 'this', 'super'
            },
            'csharp': {
                'using', 'namespace', 'class', 'public', 'private', 'protected',
                'internal', 'static', 'void', 'string', 'int', 'bool', 'var',
                'if', 'else', 'for', 'while', 'foreach', 'try', 'catch', 'finally'
            },
            'cpp': {
                'include', 'using', 'namespace', 'class', 'struct', 'public', 'private',
                'protected', 'virtual', 'static', 'const', 'if', 'else', 'for',
                'while', 'try', 'catch', 'return', 'new', 'delete'
            }
        }
        
        # Common syntax patterns
        self.syntax_patterns = [
            (r'\w+\s*\([^)]*\)', 'function_call'),
            (r'[a-zA-Z_]\w*\s*=\s*[^=]', 'assignment'),
            (r'if\s*\([^)]+\)', 'conditional'),
            (r'for\s*\([^)]*\)', 'loop'),
            (r'while\s*\([^)]+\)', 'loop'),
            (r'class\s+\w+', 'class_definition'),
            (r'def\s+\w+\s*\(', 'function_definition'),
            (r'function\s+\w+\s*\(', 'function_definition'),
            (r'import\s+[\w\.]+', 'import_statement'),
            (r'#include\s*<[^>]+>', 'include_statement'),
            (r'\{[^}]*\}', 'code_block'),
            (r'["\'].*?["\']', 'string_literal'),
            (r'//.*$', 'comment'),
            (r'/\*.*?\*/', 'block_comment'),
            (r'#.*$', 'comment'),
            # Command line patterns (more specific)
            (r'^\s*(npm|pip|apt|yum|brew|yarn|docker|git)\s+\w+', 'package_manager'),
            (r'^\s*\w+\s+--?\w+', 'command_with_flags'),
            (r'^\s*\w+\s+install\s+[\w.-]+', 'package_install'),
        ]
        
        # Variable/function naming patterns
        self.naming_patterns = [
            (r'[a-z][a-zA-Z0-9]*', 'camelCase'),
            (r'[a-z][a-z0-9_]*', 'snake_case'),
            (r'[A-Z][a-zA-Z0-9]*', 'PascalCase'),
            (r'[A-Z][A-Z0-9_]*', 'CONSTANT_CASE'),
        ]
    
    def validate_code_syntax(self, content: str, min_indicators: int = 2) -> Tuple[bool, float, List[str]]:
        """
        Validate if content has sufficient code syntax indicators
        
        Args:
            content: Text content to validate
            min_indicators: Minimum number of different code indicators required
            
        Returns:
            Tuple of (is_valid_code, confidence_score, indicators_found)
        """
        indicators_found = []
        confidence_score = 0.0
        content_lower = content.lower().strip()
        
        # Quick rejection for very short or empty content
        if len(content_lower) < 10:
            return False, 0.0, []
        
        # Check for language keywords (more strict - need actual word boundaries and context)
        keyword_matches = 0
        keyword_languages = []
        for language, keywords in self.language_keywords.items():
            lang_matches = 0
            for keyword in keywords:
                # Use word boundaries to avoid false matches in regular text
                pattern = rf'\b{re.escape(keyword)}\b'
                matches = re.finditer(pattern, content_lower)
                
                for match in matches:
                    # Check context around the keyword to see if it's used in a programming context
                    start = max(0, match.start() - 20)
                    end = min(len(content_lower), match.end() + 20)
                    context = content_lower[start:end]
                    
                    # Look for programming context indicators
                    programming_context = any(indicator in context for indicator in [
                        '(', ')', '{', '}', '[', ']', '=', ';', ':', '.', 
                        'def ', 'function', 'class ', 'import ', 'return',
                        'var ', 'let ', 'const ', 'public ', 'private '
                    ])
                    
                    # Avoid common English phrases
                    english_context = any(phrase in context for phrase in [
                        'this is', 'it is', 'that is', 'there is', 'here is',
                        'is just', 'is a', 'is the', 'is not', 'is very',
                        'and is', 'or is', 'but is', 'if is', 'as is'
                    ])
                    
                    if programming_context and not english_context:
                        lang_matches += 1
                        break  # Count each keyword only once per language
            
            if lang_matches >= 2:  # Need at least 2 keywords per language in programming context
                keyword_matches += lang_matches
                keyword_languages.append(language)
                indicators_found.append(f'{language}_keywords')
        
        # Boost confidence for keyword matches (but require multiple keywords)
        if keyword_matches >= 2:
            confidence_score += min(keyword_matches * 0.1, 0.4)
        
        # Check syntax patterns
        pattern_matches = 0
        pattern_types = []
        for pattern, pattern_name in self.syntax_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            if matches:
                pattern_matches += len(matches)
                pattern_types.append(pattern_name)
                if pattern_name not in indicators_found:
                    indicators_found.append(pattern_name)
        
        # Boost confidence for syntax patterns
        if pattern_matches > 0:
            confidence_score += min(pattern_matches * 0.05, 0.3)
        
        # Check naming conventions (require more instances and meaningful names)
        naming_matches = 0
        for pattern, convention in self.naming_patterns:
            matches = re.findall(pattern, content)
            # Filter out common English words and require programming context
            meaningful_matches = []
            for match in matches:
                if (len(match) > 2 and 
                    match.lower() not in {
                        'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 
                        'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
                        'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy',
                        'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that',
                        'with', 'have', 'from', 'they', 'know', 'want', 'been', 'good', 'much',
                        'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long',
                        'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were',
                        'just', 'regular', 'text', 'content', 'contains', 'programming', 'syntax',
                        'normal', 'sentences', 'words', 'nothing', 'looks', 'code'
                    }):
                    
                    # Check if this word appears in a programming context
                    match_index = content.lower().find(match.lower())
                    if match_index != -1:
                        start = max(0, match_index - 10)
                        end = min(len(content), match_index + len(match) + 10)
                        context = content[start:end]
                        
                        # Must have programming symbols near the identifier
                        has_programming_context = any(symbol in context for symbol in [
                            '(', ')', '{', '}', '[', ']', '=', ';', ':', '.', '->',
                            ' = ', ' == ', ' != ', ' + ', ' - ', ' * ', ' / '
                        ])
                        
                        if has_programming_context:
                            meaningful_matches.append(match)
            
            # Only count if we have multiple meaningful matches in programming context
            if len(meaningful_matches) >= 5:  # Increased threshold
                naming_matches += 1
                indicators_found.append(f'naming_{convention}')
        
        # Boost confidence for naming patterns
        if naming_matches > 0:
            confidence_score += min(naming_matches * 0.1, 0.2)
        
        # Check structural complexity
        complexity_score = self._calculate_complexity(content)
        confidence_score += complexity_score * 0.1
        
        if complexity_score > 0.3:  # Lower threshold for complexity indicator
            indicators_found.append('high_complexity')
        
        # Additional check: require at least one strong programming indicator
        strong_indicators = {'function_definition', 'class_definition', 'import_statement', 'include_statement'}
        has_strong_indicator = any(indicator in strong_indicators for indicator in indicators_found)
        
        # Command/script indicators (for bash, shell scripts, etc.)
        command_indicators = {'assignment', 'function_call', 'package_manager', 'command_with_flags', 'package_install'}
        has_command_indicator = any(indicator in command_indicators for indicator in indicators_found)
        
        # Require either strong indicators or multiple weak indicators
        unique_indicators = len(set(indicators_found))
        is_valid = (
            (has_strong_indicator and unique_indicators >= min_indicators and confidence_score >= 0.2) or
            (has_command_indicator and unique_indicators >= min_indicators and confidence_score >= 0.1) or  # For scripts
            (unique_indicators >= min_indicators + 1 and confidence_score >= 0.3)
        )
        
        return is_valid, min(confidence_score, 1.0), indicators_found
    
    def _calculate_complexity(self, content: str) -> float:
        """Calculate complexity score based on code characteristics"""
        complexity_indicators = [
            (r'[(){}[\]]', 0.1),  # Brackets and braces
            (r'[=<>!]=?', 0.1),   # Comparison operators
            (r'[+\-*/]', 0.05),   # Arithmetic operators
            (r'[;:]', 0.1),       # Statement terminators
            (r'\w+\.\w+', 0.1),   # Property access
            (r'\w+\[\w*\]', 0.1), # Array/dict access
        ]
        
        complexity = 0.0
        for pattern, weight in complexity_indicators:
            matches = len(re.findall(pattern, content))
            complexity += min(matches * weight, weight * 5)  # Cap contribution
        
        # Normalize by content length
        return min(complexity / max(len(content) / 100, 1), 1.0)


class FalsePositiveFilterSystem:
    """Main system that coordinates all filters"""
    
    def __init__(self, min_code_indicators: int = 2, confidence_threshold: float = 0.3):
        self.nav_filter = NavigationLinkFilter()
        self.structure_filter = ContentStructureFilter()
        self.syntax_validator = CodeSyntaxValidator()
        self.min_code_indicators = min_code_indicators
        self.confidence_threshold = confidence_threshold
    
    def filter_code_block(self, content: str, context_before: str = "", context_after: str = "") -> FilterResult:
        """
        Apply all filters to determine if content is likely a false positive
        
        Args:
            content: Code block content to validate
            context_before: Text content before the code block
            context_after: Text content after the code block
            
        Returns:
            FilterResult with validation outcome
        """
        filter_reasons = []
        confidence_score = 1.0
        syntax_indicators = []
        
        # Validate code syntax FIRST to get indicators
        is_valid_code, syntax_confidence, indicators = self.syntax_validator.validate_code_syntax(
            content, self.min_code_indicators
        )
        syntax_indicators = indicators
        
        # Check navigation links
        is_nav, nav_reasons = self.nav_filter.is_navigation_content(content)
        if is_nav:
            # Check if this content also has strong programming indicators
            has_strong_programming = any(
                indicator in syntax_indicators 
                for indicator in ['function_definition', 'class_definition', 'assignment', 'function_call']
            )
            
            # If it has strong programming indicators, be more lenient
            if has_strong_programming and syntax_confidence > 0.5:
                confidence_score *= 0.7  # Moderate penalty instead of heavy
            else:
                filter_reasons.append(FilterReason.NAVIGATION_LINK)
                confidence_score *= 0.1  # Heavy penalty
        
        # Check bullet point lists
        is_bullets, bullet_reasons = self.structure_filter.is_bullet_point_list(content)
        if is_bullets:
            filter_reasons.append(FilterReason.BULLET_POINT_LIST)
            confidence_score *= 0.3
        
        # Check pure markup
        is_markup, markup_reasons = self.structure_filter.is_pure_markup(content)
        if is_markup:
            filter_reasons.append(FilterReason.PURE_MARKUP)
            confidence_score *= 0.4
        
        # Apply syntax validation results
        if not is_valid_code:
            if syntax_confidence < 0.1:
                filter_reasons.append(FilterReason.NO_CODE_SYNTAX)
            else:
                filter_reasons.append(FilterReason.INSUFFICIENT_COMPLEXITY)
            confidence_score *= syntax_confidence
        else:
            confidence_score *= syntax_confidence
        
        # URL pattern check (additional validation) - only for very simple content
        url_count = sum(1 for pattern in self.nav_filter.url_patterns if pattern.search(content))
        if url_count >= 2 and len(content.split('\n')) <= 5:  # Multiple URLs in short content
            # Check if this looks like code (has programming syntax)
            has_programming_syntax = any(
                indicator in syntax_indicators 
                for indicator in ['function_definition', 'class_definition', 'assignment', 'function_call']
            )
            if not has_programming_syntax:
                filter_reasons.append(FilterReason.URL_PATTERN)
                confidence_score *= 0.5
        
        # Final decision
        is_valid_code_block = (
            len(filter_reasons) == 0 and 
            confidence_score >= self.confidence_threshold
        )
        
        return FilterResult(
            is_valid_code=is_valid_code_block,
            confidence_score=confidence_score,
            filter_reasons=filter_reasons,
            syntax_indicators=syntax_indicators
        )
    
    def batch_filter(self, code_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter a batch of code blocks, removing false positives
        
        Args:
            code_blocks: List of code block dictionaries
            
        Returns:
            Filtered list with valid code blocks only
        """
        valid_blocks = []
        
        for block in code_blocks:
            content = block.get('code', '')
            context_before = block.get('context_before', '')
            context_after = block.get('context_after', '')
            
            result = self.filter_code_block(content, context_before, context_after)
            
            if result.is_valid_code:
                # Add filter metadata to the block
                block['filter_confidence'] = result.confidence_score
                block['syntax_indicators'] = result.syntax_indicators
                valid_blocks.append(block)
            else:
                # Log filtered block for debugging
                block['filtered'] = True
                block['filter_reasons'] = [reason.value for reason in result.filter_reasons]
                block['filter_confidence'] = result.confidence_score
        
        return valid_blocks
    
    def get_filter_stats(self, code_blocks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about filtering performance
        
        Args:
            code_blocks: List of processed code blocks
            
        Returns:
            Dictionary with filtering statistics
        """
        total_blocks = len(code_blocks)
        filtered_blocks = [block for block in code_blocks if block.get('filtered', False)]
        valid_blocks = [block for block in code_blocks if not block.get('filtered', False)]
        
        filter_reason_counts = {}
        for block in filtered_blocks:
            for reason in block.get('filter_reasons', []):
                filter_reason_counts[reason] = filter_reason_counts.get(reason, 0) + 1
        
        return {
            'total_blocks': total_blocks,
            'valid_blocks': len(valid_blocks),
            'filtered_blocks': len(filtered_blocks),
            'filter_rate': len(filtered_blocks) / total_blocks if total_blocks > 0 else 0,
            'filter_reasons': filter_reason_counts,
            'avg_confidence': sum(block.get('filter_confidence', 0) for block in valid_blocks) / len(valid_blocks) if valid_blocks else 0
        }