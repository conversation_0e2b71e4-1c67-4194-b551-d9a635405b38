"""
LLM Client Abstraction Layer
Supports OpenAI and OpenRouter providers with unified interface
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

import httpx
import openai
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """Standardized response format for all LLM providers"""
    content: str
    usage: Dict[str, int]
    model: str
    provider: str

class LLMClient(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    async def generate_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> LLMResponse:
        """Generate completion using provider's API"""
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """Test provider connectivity and API key validity"""
        pass

class OpenAIClient(LLMClient):
    """OpenAI provider implementation"""
    
    def __init__(self):
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        self.client = openai.AsyncOpenAI(api_key=api_key)
        logger.info("OpenAI client initialized")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> LLMResponse:
        """Generate completion using OpenAI API"""
        try:
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            return LLMResponse(
                content=response.choices[0].message.content,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                model=response.model,
                provider="openai"
            )
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def validate_connection(self) -> bool:
        """Test OpenAI connection with a minimal request"""
        try:
            response = await self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            return True
        except Exception as e:
            logger.error(f"OpenAI connection validation failed: {e}")
            return False

class OpenRouterClient(LLMClient):
    """OpenRouter provider implementation"""
    
    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is required")
        
        self.base_url = "https://openrouter.ai/api/v1"
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # App identification for OpenRouter
        self.app_name = os.getenv("OPENROUTER_APP_NAME", "AY RAG MCP Server")
        self.app_url = os.getenv("OPENROUTER_APP_URL", "https://github.com/user/mcp-crawl4ai-rag")
        
        logger.info("OpenRouter client initialized")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_completion(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> LLMResponse:
        """Generate completion using OpenRouter API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "HTTP-Referer": self.app_url,
                "X-Title": self.app_name,
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                **kwargs
            }
            
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            
            response.raise_for_status()
            data = response.json()
            
            # Handle potential errors in response
            if "error" in data:
                raise Exception(f"OpenRouter API error: {data['error']}")
            
            return LLMResponse(
                content=data["choices"][0]["message"]["content"],
                usage=data.get("usage", {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}),
                model=data.get("model", model),
                provider="openrouter"
            )
            
        except httpx.HTTPStatusError as e:
            logger.error(f"OpenRouter HTTP error {e.response.status_code}: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"OpenRouter API error: {e}")
            raise
    
    async def validate_connection(self) -> bool:
        """Test OpenRouter connection with a minimal request"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "HTTP-Referer": self.app_url,
                "X-Title": self.app_name,
                "Content-Type": "application/json"
            }
            
            # Use a small model for testing
            payload = {
                "model": "meta-llama/llama-3.1-8b-instruct",
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 1
            }
            
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            
            response.raise_for_status()
            return True
            
        except Exception as e:
            logger.error(f"OpenRouter connection validation failed: {e}")
            return False
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

def get_llm_client() -> LLMClient:
    """Factory function to create appropriate LLM client based on configuration"""
    provider = os.getenv("LLM_PROVIDER", "openai").lower()
    
    if provider == "openrouter":
        return OpenRouterClient()
    elif provider == "openai":
        return OpenAIClient()
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}. Supported: 'openai', 'openrouter'")

# Global client instance (lazy initialization)
_llm_client = None

async def get_default_llm_client() -> LLMClient:
    """Get or create the default LLM client instance"""
    global _llm_client
    
    if _llm_client is None:
        _llm_client = get_llm_client()
        
        # Validate connection on first use
        if not await _llm_client.validate_connection():
            logger.warning("LLM client connection validation failed")
    
    return _llm_client

async def close_llm_client():
    """Clean up LLM client resources"""
    global _llm_client
    
    if _llm_client and hasattr(_llm_client, '__aexit__'):
        await _llm_client.__aexit__(None, None, None)
    
    _llm_client = None