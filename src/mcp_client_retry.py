#!/usr/bin/env python3
"""
MCP Client with Resilience and Retry Logic

This module provides a resilient MCP client that handles startup race conditions
and connection failures with exponential backoff retry logic.
"""
import asyncio
import logging
import time
import httpx
from typing import Optional, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_retries: int = 10
    initial_delay: float = 1.0
    max_delay: float = 30.0
    backoff_factor: float = 2.0
    timeout: float = 30.0

class ResilientMCPClient:
    """
    MCP client with built-in resilience and retry logic.
    
    This client handles the common race condition where the MCP server 
    HTTP endpoint is available but the MCP session is not yet initialized.
    """
    
    def __init__(self, base_url: str = "http://localhost:8051", retry_config: Optional[RetryConfig] = None):
        self.base_url = base_url.rstrip('/')
        self.retry_config = retry_config or RetryConfig()
        self.client = httpx.AsyncClient(timeout=self.retry_config.timeout)
        
    async def wait_for_server_ready(self) -> bool:
        """
        Wait for the MCP server to be fully ready with exponential backoff.
        
        Returns:
            True if server becomes ready, False if max retries exceeded
        """
        delay = self.retry_config.initial_delay
        
        for attempt in range(self.retry_config.max_retries):
            try:
                logger.info(f"Checking server readiness (attempt {attempt + 1}/{self.retry_config.max_retries})")
                
                # Check the readiness endpoint
                response = await self.client.get(f"{self.base_url}/ready")
                
                if response.status_code == 200:
                    logger.info("✅ MCP server is ready!")
                    return True
                elif response.status_code == 503:
                    logger.info("🔄 Server is still initializing, waiting...")
                else:
                    logger.warning(f"⚠️ Unexpected status code: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"❌ Connection attempt failed: {e}")
            
            # Wait before retrying
            if attempt < self.retry_config.max_retries - 1:
                logger.info(f"⏳ Waiting {delay:.1f}s before retry...")
                await asyncio.sleep(delay)
                delay = min(delay * self.retry_config.backoff_factor, self.retry_config.max_delay)
        
        logger.error(f"❌ Server did not become ready after {self.retry_config.max_retries} attempts")
        return False
    
    async def connect_with_retry(self) -> bool:
        """
        Connect to the SSE endpoint with retry logic.
        
        Returns:
            True if connection successful, False otherwise
        """
        # First, wait for server to be ready
        if not await self.wait_for_server_ready():
            return False
        
        delay = self.retry_config.initial_delay
        
        for attempt in range(self.retry_config.max_retries):
            try:
                logger.info(f"Attempting SSE connection (attempt {attempt + 1}/{self.retry_config.max_retries})")
                
                # Try to connect to the SSE endpoint
                response = await self.client.get(f"{self.base_url}/sse")
                
                if response.status_code == 200:
                    logger.info("✅ SSE connection successful!")
                    return True
                elif response.status_code == 503:
                    logger.info("🔄 Server still initializing, retrying SSE connection...")
                else:
                    logger.warning(f"⚠️ SSE connection failed with status: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"❌ SSE connection attempt failed: {e}")
            
            # Wait before retrying
            if attempt < self.retry_config.max_retries - 1:
                logger.info(f"⏳ Waiting {delay:.1f}s before SSE retry...")
                await asyncio.sleep(delay)
                delay = min(delay * self.retry_config.backoff_factor, self.retry_config.max_delay)
        
        logger.error(f"❌ SSE connection failed after {self.retry_config.max_retries} attempts")
        return False
    
    async def get_server_health(self) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive server health information.
        
        Returns:
            Health status dictionary or None if failed
        """
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code in [200, 503]:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get server health: {e}")
        
        return None
    
    async def ping_server(self) -> bool:
        """
        Simple connectivity test.
        
        Returns:
            True if server responds to ping, False otherwise
        """
        try:
            response = await self.client.get(f"{self.base_url}/ping")
            return response.status_code == 200 and response.text == "pong"
        except Exception:
            return False
    
    async def close(self):
        """Clean up resources."""
        await self.client.aclose()

async def wait_for_mcp_server(base_url: str = "http://localhost:8051", max_wait_time: float = 120.0) -> bool:
    """
    Utility function to wait for MCP server to become ready.
    
    Args:
        base_url: Base URL of the MCP server
        max_wait_time: Maximum time to wait in seconds
        
    Returns:
        True if server becomes ready, False if timeout
    """
    retry_config = RetryConfig(
        max_retries=int(max_wait_time / 2),  # Check every ~2 seconds
        initial_delay=1.0,
        max_delay=5.0
    )
    
    client = ResilientMCPClient(base_url, retry_config)
    try:
        return await client.wait_for_server_ready()
    finally:
        await client.close()

# Example usage for testing
async def main():
    """Example usage of the resilient MCP client."""
    logging.basicConfig(level=logging.INFO)
    
    client = ResilientMCPClient()
    
    try:
        # Test ping
        if await client.ping_server():
            print("✅ Server is reachable")
        else:
            print("❌ Server is not reachable")
            return
        
        # Get health status
        health = await client.get_server_health()
        if health:
            print(f"📊 Server health: {health['status']}")
            print(f"🔧 Initialization complete: {health.get('initialization_complete', False)}")
        
        # Wait for server to be ready
        if await client.wait_for_server_ready():
            print("🎉 Server is ready for MCP connections!")
        else:
            print("❌ Server failed to become ready")
            
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())