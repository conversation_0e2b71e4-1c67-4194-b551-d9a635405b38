"""
Adaptive False Positive Filtering System

Enhanced version of the false positive filter that adapts filtering strategies
based on content type classification for optimal code detection accuracy.
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from functools import lru_cache
from .false_positive_filters import (
    FalsePositiveFilterSystem, 
    FilterResult, 
    FilterReason,
    NavigationLinkFilter,
    ContentStructureFilter, 
    CodeSyntaxValidator
)
from .content_type_classifier import (
    ContentTypeClassifier,
    AdaptiveFilterManager,
    ContentType,
    FilterStrategy,
    ContentClassification,
    FilterConfiguration
)


@dataclass
class AdaptiveFilterResult:
    """Extended filter result with content type classification"""
    base_result: FilterResult
    classification: ContentClassification
    filter_config: FilterConfiguration
    strategy_applied: FilterStrategy
    review_recommended: bool
    adaptive_confidence: float


class AdaptiveFalsePositiveFilterSystem:
    """
    Enhanced false positive filter system that adapts filtering strategies
    based on detected content type for optimal accuracy per page type.
    """
    
    def __init__(self, 
                 default_min_indicators: int = 2,
                 default_confidence_threshold: float = 0.3):
        # Initialize base components
        self.base_filter_system = FalsePositiveFilterSystem(
            min_code_indicators=default_min_indicators,
            confidence_threshold=default_confidence_threshold
        )
        
        # Initialize adaptive components
        self.content_classifier = ContentTypeClassifier()
        self.adaptive_manager = AdaptiveFilterManager(self.content_classifier)
        
        # Performance tracking
        self.filter_stats = {
            'total_processed': 0,
            'filtered_by_strategy': {},
            'review_queue_size': 0,
            'accuracy_metrics': {}
        }
        
        # Cache for filter instances to avoid recreation
        self._filter_cache = {}
        self._last_classification = None
    
    def filter_code_block_adaptive(self, 
                                 content: str, 
                                 context_before: str = "", 
                                 context_after: str = "",
                                 url: str = "",
                                 page_content: str = "",
                                 metadata: Optional[Dict[str, Any]] = None) -> AdaptiveFilterResult:
        """
        Apply adaptive filtering based on detected content type
        
        Args:
            content: Code block content to validate
            context_before: Text content before the code block
            context_after: Text content after the code block
            url: URL of the source page
            page_content: Full page content for classification
            metadata: Additional metadata about the page
            
        Returns:
            AdaptiveFilterResult with enhanced filtering decision
        """
        self.filter_stats['total_processed'] += 1
        
        # Step 1: Classify content type using full page content
        classification, filter_config = self.adaptive_manager.get_adaptive_filter_config(
            page_content or context_before + content + context_after,
            url,
            metadata
        )
        
        # Step 2: Create adaptive filter system with content-specific configuration
        adaptive_filter = self._create_adaptive_filter(classification, filter_config)
        
        # Step 3: Apply base filtering with adaptive parameters
        base_result = adaptive_filter.filter_code_block(content, context_before, context_after)
        
        # Step 4: Apply content-type specific adjustments
        adjusted_result = self._apply_content_specific_adjustments(
            base_result, classification, content, context_before, context_after
        )
        
        # Step 5: Calculate adaptive confidence score
        adaptive_confidence = self._calculate_adaptive_confidence(
            adjusted_result, classification, filter_config
        )
        
        # Step 6: Determine if manual review is recommended
        review_recommended = self.adaptive_manager.should_review_manually(
            classification, adjusted_result
        )
        
        # Step 7: Update statistics
        self._update_statistics(classification, adjusted_result, review_recommended)
        
        return AdaptiveFilterResult(
            base_result=adjusted_result,
            classification=classification,
            filter_config=filter_config,
            strategy_applied=classification.strategy,
            review_recommended=review_recommended,
            adaptive_confidence=adaptive_confidence
        )
    
    def batch_filter_adaptive(self, 
                            code_blocks: List[Dict[str, Any]], 
                            url: str = "", 
                            page_content: str = "",
                            metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Apply adaptive filtering to a batch of code blocks from the same page (optimized)
        
        Args:
            code_blocks: List of code block dictionaries
            url: URL of the source page
            page_content: Full page content for classification
            metadata: Additional metadata about the page
            
        Returns:
            Filtered list with valid code blocks and adaptive metadata
        """
        if not code_blocks:
            return []
        
        # Classify content once for the entire page (with potential caching)
        classification, filter_config = self._get_cached_classification(
            page_content, url, metadata
        )
        
        # Create adaptive filter for this content type (cached)
        adaptive_filter = self._create_adaptive_filter(classification, filter_config)
        
        valid_blocks = []
        review_queue = []
        
        # Pre-compute shared data for batch processing
        shared_metadata = {
            'classification': classification,
            'filter_config': filter_config,
            'adaptive_filter': adaptive_filter
        }
        
        for block in code_blocks:
            content = block.get('code', '')
            context_before = block.get('context_before', '')
            context_after = block.get('context_after', '')
            
            # Apply optimized batch filtering
            result = self._filter_block_with_shared_context(
                content, context_before, context_after, shared_metadata
            )
            
            if result.base_result.is_valid_code:
                # Add adaptive metadata to the block
                block['adaptive_classification'] = {
                    'content_type': result.classification.content_type.value,
                    'strategy': result.strategy_applied.value,
                    'confidence': result.classification.confidence_score,
                    'adaptive_confidence': result.adaptive_confidence
                }
                block['filter_confidence'] = result.base_result.confidence_score
                block['syntax_indicators'] = result.base_result.syntax_indicators
                
                valid_blocks.append(block)
                
                # Add to review queue if recommended
                if result.review_recommended:
                    review_queue.append({
                        'block': block,
                        'classification': result.classification,
                        'reason': 'low_confidence_or_conflict'
                    })
            else:
                # Log filtered block with adaptive reasoning
                block['filtered'] = True
                block['filter_reasons'] = [reason.value for reason in result.base_result.filter_reasons]
                block['adaptive_classification'] = {
                    'content_type': result.classification.content_type.value,
                    'strategy': result.strategy_applied.value,
                    'confidence': result.classification.confidence_score
                }
        
        # Update review queue statistics
        self.filter_stats['review_queue_size'] += len(review_queue)
        
        return valid_blocks
    
    def _get_cached_classification(self, page_content: str, url: str, metadata: Optional[Dict[str, Any]]) -> Tuple:
        """Get classification with basic caching for repeated content"""
        # Simple content-based caching (could be enhanced with more sophisticated hashing)
        content_key = (url, hash(page_content) if page_content else 0)
        
        if hasattr(self, '_last_classification_key') and self._last_classification_key == content_key:
            return self._last_classification
        
        # Get fresh classification
        classification, filter_config = self.adaptive_manager.get_adaptive_filter_config(
            page_content, url, metadata
        )
        
        # Cache for potential reuse
        self._last_classification_key = content_key
        self._last_classification = (classification, filter_config)
        
        return classification, filter_config
    
    def _filter_block_with_shared_context(self, content: str, context_before: str, 
                                        context_after: str, shared_metadata: Dict) -> AdaptiveFilterResult:
        """Optimized filtering using pre-computed shared context"""
        self.filter_stats['total_processed'] += 1
        
        # Use pre-computed classification and filter
        classification = shared_metadata['classification']
        filter_config = shared_metadata['filter_config']
        adaptive_filter = shared_metadata['adaptive_filter']
        
        # Apply base filtering
        base_result = adaptive_filter.filter_code_block(content, context_before, context_after)
        
        # Apply content-type specific adjustments
        adjusted_result = self._apply_content_specific_adjustments(
            base_result, classification, content, context_before, context_after
        )
        
        # Calculate adaptive confidence score
        adaptive_confidence = self._calculate_adaptive_confidence(
            adjusted_result, classification, filter_config
        )
        
        # Determine if manual review is recommended
        review_recommended = self.adaptive_manager.should_review_manually(
            classification, adjusted_result
        )
        
        # Update statistics
        self._update_statistics(classification, adjusted_result, review_recommended)
        
        return AdaptiveFilterResult(
            base_result=adjusted_result,
            classification=classification,
            filter_config=filter_config,
            strategy_applied=classification.strategy,
            review_recommended=review_recommended,
            adaptive_confidence=adaptive_confidence
        )
    
    def _create_adaptive_filter(self, 
                              classification: ContentClassification, 
                              filter_config: FilterConfiguration) -> FalsePositiveFilterSystem:
        """Create a filter system with adaptive configuration (cached)"""
        # Cache key based on configuration to avoid recreating identical filters
        cache_key = (
            filter_config.min_code_indicators,
            filter_config.confidence_threshold
        )
        
        if cache_key not in self._filter_cache:
            self._filter_cache[cache_key] = FalsePositiveFilterSystem(
                min_code_indicators=filter_config.min_code_indicators,
                confidence_threshold=filter_config.confidence_threshold
            )
        
        return self._filter_cache[cache_key]
    
    @lru_cache(maxsize=128)
    def _get_strong_programming_indicators(self) -> frozenset:
        """Cached set of strong programming indicators for performance"""
        return frozenset([
            'function_definition', 'class_definition', 'assignment', 'function_call',
            'import_statement', 'include_statement', 'python_keywords', 'javascript_keywords',
            'java_keywords', 'csharp_keywords', 'cpp_keywords'
        ])
    
    def _apply_content_specific_adjustments(self, 
                                          base_result: FilterResult,
                                          classification: ContentClassification,
                                          content: str,
                                          context_before: str,
                                          context_after: str) -> FilterResult:
        """Apply content-type specific adjustments to the base filter result (optimized)"""
        
        # Start with base result
        adjusted_confidence = base_result.confidence_score
        adjusted_reasons = base_result.filter_reasons.copy()
        adjusted_valid = base_result.is_valid_code
        
        # Pre-compute syntax indicators set for efficiency
        syntax_set = set(base_result.syntax_indicators)
        
        # Marketing page adjustments (strictest filtering)
        if classification.content_type == ContentType.MARKETING:
            # Apply marketing adjustments only if confidence is reasonably high
            if classification.confidence_score > 0.2:
                # Check for strong programming indicators (optimized)
                strong_indicators = self._get_strong_programming_indicators()
                has_strong_programming = bool(strong_indicators & syntax_set)
                
                # Only apply very strict filtering if there are no strong programming indicators
                if not has_strong_programming:
                    if len(base_result.syntax_indicators) < 3:
                        adjusted_valid = False
                        if FilterReason.INSUFFICIENT_COMPLEXITY not in adjusted_reasons:
                            adjusted_reasons.append(FilterReason.INSUFFICIENT_COMPLEXITY)
                else:
                    # For marketing pages with actual code, override base filter if it was rejected for complexity or low confidence
                    if (not base_result.is_valid_code and 
                        (FilterReason.INSUFFICIENT_COMPLEXITY in base_result.filter_reasons or
                         len(base_result.filter_reasons) == 0)):  # No filter reasons, just low confidence
                        adjusted_valid = True  # Override the base filter decision
                        # Remove the insufficient complexity reason if present
                        if FilterReason.INSUFFICIENT_COMPLEXITY in adjusted_reasons:
                            adjusted_reasons.remove(FilterReason.INSUFFICIENT_COMPLEXITY)
                    # Be moderately strict but don't reject good code
                    adjusted_confidence = max(adjusted_confidence * 0.8, 0.3)  # Minimum confidence for actual code
                
                # Heavy penalty for navigation-like patterns (but not if strong programming is present)
                if any('navigation' in indicator for indicator in base_result.syntax_indicators):
                    if not has_strong_programming:
                        adjusted_confidence *= 0.1
                        adjusted_valid = False
                    else:
                        adjusted_confidence *= 0.5  # Lighter penalty for actual code
        
        # Tutorial page adjustments (most lenient)
        elif classification.content_type == ContentType.TUTORIAL:
            if classification.confidence_score > 0.2:
                # Be more lenient with code blocks in tutorials
                if not base_result.is_valid_code and base_result.confidence_score > 0.1:
                    # Check for educational context or programming indicators
                    educational_indicators = [
                        'step', 'example', 'tutorial', 'lesson', 'exercise',
                        'practice', 'demo', 'sample', 'here', 'how', 'create'
                    ]
                    
                    context_text = (context_before + ' ' + context_after).lower()
                    has_educational_context = any(indicator in context_text for indicator in educational_indicators)
                    
                    # Also check for programming indicators (optimized)
                    prog_indicators = {'function_definition', 'class_definition', 'python_keywords', 
                                     'javascript_keywords', 'assignment', 'function_call'}
                    has_programming_indicators = bool(prog_indicators & syntax_set)
                    
                    if has_educational_context or has_programming_indicators:
                        adjusted_valid = True
                        adjusted_confidence = max(adjusted_confidence, 0.35)
                        # Remove some filter reasons for tutorial context
                        if FilterReason.INSUFFICIENT_COMPLEXITY in adjusted_reasons:
                            adjusted_reasons.remove(FilterReason.INSUFFICIENT_COMPLEXITY)
        
        # Documentation adjustments (balanced approach)
        elif classification.content_type == ContentType.DOCUMENTATION:
            if classification.confidence_score > 0.4:
                # Balanced filtering - slightly more lenient than default
                if not base_result.is_valid_code and base_result.confidence_score > 0.2:
                    # Check for documentation context
                    doc_indicators = [
                        'api', 'reference', 'documentation', 'guide', 'manual',
                        'parameters', 'returns', 'example', 'usage'
                    ]
                    
                    context_text = (context_before + ' ' + context_after).lower()
                    if any(indicator in context_text for indicator in doc_indicators):
                        adjusted_confidence *= 1.2  # Boost confidence
        
        # GitHub repository adjustments (consistent application)
        elif classification.content_type == ContentType.GITHUB_REPO:
            # Apply consistent rules regardless of specific patterns
            # No special adjustments - use base filter results as-is
            pass
        
        # API Reference adjustments (lenient for code examples)
        elif classification.content_type == ContentType.API_REFERENCE:
            if classification.confidence_score > 0.4:
                # Be lenient with API examples and endpoint definitions
                api_indicators = [
                    'endpoint', 'request', 'response', 'method', 'parameter',
                    'curl', 'http', 'get', 'post', 'put', 'delete'
                ]
                
                context_text = (context_before + ' ' + context_after).lower()
                content_lower = content.lower()
                
                if (any(indicator in context_text for indicator in api_indicators) or
                    any(indicator in content_lower for indicator in api_indicators)):
                    adjusted_confidence *= 1.3  # Boost confidence for API context
        
        # General fallback: optimized strong programming check
        if (not adjusted_valid and 
            classification.confidence_score < 0.3):
            
            strong_indicators = self._get_strong_programming_indicators()
            syntax_set = set(base_result.syntax_indicators)
            has_strong_programming = bool(strong_indicators & syntax_set)
            
            # Override if we have strong programming indicators and either:
            # 1. The failure was due to insufficient complexity, OR
            # 2. The failure was due to low confidence but no actual filter reasons (confidence-only rejection)
            if (has_strong_programming and 
                (FilterReason.INSUFFICIENT_COMPLEXITY in adjusted_reasons or 
                 len(adjusted_reasons) == 0)):  # No actual filter reasons, just low confidence
                adjusted_valid = True
                adjusted_confidence = max(adjusted_confidence, 0.4)
                if FilterReason.INSUFFICIENT_COMPLEXITY in adjusted_reasons:
                    adjusted_reasons.remove(FilterReason.INSUFFICIENT_COMPLEXITY)
        
        # Cap confidence at 1.0
        adjusted_confidence = min(adjusted_confidence, 1.0)
        
        return FilterResult(
            is_valid_code=adjusted_valid,
            confidence_score=adjusted_confidence,
            filter_reasons=adjusted_reasons,
            syntax_indicators=base_result.syntax_indicators
        )
    
    def _calculate_adaptive_confidence(self, 
                                     filter_result: FilterResult,
                                     classification: ContentClassification,
                                     filter_config: FilterConfiguration) -> float:
        """Calculate adaptive confidence score combining filter and classification confidence"""
        
        # Base confidence from filter result
        filter_confidence = filter_result.confidence_score
        
        # Classification confidence
        classification_confidence = classification.confidence_score
        
        # Weighted combination
        filter_weight = filter_config.syntax_weight
        classification_weight = 1.0 - filter_weight
        
        adaptive_confidence = (
            filter_confidence * filter_weight +
            classification_confidence * classification_weight
        )
        
        # Apply strategy-specific adjustments
        if classification.strategy == FilterStrategy.STRICT:
            # Stricter standards reduce confidence for borderline cases
            if adaptive_confidence < 0.6:
                adaptive_confidence *= 0.8
        elif classification.strategy == FilterStrategy.LENIENT:
            # Lenient standards boost confidence for borderline cases
            if 0.2 < adaptive_confidence < 0.6:
                adaptive_confidence *= 1.2
        
        return min(adaptive_confidence, 1.0)
    
    def _update_statistics(self, 
                         classification: ContentClassification,
                         filter_result: FilterResult,
                         review_recommended: bool):
        """Update performance statistics"""
        strategy = classification.strategy.value
        
        if strategy not in self.filter_stats['filtered_by_strategy']:
            self.filter_stats['filtered_by_strategy'][strategy] = {
                'total': 0,
                'filtered': 0,
                'valid': 0,
                'reviewed': 0
            }
        
        self.filter_stats['filtered_by_strategy'][strategy]['total'] += 1
        
        if filter_result.is_valid_code:
            self.filter_stats['filtered_by_strategy'][strategy]['valid'] += 1
        else:
            self.filter_stats['filtered_by_strategy'][strategy]['filtered'] += 1
        
        if review_recommended:
            self.filter_stats['filtered_by_strategy'][strategy]['reviewed'] += 1
    
    def get_adaptive_filter_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics about adaptive filtering performance"""
        
        # Get base performance metrics
        performance_report = self.adaptive_manager.get_performance_report()
        
        # Add adaptive filtering specific metrics
        adaptive_stats = {
            'total_processed': self.filter_stats['total_processed'],
            'strategy_performance': self.filter_stats['filtered_by_strategy'],
            'review_queue_size': self.filter_stats['review_queue_size'],
            'review_rate': (
                self.filter_stats['review_queue_size'] / 
                max(self.filter_stats['total_processed'], 1)
            )
        }
        
        # Calculate strategy effectiveness
        strategy_effectiveness = {}
        for strategy, stats in self.filter_stats['filtered_by_strategy'].items():
            if stats['total'] > 0:
                strategy_effectiveness[strategy] = {
                    'precision': stats['valid'] / stats['total'],
                    'filter_rate': stats['filtered'] / stats['total'],
                    'review_rate': stats['reviewed'] / stats['total']
                }
        
        adaptive_stats['strategy_effectiveness'] = strategy_effectiveness
        
        # Combine with base performance report
        combined_stats = {
            'adaptive_filtering': adaptive_stats,
            'content_classification': performance_report
        }
        
        return combined_stats
    
    def get_review_queue_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations for manual review queue management"""
        
        stats = self.get_adaptive_filter_stats()
        recommendations = []
        
        # High review rate recommendation
        if stats['adaptive_filtering']['review_rate'] > 0.1:  # More than 10%
            recommendations.append({
                'type': 'high_review_rate',
                'priority': 'medium',
                'description': 'Review rate is above 10%, consider adjusting confidence thresholds',
                'suggested_action': 'Lower confidence thresholds for high-performing content types'
            })
        
        # Strategy-specific recommendations
        for strategy, effectiveness in stats['adaptive_filtering']['strategy_effectiveness'].items():
            if effectiveness['review_rate'] > 0.2:  # More than 20% for this strategy
                recommendations.append({
                    'type': 'strategy_high_review',
                    'priority': 'high',
                    'strategy': strategy,
                    'description': f'{strategy} strategy has high review rate ({effectiveness["review_rate"]:.1%})',
                    'suggested_action': f'Review and adjust {strategy} filtering parameters'
                })
        
        return recommendations
    
    def reset_statistics(self):
        """Reset performance statistics (useful for testing or new deployments)"""
        self.filter_stats = {
            'total_processed': 0,
            'filtered_by_strategy': {},
            'review_queue_size': 0,
            'accuracy_metrics': {}
        }
        self.adaptive_manager.performance_history = []


# Backward compatibility wrapper
class EnhancedFalsePositiveFilterSystem(AdaptiveFalsePositiveFilterSystem):
    """Backward compatible enhanced filter system"""
    
    def filter_code_block(self, content: str, context_before: str = "", context_after: str = "") -> FilterResult:
        """Backward compatible method that uses adaptive filtering with defaults"""
        result = self.filter_code_block_adaptive(
            content=content,
            context_before=context_before,
            context_after=context_after,
            url="",
            page_content=context_before + content + context_after
        )
        return result.base_result
    
    def batch_filter(self, code_blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Backward compatible batch filtering"""
        return self.batch_filter_adaptive(code_blocks)