"""
Content safety validation for crawled content

This module provides comprehensive content validation to detect and flag
suspicious, malicious, or inappropriate content before it's stored in the system.
"""
import re
from typing import List, Tuple, Dict, Set, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ContentRisk(Enum):
    """Risk levels for content validation"""
    SAFE = "safe"
    SUSPICIOUS = "suspicious"
    BLOCKED = "blocked"


class RiskReason(Enum):
    """Specific reasons for content risk classification"""
    # Form-related risks
    PASSWORD_FORM = "password_form"
    PAYMENT_FORM = "payment_form"
    PERSONAL_DATA_FORM = "personal_data_form"
    LOGIN_FORM = "login_form"
    
    # Phishing indicators
    ACCOUNT_VERIFICATION = "account_verification"
    URGENT_ACTION = "urgent_action"
    IDENTITY_REQUEST = "identity_request"
    FAKE_SECURITY_ALERT = "fake_security_alert"
    
    # Malicious content
    SCRIPT_INJECTION = "script_injection"
    SUSPICIOUS_LINKS = "suspicious_links"
    MALWARE_INDICATORS = "malware_indicators"
    
    # Spam and low-quality content
    SPAM_CONTENT = "spam_content"
    EXCESSIVE_LINKS = "excessive_links"
    DUPLICATE_CONTENT = "duplicate_content"
    
    # Privacy concerns
    PII_COLLECTION = "pii_collection"
    TRACKING_CODE = "tracking_code"


@dataclass
class ValidationResult:
    """Result of content validation"""
    risk_level: ContentRisk
    confidence: float
    reasons: List[RiskReason]
    details: List[str]
    suggestions: List[str]
    safe_to_store: bool


class ContentSafetyValidator:
    """Validates content for safety risks and suspicious patterns"""
    
    def __init__(self):
        self._init_patterns()
        self._init_risk_weights()
    
    def _init_patterns(self):
        """Initialize detection patterns"""
        
        # Form detection patterns
        self.form_patterns = {
            RiskReason.PASSWORD_FORM: [
                (r'<input[^>]*type=["\']password["\']', 'Password input field'),
                (r'name=["\']password["\']', 'Password field by name'),
                (r'id=["\']password["\']', 'Password field by ID'),
            ],
            RiskReason.PAYMENT_FORM: [
                (r'credit.?card|card.?number|cvv|expir', 'Credit card information'),
                (r'billing|payment|checkout|purchase', 'Payment-related form'),
                (r'amount|price|cost|\$\d+', 'Financial information'),
            ],
            RiskReason.PERSONAL_DATA_FORM: [
                (r'ssn|social.?security|tax.?id', 'Social security number'),
                (r'date.?of.?birth|dob|birthday', 'Date of birth'),
                (r'driver.?license|passport', 'Government ID'),
                (r'phone.?number|address|zip.?code', 'Personal contact info'),
            ],
            RiskReason.LOGIN_FORM: [
                (r'<form[^>]*>.*?login.*?</form>', 'Login form'),
                (r'username|email.*password', 'Credential collection'),
                (r'sign.?in|log.?in|authenticate', 'Authentication form'),
            ]
        }
        
        # Phishing indicators
        self.phishing_patterns = {
            RiskReason.ACCOUNT_VERIFICATION: [
                (r'verify.{0,30}account|account.{0,30}verification', 'Account verification request'),
                (r'suspended.{0,30}account|account.{0,30}suspended', 'Account suspension claim'),
                (r'confirm.{0,30}identity|identity.{0,30}verification', 'Identity verification'),
            ],
            RiskReason.URGENT_ACTION: [
                (r'urgent.{0,20}action|immediate.{0,20}action', 'Urgent action required'),
                (r'act.{0,10}now|click.{0,10}here.{0,10}immediately', 'Immediate action request'),
                (r'expires?.{0,10}(today|soon|24.?hours)', 'Time pressure tactics'),
            ],
            RiskReason.FAKE_SECURITY_ALERT: [
                (r'security.{0,20}alert|virus.{0,20}detected', 'Fake security alert'),
                (r'malware.{0,20}found|computer.{0,20}infected', 'Fake malware warning'),
                (r'unauthorized.{0,20}access|breach.{0,20}detected', 'Fake breach alert'),
            ]
        }
        
        # Script and injection patterns
        self.script_patterns = {
            RiskReason.SCRIPT_INJECTION: [
                (r'<script[^>]*>', 'Script tag'),
                (r'javascript:', 'JavaScript URL'),
                (r'on\w+\s*=\s*["\'][^"\']*["\']', 'Event handler'),
                (r'eval\s*\(|setTimeout\s*\(|setInterval\s*\(', 'Dangerous JavaScript'),
            ],
            RiskReason.SUSPICIOUS_LINKS: [
                (r'bit\.ly|tinyurl|t\.co|goo\.gl', 'URL shortener'),
                (r'[a-z0-9]+\.tk|[a-z0-9]+\.ml|[a-z0-9]+\.ga', 'Suspicious TLD'),
                (r'[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}', 'IP address URL'),
            ]
        }
        
        # Content quality patterns
        self.quality_patterns = {
            RiskReason.SPAM_CONTENT: [
                (r'buy.{0,10}now|limited.{0,10}time|special.{0,10}offer', 'Sales pressure'),
                (r'free.{0,10}money|easy.{0,10}cash|work.{0,10}from.{0,10}home', 'Get-rich-quick'),
                (r'click.{0,10}here.{0,10}(now|today|free)', 'Clickbait'),
            ],
        }
        
        # Privacy patterns
        self.privacy_patterns = {
            RiskReason.PII_COLLECTION: [
                (r'collect.{0,20}(personal|private).{0,20}information', 'PII collection'),
                (r'share.{0,20}data.{0,20}with.{0,20}third.{0,20}parties', 'Data sharing'),
            ],
            RiskReason.TRACKING_CODE: [
                (r'google-analytics|gtag|facebook.{0,10}pixel', 'Tracking code'),
                (r'track.{0,10}(user|visitor|behavior)', 'User tracking'),
            ]
        }
    
    def _init_risk_weights(self):
        """Initialize risk weights for different categories"""
        self.risk_weights = {
            # High-risk patterns
            RiskReason.PASSWORD_FORM: 0.8,
            RiskReason.PAYMENT_FORM: 0.9,
            RiskReason.PERSONAL_DATA_FORM: 0.8,
            RiskReason.SCRIPT_INJECTION: 0.9,
            RiskReason.FAKE_SECURITY_ALERT: 0.7,
            
            # Medium-risk patterns
            RiskReason.LOGIN_FORM: 0.6,
            RiskReason.ACCOUNT_VERIFICATION: 0.6,
            RiskReason.URGENT_ACTION: 0.5,
            RiskReason.SUSPICIOUS_LINKS: 0.5,
            
            # Lower-risk patterns
            RiskReason.SPAM_CONTENT: 0.3,
            RiskReason.EXCESSIVE_LINKS: 0.2,
            RiskReason.PII_COLLECTION: 0.4,
            RiskReason.TRACKING_CODE: 0.2,
        }
    
    def validate_content(self, content: str, url: str = "", title: str = "") -> ValidationResult:
        """
        Validate content for safety risks
        
        Args:
            content: The content to validate
            url: Source URL for additional context
            title: Page title for additional context
            
        Returns:
            ValidationResult: Comprehensive validation result
        """
        reasons = []
        details = []
        total_risk_score = 0.0
        
        # Combine content, title, and URL for comprehensive analysis
        full_text = f"{title} {content} {url}".lower()
        
        # Check all pattern categories
        pattern_groups = [
            self.form_patterns,
            self.phishing_patterns,
            self.script_patterns,
            self.quality_patterns,
            self.privacy_patterns
        ]
        
        for pattern_group in pattern_groups:
            for risk_reason, patterns in pattern_group.items():
                risk_score, found_details = self._check_patterns(
                    patterns, full_text, content, risk_reason
                )
                
                if risk_score > 0:
                    reasons.append(risk_reason)
                    details.extend(found_details)
                    total_risk_score += risk_score
        
        # Check for excessive links separately
        link_count = len(re.findall(r'<a[^>]*href', content, re.IGNORECASE))
        if link_count > 20:
            reasons.append(RiskReason.EXCESSIVE_LINKS)
            details.append(f"Excessive links detected: {link_count} links")
            total_risk_score += 0.3
        
        # Determine overall risk level with more conservative thresholds
        confidence = min(total_risk_score, 1.0)
        
        if total_risk_score >= 1.2:  # Raised threshold - need multiple high-risk indicators
            risk_level = ContentRisk.BLOCKED
            safe_to_store = False
        elif total_risk_score >= 0.6:  # Raised threshold - be less aggressive
            risk_level = ContentRisk.SUSPICIOUS
            safe_to_store = True  # Store but flag for review
        else:
            risk_level = ContentRisk.SAFE
            safe_to_store = True
        
        # Generate suggestions
        suggestions = self._generate_suggestions(risk_level, reasons, url)
        
        return ValidationResult(
            risk_level=risk_level,
            confidence=confidence,
            reasons=reasons,
            details=details,
            suggestions=suggestions,
            safe_to_store=safe_to_store
        )
    
    def _check_patterns(
        self, 
        patterns: List[Tuple[str, str]], 
        full_text: str, 
        content: str, 
        risk_reason: RiskReason
    ) -> Tuple[float, List[str]]:
        """Check patterns and return risk score and details"""
        found_details = []
        match_count = 0
        
        for pattern, description in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                match_count += len(matches)
                found_details.append(f"{description}: {len(matches)} matches")
        
        if match_count > 0:
            # Get base risk weight and scale by number of matches
            base_weight = self.risk_weights.get(risk_reason, 0.3)
            risk_score = min(base_weight * (1 + match_count * 0.1), 1.0)
            return risk_score, found_details
        
        return 0.0, []
    
    def _generate_suggestions(
        self, 
        risk_level: ContentRisk, 
        reasons: List[RiskReason], 
        url: str = ""
    ) -> List[str]:
        """Generate actionable suggestions based on risk assessment"""
        suggestions = []
        
        if risk_level == ContentRisk.BLOCKED:
            suggestions.extend([
                "Content blocked due to high security risk",
                "Manual review required before processing",
                "Consider excluding this URL from future crawls"
            ])
        elif risk_level == ContentRisk.SUSPICIOUS:
            suggestions.extend([
                "Content flagged for manual review",
                "Verify legitimacy before using in responses",
                "Monitor for similar patterns from this source"
            ])
        
        # Specific suggestions based on reasons
        reason_suggestions = {
            RiskReason.PASSWORD_FORM: "Content contains login forms - may be inappropriate for indexing",
            RiskReason.PAYMENT_FORM: "Payment forms detected - ensure this is legitimate commerce content",
            RiskReason.PERSONAL_DATA_FORM: "Personal data collection detected - verify privacy compliance",
            RiskReason.SCRIPT_INJECTION: "Script content detected - potential security risk",
            RiskReason.ACCOUNT_VERIFICATION: "Account verification request detected - verify source legitimacy",
            RiskReason.URGENT_ACTION: "Urgent action request detected - potential phishing",
            RiskReason.FAKE_SECURITY_ALERT: "Fake security alert detected - potential phishing",
            RiskReason.SPAM_CONTENT: "Spam-like content detected - may be low quality",
            RiskReason.EXCESSIVE_LINKS: "High link density - may be link farm or spam"
        }
        
        for reason in reasons:
            if reason in reason_suggestions:
                suggestions.append(reason_suggestions[reason])
        
        return suggestions
    
    def is_safe_for_indexing(self, validation_result: ValidationResult) -> bool:
        """
        Determine if content is safe for search indexing
        
        Args:
            validation_result: Result from validate_content()
            
        Returns:
            bool: True if safe to include in search results
        """
        if validation_result.risk_level == ContentRisk.BLOCKED:
            return False
        
        # Don't index forms or phishing content even if marked as suspicious
        dangerous_reasons = {
            RiskReason.PASSWORD_FORM,
            RiskReason.PAYMENT_FORM,
            RiskReason.PERSONAL_DATA_FORM,
            RiskReason.SCRIPT_INJECTION,
            RiskReason.FAKE_SECURITY_ALERT
        }
        
        return not any(reason in dangerous_reasons for reason in validation_result.reasons)


# Convenience functions
def validate_url_content(content: str, url: str, title: str = "") -> ValidationResult:
    """Convenience function to validate content from a URL"""
    validator = ContentSafetyValidator()
    return validator.validate_content(content, url, title)


def is_content_safe(content: str, url: str = "") -> bool:
    """Quick safety check for content"""
    result = validate_url_content(content, url)
    return result.safe_to_store and result.risk_level != ContentRisk.BLOCKED