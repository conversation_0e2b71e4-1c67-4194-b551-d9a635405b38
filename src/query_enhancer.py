"""
Simple query enhancement for better search relevance.
Focuses on technical documentation and API-related queries.
"""
import re
from typing import List, Dict, Set


class SimpleQueryEnhancer:
    """Simple query enhancement using predefined patterns and synonyms."""
    
    def __init__(self):
        # Technical synonyms for common terms
        self.synonyms = {
            'rate limit': ['rate limiting', 'throttling', 'quota', 'usage limits', 'request limits'],
            'api': ['rest api', 'api endpoint', 'web api', 'http api', 'service api'],
            'authentication': ['auth', 'oauth', 'jwt', 'token', 'credentials', 'login'],
            'authorization': ['access control', 'permissions', 'roles', 'scope'],
            'error': ['exception', 'failure', 'bug', 'issue', 'problem'],
            'configuration': ['config', 'setup', 'settings', 'options', 'parameters'],
            'installation': ['install', 'setup', 'deployment', 'getting started'],
            'example': ['demo', 'sample', 'tutorial', 'guide', 'walkthrough'],
            'documentation': ['docs', 'reference', 'manual', 'guide'],
            'performance': ['speed', 'optimization', 'efficiency', 'latency'],
        }
        
        # Common technical patterns
        self.patterns = {
            r'\b(\w+)\s+api\b': r'\1 API endpoint',
            r'\bhow\s+to\s+(\w+)\b': r'\1 tutorial guide',
            r'\berror\s+(\d+)\b': r'HTTP \1 error status',
            r'\b(\w+)\s+limit\b': r'\1 rate limiting quota',
        }
    
    def expand_query(self, query: str) -> List[str]:
        """Generate query variations for better matching."""
        variations = [query.strip()]
        query_lower = query.lower()
        
        # Add synonym variations
        for term, synonyms in self.synonyms.items():
            if term in query_lower:
                for synonym in synonyms:
                    # Replace each occurrence
                    new_variation = query_lower.replace(term, synonym)
                    if new_variation not in [v.lower() for v in variations]:
                        variations.append(new_variation)
        
        # Apply pattern transformations
        for pattern, replacement in self.patterns.items():
            match = re.search(pattern, query_lower)
            if match:
                new_variation = re.sub(pattern, replacement, query_lower)
                if new_variation not in [v.lower() for v in variations]:
                    variations.append(new_variation)
        
        # Add technical context variations
        variations.extend(self._add_context_variations(query))
        
        # Remove duplicates while preserving order
        seen = set()
        unique_variations = []
        for variation in variations:
            variation_lower = variation.lower()
            if variation_lower not in seen:
                seen.add(variation_lower)
                unique_variations.append(variation)
        
        return unique_variations[:5]  # Limit to 5 variations
    
    def _add_context_variations(self, query: str) -> List[str]:
        """Add context-aware variations based on query type."""
        variations = []
        query_lower = query.lower()
        
        # API-related queries
        if any(term in query_lower for term in ['api', 'endpoint', 'request']):
            variations.extend([
                f"{query} documentation",
                f"{query} example code",
                f"how to use {query}"
            ])
        
        # Error-related queries  
        elif any(term in query_lower for term in ['error', 'exception', 'fail']):
            variations.extend([
                f"{query} troubleshooting",
                f"fix {query}",
                f"{query} solution"
            ])
        
        # Configuration queries
        elif any(term in query_lower for term in ['config', 'setup', 'install']):
            variations.extend([
                f"{query} guide",
                f"{query} tutorial",
                f"getting started {query}"
            ])
        
        return variations
    
    def enhance_search_query(self, query: str) -> Dict[str, any]:
        """Create enhanced search parameters."""
        variations = self.expand_query(query)
        
        return {
            'primary_query': query,
            'variations': variations,
            'boost_terms': self._extract_boost_terms(query),
            'filter_suggestions': self._suggest_filters(query)
        }
    
    def _extract_boost_terms(self, query: str) -> List[str]:
        """Extract terms that should be boosted in search."""
        boost_terms = []
        query_lower = query.lower()
        
        # Technical terms that should be heavily weighted
        technical_terms = [
            'api', 'endpoint', 'authentication', 'authorization', 
            'rate limit', 'token', 'oauth', 'jwt', 'sdk',
            'webhook', 'callback', 'response', 'request'
        ]
        
        for term in technical_terms:
            if term in query_lower:
                boost_terms.append(term)
        
        return boost_terms
    
    def _suggest_filters(self, query: str) -> Dict[str, str]:
        """Suggest metadata filters based on query content."""
        filters = {}
        query_lower = query.lower()
        
        # Language-specific filters
        languages = ['python', 'javascript', 'java', 'go', 'rust', 'php', 'ruby']
        for lang in languages:
            if lang in query_lower:
                filters['language'] = lang
                break
        
        # Content type filters
        if any(term in query_lower for term in ['example', 'demo', 'tutorial']):
            filters['content_type'] = 'example'
        elif any(term in query_lower for term in ['reference', 'docs', 'documentation']):
            filters['content_type'] = 'reference'
        
        return filters