"""
Simple GitHub API client for better repository crawling.
Follows KISS principle - only essential functionality.
"""
import os
import requests
import logging
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class SimpleGitHubClient:
    """Simple GitHub API client with rate limiting awareness."""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token or os.getenv("GITHUB_TOKEN")
        self.base_url = "https://api.github.com"
        self.session = requests.Session()
        
        if self.token:
            self.session.headers.update({"Authorization": f"token {self.token}"})
        
    def is_github_url(self, url: str) -> bool:
        """Check if URL is a GitHub repository."""
        parsed = urlparse(url)
        return parsed.netloc.lower() in ['github.com', 'www.github.com']
    
    def extract_repo_info(self, url: str) -> Optional[tuple]:
        """Extract owner and repo name from GitHub URL."""
        if not self.is_github_url(url):
            return None
            
        parsed = urlparse(url)
        path_parts = parsed.path.strip('/').split('/')
        
        if len(path_parts) >= 2:
            return path_parts[0], path_parts[1]
        return None
    
    def get_repo_readme(self, owner: str, repo: str) -> Optional[str]:
        """Get repository README content."""
        try:
            url = f"{self.base_url}/repos/{owner}/{repo}/readme"
            response = self.session.get(url)
            
            if response.status_code == 200:
                readme_data = response.json()
                # Decode base64 content
                import base64
                content = base64.b64decode(readme_data['content']).decode('utf-8')
                return content
            else:
                logger.warning(f"README not found for {owner}/{repo}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching README for {owner}/{repo}: {e}")
            return None
    
    def get_repo_info(self, owner: str, repo: str) -> Optional[Dict[str, Any]]:
        """Get basic repository information."""
        try:
            url = f"{self.base_url}/repos/{owner}/{repo}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'name': data.get('name'),
                    'description': data.get('description'),
                    'language': data.get('language'),
                    'topics': data.get('topics', []),
                    'stars': data.get('stargazers_count', 0),
                    'url': data.get('html_url')
                }
            else:
                logger.warning(f"Repo info not found for {owner}/{repo}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching repo info for {owner}/{repo}: {e}")
            return None
    
    def get_rate_limit_info(self) -> Dict[str, Any]:
        """Get current rate limit status."""
        try:
            url = f"{self.base_url}/rate_limit"
            response = self.session.get(url)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Rate limit check failed: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return {"error": str(e)}


def create_github_enhanced_content(url: str, readme_content: str, repo_info: Dict[str, Any]) -> str:
    """Create enhanced content combining README and repo metadata."""
    enhanced_content = f"""# {repo_info.get('name', 'Repository')}

**Description**: {repo_info.get('description', 'No description available')}
**Primary Language**: {repo_info.get('language', 'Not specified')}
**Stars**: {repo_info.get('stars', 0)}
**Topics**: {', '.join(repo_info.get('topics', []))}
**URL**: {repo_info.get('url', url)}

---

{readme_content}
"""
    return enhanced_content