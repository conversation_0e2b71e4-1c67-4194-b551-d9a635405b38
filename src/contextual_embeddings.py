"""
Contextual embeddings implementation for enhanced RAG retrieval.
Inspired by ref-rag-project with OpenRouter multi-model support.
"""
import os
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ContextualConfig:
    """Configuration for contextual embeddings."""
    max_context_length: int = 2000
    chunk_overlap: int = 200
    max_context_chunks: int = 5
    enable_document_summary: bool = True
    context_template: str = "Document context: {context}\n\nChunk content: {chunk}"

class ContextualEmbeddingProcessor:
    """
    Processes document chunks with additional context for better embeddings.
    Uses LLM to enhance chunk understanding within document context.
    """
    
    def __init__(self, config: Optional[ContextualConfig] = None):
        self.config = config or ContextualConfig()
        self._llm_client = None
        
    async def _get_llm_client(self):
        """Get LLM client instance."""
        if self._llm_client is None:
            try:
                from llm_client import get_default_llm_client
                self._llm_client = get_default_llm_client()
            except ImportError:
                logger.error("LLM client not available for contextual embeddings")
                raise ValueError("Contextual embeddings require LLM client configuration")
        return self._llm_client
    
    def extract_document_context(
        self, 
        full_document: str, 
        target_chunk: str,
        chunk_position: int = 0
    ) -> str:
        """
        Extract relevant context from full document for a specific chunk.
        
        Args:
            full_document: Complete document text
            target_chunk: Specific chunk to enhance
            chunk_position: Position of chunk within document
            
        Returns:
            Context string to enhance chunk understanding
        """
        # Calculate context window around the chunk
        doc_length = len(full_document)
        chunk_start = full_document.find(target_chunk)
        
        if chunk_start == -1:
            # Chunk not found, use position-based context
            chars_per_chunk = doc_length // max(1, self.config.max_context_chunks)
            chunk_start = chunk_position * chars_per_chunk
        
        # Calculate context boundaries
        context_start = max(0, chunk_start - self.config.max_context_length // 2)
        context_end = min(doc_length, chunk_start + len(target_chunk) + self.config.max_context_length // 2)
        
        # Extract context with overlap
        context = full_document[context_start:context_end]
        
        # Clean up context (remove incomplete sentences at boundaries)
        context = self._clean_context_boundaries(context, target_chunk)
        
        return context[:self.config.max_context_length]
    
    def _clean_context_boundaries(self, context: str, target_chunk: str) -> str:
        """Clean context boundaries to avoid incomplete sentences."""
        # Find sentence boundaries
        sentences = context.split('. ')
        if len(sentences) <= 2:
            return context
        
        # Remove first incomplete sentence if it doesn't start with capital
        if sentences[0] and not sentences[0][0].isupper():
            sentences = sentences[1:]
        
        # Remove last incomplete sentence if it doesn't end properly
        if sentences[-1] and not sentences[-1].endswith(('.', '!', '?')):
            sentences = sentences[:-1]
        
        return '. '.join(sentences)
    
    async def generate_contextual_content(
        self,
        full_document: str,
        chunk: str,
        chunk_metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Generate enhanced chunk content with document context.
        
        Args:
            full_document: Complete document text
            chunk: Original chunk text
            chunk_metadata: Optional metadata about the chunk
            
        Returns:
            Tuple of (enhanced_content, enhanced_metadata)
        """
        try:
            # Extract relevant context
            context = self.extract_document_context(full_document, chunk)
            
            # Prepare contextual prompt
            llm_client = await self._get_llm_client()
            
            # Create context-aware prompt
            prompt = self._create_context_prompt(context, chunk, chunk_metadata)
            
            # Generate enhanced content
            response = await llm_client.generate_async(
                prompt=prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            enhanced_content = response.get('content', chunk)
            
            # Create enhanced metadata
            enhanced_metadata = {
                **(chunk_metadata or {}),
                'has_contextual_embedding': True,
                'context_length': len(context),
                'enhancement_method': 'llm_contextual'
            }
            
            return enhanced_content, enhanced_metadata
            
        except Exception as e:
            logger.warning(f"Contextual embedding failed: {e}, using original chunk")
            return chunk, chunk_metadata or {}
    
    def _create_context_prompt(
        self,
        context: str,
        chunk: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create prompt for contextual enhancement."""
        
        # Base prompt for context enhancement
        base_prompt = """You are helping to enhance a text chunk for better search retrieval by providing additional context.

Given the document context and a specific chunk, provide a brief contextual summary that would help someone understand what this chunk is about within the larger document.

Instructions:
1. Keep the summary concise (1-2 sentences)
2. Focus on what makes this chunk unique or important
3. Include relevant technical terms or concepts
4. Don't repeat the chunk content verbatim

Document Context:
{context}

Specific Chunk:
{chunk}

Contextual Summary:"""
        
        return base_prompt.format(
            context=context[:1500],  # Limit context length
            chunk=chunk[:500]  # Limit chunk length
        )
    
    async def process_document_chunks(
        self,
        full_document: str,
        chunks: List[str],
        metadata_list: Optional[List[Dict[str, Any]]] = None
    ) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Process multiple chunks with contextual enhancement.
        
        Args:
            full_document: Complete document text
            chunks: List of chunk texts
            metadata_list: Optional list of metadata for each chunk
            
        Returns:
            List of (enhanced_content, enhanced_metadata) tuples
        """
        if not chunks:
            return []
        
        if metadata_list is None:
            metadata_list = [{}] * len(chunks)
        
        # Process chunks with rate limiting
        results = []
        batch_size = 5  # Process in batches to avoid overwhelming LLM API
        
        for i in range(0, len(chunks), batch_size):
            batch_chunks = chunks[i:i + batch_size]
            batch_metadata = metadata_list[i:i + batch_size]
            
            # Process batch concurrently
            batch_tasks = [
                self.generate_contextual_content(full_document, chunk, metadata)
                for chunk, metadata in zip(batch_chunks, batch_metadata)
            ]
            
            try:
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Handle results and exceptions
                for j, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        logger.warning(f"Contextual processing failed for chunk {i+j}: {result}")
                        # Use original chunk as fallback
                        original_chunk = batch_chunks[j]
                        original_metadata = batch_metadata[j]
                        results.append((original_chunk, original_metadata))
                    else:
                        results.append(result)
                        
            except Exception as e:
                logger.error(f"Batch contextual processing failed: {e}")
                # Fallback to original chunks
                for chunk, metadata in zip(batch_chunks, batch_metadata):
                    results.append((chunk, metadata))
            
            # Add small delay between batches to avoid rate limits
            if i + batch_size < len(chunks):
                await asyncio.sleep(0.5)
        
        logger.info(f"Processed {len(results)}/{len(chunks)} chunks with contextual enhancement")
        return results

class SimpleContextualProcessor:
    """
    Simplified contextual processor that doesn't require LLM calls.
    Uses heuristic-based context extraction.
    """
    
    def __init__(self, config: Optional[ContextualConfig] = None):
        self.config = config or ContextualConfig()
    
    def extract_surrounding_context(
        self,
        full_document: str,
        chunk: str,
        context_size: int = 500
    ) -> str:
        """Extract surrounding text context without LLM processing."""
        chunk_start = full_document.find(chunk)
        if chunk_start == -1:
            return ""
        
        # Get surrounding context
        start = max(0, chunk_start - context_size)
        end = min(len(full_document), chunk_start + len(chunk) + context_size)
        
        return full_document[start:end]
    
    def enhance_chunk_with_headers(
        self,
        full_document: str,
        chunk: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Enhance chunk by adding relevant headers and context."""
        # Find headers in the document
        headers = self._extract_headers(full_document)
        
        # Find most relevant header for this chunk
        relevant_header = self._find_relevant_header(full_document, chunk, headers)
        
        # Create enhanced content
        if relevant_header:
            enhanced_content = f"[Section: {relevant_header}] {chunk}"
        else:
            enhanced_content = chunk
        
        # Enhanced metadata
        enhanced_metadata = {
            **(metadata or {}),
            'has_contextual_embedding': True,
            'relevant_header': relevant_header,
            'enhancement_method': 'heuristic_headers'
        }
        
        return enhanced_content, enhanced_metadata
    
    def _extract_headers(self, document: str) -> List[str]:
        """Extract markdown headers from document."""
        import re
        
        # Find markdown headers
        header_pattern = r'^(#{1,6})\s+(.+)$'
        headers = []
        
        for line in document.split('\n'):
            match = re.match(header_pattern, line.strip())
            if match:
                level = len(match.group(1))
                title = match.group(2).strip()
                headers.append((level, title, line))
        
        return headers
    
    def _find_relevant_header(
        self,
        document: str,
        chunk: str,
        headers: List[Tuple[int, str, str]]
    ) -> Optional[str]:
        """Find the most relevant header for a chunk."""
        chunk_position = document.find(chunk)
        if chunk_position == -1 or not headers:
            return None
        
        # Find the closest preceding header
        best_header = None
        best_distance = float('inf')
        
        for level, title, header_line in headers:
            header_position = document.find(header_line)
            if header_position < chunk_position:
                distance = chunk_position - header_position
                if distance < best_distance:
                    best_distance = distance
                    best_header = title
        
        return best_header

def get_contextual_processor(use_llm: bool = True) -> ContextualEmbeddingProcessor:
    """Get appropriate contextual processor based on configuration."""
    if use_llm and os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "false") == "true":
        try:
            return ContextualEmbeddingProcessor()
        except Exception as e:
            logger.warning(f"LLM contextual processor unavailable: {e}, using simple processor")
            return SimpleContextualProcessor()
    else:
        return SimpleContextualProcessor()

async def process_chunk_with_context_async(
    full_document: str,
    chunk: str,
    metadata: Optional[Dict[str, Any]] = None,
    use_llm: bool = True
) -> Tuple[str, Dict[str, Any]]:
    """
    Async wrapper for processing a single chunk with context.
    
    Args:
        full_document: Complete document text
        chunk: Chunk to enhance
        metadata: Optional chunk metadata
        use_llm: Whether to use LLM-based enhancement
        
    Returns:
        Tuple of (enhanced_content, enhanced_metadata)
    """
    processor = get_contextual_processor(use_llm)
    
    if isinstance(processor, ContextualEmbeddingProcessor):
        return await processor.generate_contextual_content(full_document, chunk, metadata)
    else:
        # Simple processor - synchronous
        return processor.enhance_chunk_with_headers(full_document, chunk, metadata)