"""
Redis-based Job Queue System for AY RAG MCP Server
Implements async job processing with status tracking and result storage
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union
import redis.asyncio as redis
import logging

logger = logging.getLogger(__name__)

class JobStatus(Enum):
    """Job status enumeration"""
    QUEUED = "queued"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class JobRequest:
    """Job request data structure"""
    job_id: str
    job_type: str  # 'smart_crawl', 'single_crawl', etc.
    url: str
    parameters: Dict[str, Any]
    created_at: float
    priority: int = 5  # 1-10, lower = higher priority
    max_retries: int = 3
    timeout: int = 3600  # 1 hour default
    
    @classmethod
    def create(cls, job_type: str, url: str, parameters: Dict[str, Any], 
               priority: int = 5, max_retries: int = 3, timeout: int = 3600) -> 'JobRequest':
        """Create a new job request with generated ID"""
        return cls(
            job_id=str(uuid.uuid4()),
            job_type=job_type,
            url=url,
            parameters=parameters,
            created_at=time.time(),
            priority=priority,
            max_retries=max_retries,
            timeout=timeout
        )

@dataclass 
class JobResult:
    """Job result data structure"""
    job_id: str
    status: JobStatus
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        data = {
            "job_id": self.job_id,
            "status": self.status.value,
            "retry_count": self.retry_count
        }
        if self.result_data:
            data["result"] = self.result_data
        if self.error_message:
            data["error"] = self.error_message
        if self.started_at:
            data["started_at"] = datetime.fromtimestamp(self.started_at).isoformat()
        if self.completed_at:
            data["completed_at"] = datetime.fromtimestamp(self.completed_at).isoformat()
            if self.started_at:
                data["duration_seconds"] = round(self.completed_at - self.started_at, 2)
        return data

class RedisJobQueue:
    """Redis-based job queue with status tracking"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 queue_prefix: str = "ay-rag"):
        self.redis_url = redis_url
        self.prefix = queue_prefix
        self.redis_pool = None
        
        # Redis key patterns
        self.job_queue_key = f"{self.prefix}:queue:crawl"
        self.job_processing_key = f"{self.prefix}:processing"
        self.job_data_pattern = f"{self.prefix}:job:{{}}"
        self.job_result_pattern = f"{self.prefix}:result:{{}}"
        self.job_status_pattern = f"{self.prefix}:status:{{}}"
        
    async def connect(self):
        """Initialize Redis connection pool"""
        if not self.redis_pool:
            self.redis_pool = redis.ConnectionPool.from_url(
                self.redis_url,
                decode_responses=True,
                max_connections=10
            )
            logger.info(f"Connected to Redis at {self.redis_url}")
    
    async def disconnect(self):
        """Close Redis connection pool"""
        if self.redis_pool:
            await self.redis_pool.disconnect()
            self.redis_pool = None
            logger.info("Disconnected from Redis")
    
    def _get_redis_client(self) -> redis.Redis:
        """Get Redis client instance"""
        if not self.redis_pool:
            raise RuntimeError("Redis not connected. Call connect() first.")
        return redis.Redis(connection_pool=self.redis_pool)
    
    async def enqueue_job(self, job_request: JobRequest) -> str:
        """Add job to queue and return job ID"""
        redis_client = self._get_redis_client()
        
        try:
            # Store job data
            job_key = self.job_data_pattern.format(job_request.job_id)
            job_data = {
                **asdict(job_request),
                "status": JobStatus.QUEUED.value,
                "enqueued_at": time.time()
            }
            
            # Use pipeline for atomic operation
            async with redis_client.pipeline() as pipe:
                # Store job data as hash
                await pipe.hset(job_key, mapping={
                    k: json.dumps(v) if isinstance(v, (dict, list)) else str(v) 
                    for k, v in job_data.items()
                })
                
                # Set job expiration (24 hours)
                await pipe.expire(job_key, 86400)
                
                # Add to priority queue (lower score = higher priority)
                priority_score = job_request.priority * 1000 + job_request.created_at
                await pipe.zadd(self.job_queue_key, {job_request.job_id: priority_score})
                
                # Set initial status
                status_key = self.job_status_pattern.format(job_request.job_id)
                await pipe.set(status_key, JobStatus.QUEUED.value, ex=86400)
                
                await pipe.execute()
            
            logger.info(f"Enqueued job {job_request.job_id} for URL: {job_request.url}")
            return job_request.job_id
            
        except Exception as e:
            logger.error(f"Failed to enqueue job {job_request.job_id}: {e}")
            raise
        finally:
            await redis_client.aclose()
    
    async def dequeue_job(self) -> Optional[JobRequest]:
        """Get next job from queue for processing"""
        redis_client = self._get_redis_client()
        
        try:
            # Get highest priority job atomically
            async with redis_client.pipeline() as pipe:
                while True:
                    try:
                        # Watch the queue for changes
                        await pipe.watch(self.job_queue_key)
                        
                        # Get job with lowest priority score
                        job_items = await pipe.zrange(self.job_queue_key, 0, 0, withscores=True)
                        if not job_items:
                            return None
                        
                        job_id = job_items[0][0]
                        
                        # Start atomic transaction
                        pipe.multi()
                        
                        # Move job from queue to processing
                        await pipe.zrem(self.job_queue_key, job_id)
                        await pipe.sadd(self.job_processing_key, job_id)
                        
                        # Update status
                        status_key = self.job_status_pattern.format(job_id)
                        await pipe.set(status_key, JobStatus.PROCESSING.value, ex=86400)
                        
                        await pipe.execute()
                        break
                        
                    except redis.WatchError:
                        # Retry if queue was modified by another worker
                        continue
            
            # Get job data
            job_key = self.job_data_pattern.format(job_id)
            job_data = await redis_client.hgetall(job_key)
            
            if not job_data:
                logger.warning(f"Job data not found for {job_id}")
                return None
            
            # Parse job data
            parsed_data = {}
            for k, v in job_data.items():
                try:
                    # Try to parse as JSON first
                    parsed_data[k] = json.loads(v)
                except (json.JSONDecodeError, TypeError):
                    # Fall back to string
                    parsed_data[k] = v
            
            # Convert to JobRequest
            job_request = JobRequest(**{
                k: v for k, v in parsed_data.items() 
                if k in JobRequest.__dataclass_fields__
            })
            
            logger.info(f"Dequeued job {job_id} for processing")
            return job_request
            
        except Exception as e:
            logger.error(f"Failed to dequeue job: {e}")
            return None
        finally:
            await redis_client.aclose()
    
    async def update_job_status(self, job_id: str, status: JobStatus, 
                               result_data: Optional[Dict[str, Any]] = None,
                               error_message: Optional[str] = None) -> bool:
        """Update job status and optionally store results"""
        redis_client = self._get_redis_client()
        
        try:
            current_time = time.time()
            
            # Create result object
            job_result = JobResult(
                job_id=job_id,
                status=status,
                result_data=result_data,
                error_message=error_message,
                completed_at=current_time if status in [JobStatus.COMPLETED, JobStatus.FAILED] else None
            )
            
            async with redis_client.pipeline() as pipe:
                # Update status
                status_key = self.job_status_pattern.format(job_id)
                await pipe.set(status_key, status.value, ex=86400)
                
                # Store result data
                result_key = self.job_result_pattern.format(job_id)
                result_dict = job_result.to_dict()
                await pipe.hset(result_key, mapping={
                    k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                    for k, v in result_dict.items()
                })
                await pipe.expire(result_key, 86400)
                
                # Remove from processing set if completed/failed
                if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                    await pipe.srem(self.job_processing_key, job_id)
                
                await pipe.execute()
            
            logger.info(f"Updated job {job_id} status to {status.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update job {job_id} status: {e}")
            return False
        finally:
            await redis_client.aclose()
    
    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get current job status and results"""
        redis_client = self._get_redis_client()
        
        try:
            # Get result data
            result_key = self.job_result_pattern.format(job_id)
            result_data = await redis_client.hgetall(result_key)
            
            if not result_data:
                # Check if job exists at all
                status_key = self.job_status_pattern.format(job_id)
                status = await redis_client.get(status_key)
                if status:
                    return JobResult(job_id=job_id, status=JobStatus(status))
                return None
            
            # Parse result data
            parsed_result = {}
            for k, v in result_data.items():
                try:
                    parsed_result[k] = json.loads(v)
                except (json.JSONDecodeError, TypeError):
                    parsed_result[k] = v
            
            return JobResult(
                job_id=job_id,
                status=JobStatus(parsed_result.get("status", "unknown")),
                result_data=parsed_result.get("result"),
                error_message=parsed_result.get("error"),
                started_at=float(parsed_result["started_at"]) if parsed_result.get("started_at") else None,
                completed_at=float(parsed_result["completed_at"]) if parsed_result.get("completed_at") else None,
                retry_count=int(parsed_result.get("retry_count", 0))
            )
            
        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return None
        finally:
            await redis_client.aclose()
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        redis_client = self._get_redis_client()
        
        try:
            async with redis_client.pipeline() as pipe:
                await pipe.zcard(self.job_queue_key)  # Queued jobs
                await pipe.scard(self.job_processing_key)  # Processing jobs
                results = await pipe.execute()
            
            return {
                "queued_jobs": results[0],
                "processing_jobs": results[1],
                "total_active_jobs": results[0] + results[1]
            }
            
        except Exception as e:
            logger.error(f"Failed to get queue stats: {e}")
            return {"error": str(e)}
        finally:
            await redis_client.aclose()
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a queued job"""
        redis_client = self._get_redis_client()
        
        try:
            # Check if job is still queued
            is_queued = await redis_client.zscore(self.job_queue_key, job_id)
            
            if is_queued is not None:
                # Remove from queue and mark as cancelled
                async with redis_client.pipeline() as pipe:
                    await pipe.zrem(self.job_queue_key, job_id)
                    await pipe.execute()
                
                await self.update_job_status(job_id, JobStatus.CANCELLED)
                logger.info(f"Cancelled queued job {job_id}")
                return True
            
            logger.warning(f"Job {job_id} not found in queue or already processing")
            return False
            
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False
        finally:
            await redis_client.aclose()
    
    async def cleanup_expired_jobs(self, max_age_hours: int = 24) -> int:
        """Clean up old completed jobs"""
        redis_client = self._get_redis_client()
        cleaned_count = 0
        
        try:
            # Get all processing jobs to check for timeouts
            processing_jobs = await redis_client.smembers(self.job_processing_key)
            current_time = time.time()
            
            for job_id in processing_jobs:
                job_key = self.job_data_pattern.format(job_id)
                job_data = await redis_client.hget(job_key, "enqueued_at")
                
                if job_data:
                    try:
                        enqueued_at = float(job_data)
                        if current_time - enqueued_at > 3600:  # 1 hour timeout
                            await self.update_job_status(job_id, JobStatus.FAILED, 
                                                       error_message="Job timed out")
                            cleaned_count += 1
                            logger.info(f"Marked timed out job {job_id} as failed")
                    except ValueError:
                        continue
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired jobs: {e}")
            return 0
        finally:
            await redis_client.aclose()

# Global job queue instance
job_queue: Optional[RedisJobQueue] = None

async def get_job_queue() -> RedisJobQueue:
    """Get or create job queue instance"""
    global job_queue
    if job_queue is None:
        import os
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        job_queue = RedisJobQueue(redis_url)
        await job_queue.connect()
    return job_queue

async def shutdown_job_queue():
    """Shutdown job queue connection"""
    global job_queue
    if job_queue:
        await job_queue.disconnect()
        job_queue = None