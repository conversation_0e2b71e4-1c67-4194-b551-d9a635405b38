"""
Performance Monitoring for Content Type Classification and Adaptive Filtering

This module provides comprehensive performance monitoring, metrics collection,
and analytics for the adaptive content type classification system.
"""

import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics


@dataclass
class FilteringMetrics:
    """Metrics for a single filtering operation"""
    timestamp: float
    content_type: str
    strategy: str
    classification_confidence: float
    filter_confidence: float
    adaptive_confidence: float
    was_filtered: bool
    review_recommended: bool
    processing_time_ms: float
    content_length: int
    url_domain: str = ""
    

@dataclass
class PerformanceSnapshot:
    """Performance snapshot for a time period"""
    start_time: float
    end_time: float
    total_processed: int
    total_filtered: int
    total_reviewed: int
    avg_processing_time_ms: float
    avg_classification_confidence: float
    avg_filter_confidence: float
    content_type_distribution: Dict[str, int]
    strategy_distribution: Dict[str, int]
    filter_effectiveness: Dict[str, float]


class PerformanceMonitor:
    """
    Monitors and analyzes performance of adaptive content type classification
    and filtering system with real-time metrics and historical analysis.
    """
    
    def __init__(self, max_history_size: int = 10000, 
                 snapshot_interval_minutes: int = 60):
        self.max_history_size = max_history_size
        self.snapshot_interval = snapshot_interval_minutes * 60  # Convert to seconds
        
        # Real-time metrics storage
        self.metrics_history: deque = deque(maxlen=max_history_size)
        self.performance_snapshots: List[PerformanceSnapshot] = []
        
        # Aggregated statistics
        self.stats = {
            'total_operations': 0,
            'total_processing_time': 0.0,
            'content_type_counts': defaultdict(int),
            'strategy_counts': defaultdict(int),
            'filter_outcomes': defaultdict(int),
            'domain_performance': defaultdict(list),
            'hourly_volumes': defaultdict(int),
            'error_counts': defaultdict(int)
        }
        
        # Performance thresholds for alerting
        self.thresholds = {
            'max_processing_time_ms': 1000,  # 1 second
            'min_classification_confidence': 0.3,
            'max_review_rate': 0.15,  # 15%
            'min_filter_effectiveness': 0.8  # 80%
        }
        
        # Last snapshot time
        self.last_snapshot_time = time.time()
    
    def record_filtering_operation(self, 
                                 classification_result: Any,
                                 filter_result: Any,
                                 processing_time_ms: float,
                                 content_length: int,
                                 url: str = "",
                                 was_filtered: bool = False,
                                 review_recommended: bool = False) -> None:
        """Record metrics for a single filtering operation"""
        
        # Extract domain from URL
        domain = ""
        if url:
            try:
                from urllib.parse import urlparse
                domain = urlparse(url).netloc
            except Exception:
                domain = "unknown"
        
        # Create metrics record
        metrics = FilteringMetrics(
            timestamp=time.time(),
            content_type=getattr(classification_result, 'content_type', 'unknown'),
            strategy=getattr(classification_result, 'strategy', 'unknown'),
            classification_confidence=getattr(classification_result, 'confidence_score', 0.0),
            filter_confidence=getattr(filter_result, 'confidence_score', 0.0),
            adaptive_confidence=getattr(filter_result, 'adaptive_confidence', 0.0),
            was_filtered=was_filtered,
            review_recommended=review_recommended,
            processing_time_ms=processing_time_ms,
            content_length=content_length,
            url_domain=domain
        )
        
        # Store in history
        self.metrics_history.append(metrics)
        
        # Update aggregated stats
        self._update_aggregate_stats(metrics)
        
        # Check if we need to create a performance snapshot
        current_time = time.time()
        if current_time - self.last_snapshot_time >= self.snapshot_interval:
            self._create_performance_snapshot()
            self.last_snapshot_time = current_time
    
    def _update_aggregate_stats(self, metrics: FilteringMetrics) -> None:
        """Update aggregated statistics with new metrics"""
        self.stats['total_operations'] += 1
        self.stats['total_processing_time'] += metrics.processing_time_ms
        
        # Content type and strategy tracking
        content_type = getattr(metrics, 'content_type', 'unknown')
        strategy = getattr(metrics, 'strategy', 'unknown')
        
        self.stats['content_type_counts'][content_type] += 1
        self.stats['strategy_counts'][strategy] += 1
        
        # Filter outcomes
        if metrics.was_filtered:
            self.stats['filter_outcomes']['filtered'] += 1
        else:
            self.stats['filter_outcomes']['passed'] += 1
        
        if metrics.review_recommended:
            self.stats['filter_outcomes']['reviewed'] += 1
        
        # Domain performance
        self.stats['domain_performance'][metrics.url_domain].append({
            'classification_confidence': metrics.classification_confidence,
            'filter_confidence': metrics.filter_confidence,
            'processing_time': metrics.processing_time_ms
        })
        
        # Hourly volume tracking
        hour_key = datetime.fromtimestamp(metrics.timestamp).strftime('%Y-%m-%d %H:00')
        self.stats['hourly_volumes'][hour_key] += 1
    
    def _create_performance_snapshot(self) -> None:
        """Create a performance snapshot for the recent time period"""
        if not self.metrics_history:
            return
        
        current_time = time.time()
        cutoff_time = current_time - self.snapshot_interval
        
        # Filter recent metrics
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return
        
        # Calculate snapshot metrics
        total_processed = len(recent_metrics)
        total_filtered = sum(1 for m in recent_metrics if m.was_filtered)
        total_reviewed = sum(1 for m in recent_metrics if m.review_recommended)
        
        processing_times = [m.processing_time_ms for m in recent_metrics]
        avg_processing_time = statistics.mean(processing_times) if processing_times else 0.0
        
        classification_confidences = [m.classification_confidence for m in recent_metrics]
        avg_classification_confidence = statistics.mean(classification_confidences) if classification_confidences else 0.0
        
        filter_confidences = [m.filter_confidence for m in recent_metrics]
        avg_filter_confidence = statistics.mean(filter_confidences) if filter_confidences else 0.0
        
        # Content type and strategy distributions
        content_type_dist = defaultdict(int)
        strategy_dist = defaultdict(int)
        
        for m in recent_metrics:
            content_type_dist[m.content_type] += 1
            strategy_dist[m.strategy] += 1
        
        # Filter effectiveness by strategy
        filter_effectiveness = {}
        for strategy in strategy_dist.keys():
            strategy_metrics = [m for m in recent_metrics if m.strategy == strategy]
            if strategy_metrics:
                passed = sum(1 for m in strategy_metrics if not m.was_filtered)
                effectiveness = passed / len(strategy_metrics)
                filter_effectiveness[strategy] = effectiveness
        
        # Create snapshot
        snapshot = PerformanceSnapshot(
            start_time=cutoff_time,
            end_time=current_time,
            total_processed=total_processed,
            total_filtered=total_filtered,
            total_reviewed=total_reviewed,
            avg_processing_time_ms=avg_processing_time,
            avg_classification_confidence=avg_classification_confidence,
            avg_filter_confidence=avg_filter_confidence,
            content_type_distribution=dict(content_type_dist),
            strategy_distribution=dict(strategy_dist),
            filter_effectiveness=filter_effectiveness
        )
        
        self.performance_snapshots.append(snapshot)
        
        # Keep only recent snapshots (last 24 hours)
        cutoff_snapshot_time = current_time - (24 * 3600)
        self.performance_snapshots = [
            s for s in self.performance_snapshots 
            if s.end_time >= cutoff_snapshot_time
        ]
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time performance metrics"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        # Recent metrics (last 5 minutes)
        current_time = time.time()
        recent_cutoff = current_time - 300  # 5 minutes
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= recent_cutoff]
        
        if not recent_metrics:
            return {"status": "no_recent_data"}
        
        # Calculate real-time stats
        total_recent = len(recent_metrics)
        filtered_recent = sum(1 for m in recent_metrics if m.was_filtered)
        reviewed_recent = sum(1 for m in recent_metrics if m.review_recommended)
        
        processing_times = [m.processing_time_ms for m in recent_metrics]
        avg_processing_time = statistics.mean(processing_times) if processing_times else 0.0
        max_processing_time = max(processing_times) if processing_times else 0.0
        
        classification_confidences = [m.classification_confidence for m in recent_metrics]
        avg_classification_confidence = statistics.mean(classification_confidences) if classification_confidences else 0.0
        
        return {
            "status": "active",
            "time_window_minutes": 5,
            "metrics": {
                "total_processed": total_recent,
                "filter_rate": filtered_recent / total_recent if total_recent > 0 else 0,
                "review_rate": reviewed_recent / total_recent if total_recent > 0 else 0,
                "avg_processing_time_ms": avg_processing_time,
                "max_processing_time_ms": max_processing_time,
                "avg_classification_confidence": avg_classification_confidence,
                "operations_per_minute": total_recent / 5
            }
        }
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the specified time period"""
        current_time = time.time()
        cutoff_time = current_time - (hours * 3600)
        
        # Filter metrics for the time period
        period_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not period_metrics:
            return {"status": "no_data", "time_period_hours": hours}
        
        # Calculate summary statistics
        total_processed = len(period_metrics)
        total_filtered = sum(1 for m in period_metrics if m.was_filtered)
        total_reviewed = sum(1 for m in period_metrics if m.review_recommended)
        
        # Performance metrics
        processing_times = [m.processing_time_ms for m in period_metrics]
        avg_processing_time = statistics.mean(processing_times)
        p95_processing_time = statistics.quantiles(processing_times, n=20)[18] if len(processing_times) > 20 else max(processing_times)
        
        # Confidence metrics
        classification_confidences = [m.classification_confidence for m in period_metrics]
        filter_confidences = [m.filter_confidence for m in period_metrics]
        
        avg_classification_confidence = statistics.mean(classification_confidences)
        avg_filter_confidence = statistics.mean(filter_confidences)
        
        # Content type analysis
        content_type_stats = defaultdict(lambda: {'count': 0, 'filtered': 0, 'reviewed': 0})
        strategy_stats = defaultdict(lambda: {'count': 0, 'filtered': 0, 'reviewed': 0})
        
        for m in period_metrics:
            content_type_stats[m.content_type]['count'] += 1
            strategy_stats[m.strategy]['count'] += 1
            
            if m.was_filtered:
                content_type_stats[m.content_type]['filtered'] += 1
                strategy_stats[m.strategy]['filtered'] += 1
            
            if m.review_recommended:
                content_type_stats[m.content_type]['reviewed'] += 1
                strategy_stats[m.strategy]['reviewed'] += 1
        
        # Calculate effectiveness rates
        for stats in [content_type_stats, strategy_stats]:
            for key, data in stats.items():
                if data['count'] > 0:
                    data['filter_rate'] = data['filtered'] / data['count']
                    data['review_rate'] = data['reviewed'] / data['count']
                    data['effectiveness'] = (data['count'] - data['filtered']) / data['count']
        
        return {
            "status": "success",
            "time_period_hours": hours,
            "summary": {
                "total_processed": total_processed,
                "total_filtered": total_filtered,
                "total_reviewed": total_reviewed,
                "filter_rate": total_filtered / total_processed,
                "review_rate": total_reviewed / total_processed,
                "avg_processing_time_ms": avg_processing_time,
                "p95_processing_time_ms": p95_processing_time,
                "avg_classification_confidence": avg_classification_confidence,
                "avg_filter_confidence": avg_filter_confidence
            },
            "content_type_analysis": dict(content_type_stats),
            "strategy_analysis": dict(strategy_stats)
        }
    
    def get_performance_alerts(self) -> List[Dict[str, Any]]:
        """Check for performance issues and return alerts"""
        alerts = []
        
        if not self.metrics_history:
            return alerts
        
        # Get recent metrics (last 10 minutes)
        current_time = time.time()
        recent_cutoff = current_time - 600  # 10 minutes
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= recent_cutoff]
        
        if not recent_metrics:
            return alerts
        
        # Check processing time
        processing_times = [m.processing_time_ms for m in recent_metrics]
        avg_processing_time = statistics.mean(processing_times)
        max_processing_time = max(processing_times)
        
        if avg_processing_time > self.thresholds['max_processing_time_ms']:
            alerts.append({
                'type': 'high_processing_time',
                'severity': 'warning',
                'message': f'Average processing time ({avg_processing_time:.1f}ms) exceeds threshold ({self.thresholds["max_processing_time_ms"]}ms)',
                'current_value': avg_processing_time,
                'threshold': self.thresholds['max_processing_time_ms']
            })
        
        if max_processing_time > self.thresholds['max_processing_time_ms'] * 2:
            alerts.append({
                'type': 'very_high_processing_time',
                'severity': 'critical',
                'message': f'Maximum processing time ({max_processing_time:.1f}ms) is extremely high',
                'current_value': max_processing_time,
                'threshold': self.thresholds['max_processing_time_ms'] * 2
            })
        
        # Check classification confidence
        classification_confidences = [m.classification_confidence for m in recent_metrics]
        avg_classification_confidence = statistics.mean(classification_confidences)
        
        if avg_classification_confidence < self.thresholds['min_classification_confidence']:
            alerts.append({
                'type': 'low_classification_confidence',
                'severity': 'warning',
                'message': f'Average classification confidence ({avg_classification_confidence:.2f}) is below threshold ({self.thresholds["min_classification_confidence"]})',
                'current_value': avg_classification_confidence,
                'threshold': self.thresholds['min_classification_confidence']
            })
        
        # Check review rate
        total_recent = len(recent_metrics)
        reviewed_recent = sum(1 for m in recent_metrics if m.review_recommended)
        review_rate = reviewed_recent / total_recent if total_recent > 0 else 0
        
        if review_rate > self.thresholds['max_review_rate']:
            alerts.append({
                'type': 'high_review_rate',
                'severity': 'warning',
                'message': f'Review rate ({review_rate:.1%}) exceeds threshold ({self.thresholds["max_review_rate"]:.1%})',
                'current_value': review_rate,
                'threshold': self.thresholds['max_review_rate']
            })
        
        return alerts
    
    def export_metrics(self, filename: str, format: str = 'json') -> bool:
        """Export metrics to file"""
        try:
            data = {
                'export_timestamp': time.time(),
                'metrics_count': len(self.metrics_history),
                'snapshots_count': len(self.performance_snapshots),
                'metrics': [asdict(m) for m in self.metrics_history],
                'snapshots': [asdict(s) for s in self.performance_snapshots],
                'aggregate_stats': dict(self.stats)
            }
            
            if format.lower() == 'json':
                with open(filename, 'w') as f:
                    json.dump(data, f, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            return True
        except Exception as e:
            print(f"Error exporting metrics: {e}")
            return False
    
    def reset_metrics(self) -> None:
        """Reset all metrics and statistics"""
        self.metrics_history.clear()
        self.performance_snapshots.clear()
        self.stats = {
            'total_operations': 0,
            'total_processing_time': 0.0,
            'content_type_counts': defaultdict(int),
            'strategy_counts': defaultdict(int),
            'filter_outcomes': defaultdict(int),
            'domain_performance': defaultdict(list),
            'hourly_volumes': defaultdict(int),
            'error_counts': defaultdict(int)
        }
        self.last_snapshot_time = time.time()
    
    def get_domain_analysis(self, min_samples: int = 10) -> Dict[str, Any]:
        """Analyze performance by domain"""
        domain_analysis = {}
        
        for domain, performance_data in self.stats['domain_performance'].items():
            if len(performance_data) >= min_samples:
                classification_confidences = [d['classification_confidence'] for d in performance_data]
                filter_confidences = [d['filter_confidence'] for d in performance_data]
                processing_times = [d['processing_time'] for d in performance_data]
                
                domain_analysis[domain] = {
                    'sample_count': len(performance_data),
                    'avg_classification_confidence': statistics.mean(classification_confidences),
                    'avg_filter_confidence': statistics.mean(filter_confidences),
                    'avg_processing_time_ms': statistics.mean(processing_times),
                    'confidence_std_dev': statistics.stdev(classification_confidences) if len(classification_confidences) > 1 else 0
                }
        
        return domain_analysis