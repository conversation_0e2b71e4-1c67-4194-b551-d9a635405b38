"""
Advanced RAG configuration system with modular feature flags.
Inspired by ref-rag-project architecture with enhancements.
"""
import os
import logging
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)

class RAGStrategy(Enum):
    """Available RAG strategies."""
    BASIC = "basic"
    CONTEXTUAL_EMBEDDINGS = "contextual_embeddings"
    HYBRID_SEARCH = "hybrid_search"
    AGENTIC_RAG = "agentic_rag"
    RERANKING = "reranking"
    KNOWLEDGE_GRAPH = "knowledge_graph"

class ScoringMode(Enum):
    """Scoring system modes."""
    SIMPLE = "simple"
    NORMALIZED = "normalized" 
    ADAPTIVE = "adaptive"

@dataclass
class RAGConfiguration:
    """
    Comprehensive RAG system configuration.
    Supports modular feature flags and performance tuning.
    """
    
    # Core RAG Strategies (Feature Flags)
    use_contextual_embeddings: bool = False
    use_hybrid_search: bool = True
    use_agentic_rag: bool = False
    use_reranking: bool = True
    use_knowledge_graph: bool = False
    
    # Enhanced Features
    use_enhanced_chunking: bool = False
    use_query_expansion: bool = True
    use_result_deduplication: bool = False
    use_semantic_caching: bool = False
    
    # Scoring Configuration
    scoring_mode: ScoringMode = ScoringMode.NORMALIZED
    vector_weight: float = 0.5
    keyword_weight: float = 0.3
    rerank_weight: float = 0.2
    dual_match_boost: float = 1.2
    rerank_threshold: float = -2.0
    
    # Performance Settings
    max_concurrent_searches: int = 3
    embedding_batch_size: int = 50
    query_timeout_seconds: int = 30
    enable_parallel_processing: bool = True
    
    # Code Analysis Settings (for Agentic RAG)
    min_code_length: int = 50
    max_code_length: int = 10000
    code_extraction_timeout: int = 10
    enable_code_summarization: bool = True
    
    # Contextual Embeddings Settings
    contextual_window_size: int = 2000
    contextual_overlap: int = 200
    max_context_chunks: int = 5
    
    # Hybrid Search Settings
    keyword_search_boost: float = 1.1
    vector_search_boost: float = 1.0
    minimum_keyword_length: int = 3
    
    # Quality Filters
    enable_content_validation: bool = True
    minimum_relevance_score: float = 0.1
    maximum_duplicate_similarity: float = 0.95
    
    # Caching Settings
    cache_ttl_seconds: int = 3600
    max_cache_size: int = 1000
    enable_query_cache: bool = True
    enable_embedding_cache: bool = True
    
    # Monitoring & Observability
    enable_performance_metrics: bool = True
    enable_query_logging: bool = False
    log_slow_queries_threshold: float = 5.0
    
    # Advanced Features (Future)
    enable_late_chunking: bool = False
    enable_adaptive_retrieval: bool = False
    enable_multi_modal_search: bool = False
    
    @classmethod
    def from_environment(cls) -> 'RAGConfiguration':
        """
        Create configuration from environment variables.
        Supports all ref-rag-project flags plus enhancements.
        """
        def get_bool(key: str, default: bool = False) -> bool:
            return os.getenv(key, str(default)).lower() in ('true', '1', 'yes', 'on')
        
        def get_float(key: str, default: float) -> float:
            try:
                return float(os.getenv(key, str(default)))
            except (ValueError, TypeError):
                logger.warning(f"Invalid float value for {key}, using default: {default}")
                return default
        
        def get_int(key: str, default: int) -> int:
            try:
                return int(os.getenv(key, str(default)))
            except (ValueError, TypeError):
                logger.warning(f"Invalid int value for {key}, using default: {default}")
                return default
        
        # Core RAG strategies (ref-rag-project compatibility)
        use_contextual_embeddings = get_bool("USE_CONTEXTUAL_EMBEDDINGS", False)
        use_hybrid_search = get_bool("USE_HYBRID_SEARCH", True)
        use_agentic_rag = get_bool("USE_AGENTIC_RAG", False)
        use_reranking = get_bool("USE_RERANKING", True)
        use_knowledge_graph = get_bool("USE_KNOWLEDGE_GRAPH", False)
        
        # Enhanced features
        use_enhanced_chunking = get_bool("USE_ENHANCED_CHUNKING", False)
        use_query_expansion = get_bool("USE_QUERY_EXPANSION", True)
        use_result_deduplication = get_bool("USE_RESULT_DEDUPLICATION", False)
        use_semantic_caching = get_bool("USE_SEMANTIC_CACHING", False)
        
        # Scoring configuration
        scoring_mode_str = os.getenv("SCORING_MODE", "normalized").lower()
        scoring_mode = ScoringMode.NORMALIZED
        try:
            scoring_mode = ScoringMode(scoring_mode_str)
        except ValueError:
            logger.warning(f"Invalid scoring mode: {scoring_mode_str}, using normalized")
        
        # Performance settings
        max_concurrent = get_int("MAX_CONCURRENT_SEARCHES", 3)
        batch_size = get_int("EMBEDDING_BATCH_SIZE", 50)
        query_timeout = get_int("QUERY_TIMEOUT_SECONDS", 30)
        
        # Code analysis settings
        min_code_length = get_int("MIN_CODE_LENGTH", 50)
        max_code_length = get_int("MAX_CODE_LENGTH", 10000)
        
        # Scoring weights
        vector_weight = get_float("VECTOR_WEIGHT", 0.5)
        keyword_weight = get_float("KEYWORD_WEIGHT", 0.3)
        rerank_weight = get_float("RERANK_WEIGHT", 0.2)
        dual_match_boost = get_float("DUAL_MATCH_BOOST", 1.2)
        rerank_threshold = get_float("RERANK_THRESHOLD", -2.0)
        
        # Quality settings
        min_relevance = get_float("MINIMUM_RELEVANCE_SCORE", 0.1)
        max_duplicate_sim = get_float("MAXIMUM_DUPLICATE_SIMILARITY", 0.95)
        
        # Caching settings
        cache_ttl = get_int("CACHE_TTL_SECONDS", 3600)
        max_cache_size = get_int("MAX_CACHE_SIZE", 1000)
        
        return cls(
            # Core strategies
            use_contextual_embeddings=use_contextual_embeddings,
            use_hybrid_search=use_hybrid_search,
            use_agentic_rag=use_agentic_rag,
            use_reranking=use_reranking,
            use_knowledge_graph=use_knowledge_graph,
            
            # Enhanced features
            use_enhanced_chunking=use_enhanced_chunking,
            use_query_expansion=use_query_expansion,
            use_result_deduplication=use_result_deduplication,
            use_semantic_caching=use_semantic_caching,
            
            # Scoring
            scoring_mode=scoring_mode,
            vector_weight=vector_weight,
            keyword_weight=keyword_weight,
            rerank_weight=rerank_weight,
            dual_match_boost=dual_match_boost,
            rerank_threshold=rerank_threshold,
            
            # Performance
            max_concurrent_searches=max_concurrent,
            embedding_batch_size=batch_size,
            query_timeout_seconds=query_timeout,
            enable_parallel_processing=get_bool("ENABLE_PARALLEL_PROCESSING", True),
            
            # Code analysis
            min_code_length=min_code_length,
            max_code_length=max_code_length,
            code_extraction_timeout=get_int("CODE_EXTRACTION_TIMEOUT", 10),
            enable_code_summarization=get_bool("ENABLE_CODE_SUMMARIZATION", True),
            
            # Quality filters
            enable_content_validation=get_bool("ENABLE_CONTENT_VALIDATION", True),
            minimum_relevance_score=min_relevance,
            maximum_duplicate_similarity=max_duplicate_sim,
            
            # Caching
            cache_ttl_seconds=cache_ttl,
            max_cache_size=max_cache_size,
            enable_query_cache=get_bool("ENABLE_QUERY_CACHE", True),
            enable_embedding_cache=get_bool("ENABLE_EMBEDDING_CACHE", True),
            
            # Monitoring
            enable_performance_metrics=get_bool("ENABLE_PERFORMANCE_METRICS", True),
            enable_query_logging=get_bool("ENABLE_QUERY_LOGGING", False),
            log_slow_queries_threshold=get_float("LOG_SLOW_QUERIES_THRESHOLD", 5.0),
            
            # Advanced features
            enable_late_chunking=get_bool("ENABLE_LATE_CHUNKING", False),
            enable_adaptive_retrieval=get_bool("ENABLE_ADAPTIVE_RETRIEVAL", False),
            enable_multi_modal_search=get_bool("ENABLE_MULTI_MODAL_SEARCH", False)
        )
    
    def get_enabled_strategies(self) -> List[RAGStrategy]:
        """Get list of enabled RAG strategies."""
        strategies = []
        
        if self.use_contextual_embeddings:
            strategies.append(RAGStrategy.CONTEXTUAL_EMBEDDINGS)
        if self.use_hybrid_search:
            strategies.append(RAGStrategy.HYBRID_SEARCH)
        if self.use_agentic_rag:
            strategies.append(RAGStrategy.AGENTIC_RAG)
        if self.use_reranking:
            strategies.append(RAGStrategy.RERANKING)
        if self.use_knowledge_graph:
            strategies.append(RAGStrategy.KNOWLEDGE_GRAPH)
        
        if not strategies:
            strategies.append(RAGStrategy.BASIC)
        
        return strategies
    
    def validate_configuration(self) -> List[str]:
        """
        Validate configuration and return list of warnings/errors.
        """
        warnings = []
        
        # Check weight normalization
        total_weight = self.vector_weight + self.keyword_weight + self.rerank_weight
        if abs(total_weight - 1.0) > 0.1:
            warnings.append(
                f"Scoring weights sum to {total_weight:.2f}, not 1.0. "
                "This may affect score normalization."
            )
        
        # Check performance settings
        if self.max_concurrent_searches > 10:
            warnings.append(
                f"High concurrent searches ({self.max_concurrent_searches}) "
                "may cause resource exhaustion."
            )
        
        if self.embedding_batch_size > 100:
            warnings.append(
                f"Large batch size ({self.embedding_batch_size}) "
                "may cause memory issues or API timeouts."
            )
        
        # Check code analysis settings
        if self.use_agentic_rag and self.min_code_length >= self.max_code_length:
            warnings.append(
                "min_code_length >= max_code_length will prevent code extraction."
            )
        
        # Check dependencies
        if self.use_reranking and not self.use_hybrid_search:
            warnings.append(
                "Reranking is most effective with hybrid search enabled."
            )
        
        if self.use_semantic_caching and not self.enable_embedding_cache:
            warnings.append(
                "Semantic caching requires embedding cache to be enabled."
            )
        
        return warnings
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for serialization."""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, Enum):
                result[key] = value.value
            else:
                result[key] = value
        return result
    
    def __str__(self) -> str:
        """Human-readable configuration summary."""
        enabled_strategies = [s.value for s in self.get_enabled_strategies()]
        
        return (
            f"RAG Configuration:\n"
            f"  Strategies: {', '.join(enabled_strategies)}\n"
            f"  Scoring: {self.scoring_mode.value} "
            f"(V:{self.vector_weight}, K:{self.keyword_weight}, R:{self.rerank_weight})\n"
            f"  Performance: {self.max_concurrent_searches} concurrent, "
            f"batch={self.embedding_batch_size}\n"
            f"  Features: "
            f"{'chunking ' if self.use_enhanced_chunking else ''}"
            f"{'expansion ' if self.use_query_expansion else ''}"
            f"{'dedup ' if self.use_result_deduplication else ''}"
            f"{'cache' if self.use_semantic_caching else ''}"
        )

# Predefined configurations for common use cases
def get_basic_config() -> RAGConfiguration:
    """Basic RAG configuration for simple use cases."""
    return RAGConfiguration(
        use_contextual_embeddings=False,
        use_hybrid_search=True,
        use_agentic_rag=False,
        use_reranking=False,
        use_knowledge_graph=False,
        scoring_mode=ScoringMode.SIMPLE
    )

def get_advanced_config() -> RAGConfiguration:
    """Advanced configuration for AI coding assistants."""
    return RAGConfiguration(
        use_contextual_embeddings=True,
        use_hybrid_search=True,
        use_agentic_rag=True,
        use_reranking=True,
        use_knowledge_graph=False,
        use_enhanced_chunking=True,
        use_query_expansion=True,
        scoring_mode=ScoringMode.ADAPTIVE
    )

def get_performance_config() -> RAGConfiguration:
    """Performance-optimized configuration."""
    return RAGConfiguration(
        use_contextual_embeddings=False,
        use_hybrid_search=True,
        use_agentic_rag=False,
        use_reranking=True,
        use_knowledge_graph=False,
        use_semantic_caching=True,
        enable_parallel_processing=True,
        max_concurrent_searches=5,
        scoring_mode=ScoringMode.NORMALIZED
    )

def get_comprehensive_config() -> RAGConfiguration:
    """Comprehensive configuration with all features enabled."""
    return RAGConfiguration(
        use_contextual_embeddings=True,
        use_hybrid_search=True,
        use_agentic_rag=True,
        use_reranking=True,
        use_knowledge_graph=True,
        use_enhanced_chunking=True,
        use_query_expansion=True,
        use_result_deduplication=True,
        use_semantic_caching=True,
        scoring_mode=ScoringMode.ADAPTIVE
    )

# Global configuration instance
_config: Optional[RAGConfiguration] = None

def get_rag_config() -> RAGConfiguration:
    """Get global RAG configuration instance."""
    global _config
    if _config is None:
        _config = RAGConfiguration.from_environment()
        
        # Validate and log warnings
        warnings = _config.validate_configuration()
        if warnings:
            logger.warning("RAG Configuration warnings:")
            for warning in warnings:
                logger.warning(f"  - {warning}")
        
        logger.info(f"RAG Configuration loaded:\n{_config}")
    
    return _config

def reload_config() -> RAGConfiguration:
    """Reload configuration from environment."""
    global _config
    _config = None
    return get_rag_config()