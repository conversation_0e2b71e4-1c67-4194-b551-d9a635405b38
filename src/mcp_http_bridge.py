#!/usr/bin/env python3
"""
Simple HTTP Bridge for MCP SSE Server

This creates an HTTP API that directly imports and calls the MCP tools,
bypassing the SSE protocol entirely.
"""
import asyncio
import json
import logging
import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Import MCP server and its context
from ay_rag_mcp import (
    crawl_single_page,
    smart_crawl_url,
    get_available_sources, 
    get_database_stats,
    perform_rag_query,
    search_code_examples,
    list_content,
    delete_content,
    flag_content,
    AYRAGContext,
    _initialize_shared_resources,
    _server_ready,
    _initialization_complete
)
import ay_rag_mcp

from fastapi import FastAPI, HTTPException
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ToolCallRequest(BaseModel):
    """Request model for tool calls"""
    name: str
    arguments: Dict[str, Any] = {}

app = FastAPI(
    title="MCP HTTP Bridge",
    description="HTTP API bridge for AY RAG MCP Server",
    version="1.0.0"
)

# Create wrapper classes to mimic the expected context structure
class RequestContext:
    """Wrapper for request context"""
    def __init__(self, lifespan_context):
        self.lifespan_context = lifespan_context

class MCPContext:
    """Wrapper that mimics the MCP context structure"""
    def __init__(self, ayrag_context: AYRAGContext):
        self.request_context = RequestContext(ayrag_context)

# Global context
_context: Optional[MCPContext] = None

async def get_context() -> MCPContext:
    """Get or create the MCP context"""
    global _context
    if _context is None:
        # Initialize shared resources
        shared = await _initialize_shared_resources()
        ayrag_context = AYRAGContext(
            crawler=shared.crawler,
            supabase_client=shared.supabase_client,
            reranking_model=shared.reranking_model
        )
        _context = MCPContext(ayrag_context)
    return _context

# Map tool names to functions
TOOL_MAP = {
    "crawl_single_page": crawl_single_page,
    "smart_crawl_url": smart_crawl_url,
    "get_available_sources": get_available_sources,
    "get_database_stats": get_database_stats,
    "perform_rag_query": perform_rag_query,
    "search_code_examples": search_code_examples,
    "list_content": list_content,
    "delete_content": delete_content,
    "flag_content": flag_content
}

@app.on_event("startup")
async def startup():
    """Initialize context on startup"""
    logger.info("Initializing MCP HTTP Bridge...")
    await get_context()
    
    # Set the initialization flags to bypass the server ready check
    ay_rag_mcp._initialization_complete = True
    _server_ready.set()
    
    logger.info("MCP HTTP Bridge ready!")

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "healthy", "service": "mcp-http-bridge"}

@app.post("/tools/call")
async def call_tool(request: ToolCallRequest):
    """Call an MCP tool via HTTP"""
    try:
        # Get the tool function
        if request.name not in TOOL_MAP:
            raise HTTPException(status_code=404, detail=f"Tool '{request.name}' not found")
        
        tool_func = TOOL_MAP[request.name]
        context = await get_context()
        
        # Call the tool with context and arguments
        result = await tool_func(context, **request.arguments)
        
        # Parse the JSON result (tools return JSON strings)
        if isinstance(result, str):
            try:
                parsed_result = json.loads(result)
            except json.JSONDecodeError:
                parsed_result = {"result": result}
        else:
            parsed_result = result
        
        # Format response to match expected format
        return {
            "content": [{
                "text": json.dumps(parsed_result)
            }]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Tool call failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "MCP HTTP Bridge",
        "endpoints": {
            "health": "/health",
            "tools": "/tools/call"
        },
        "available_tools": list(TOOL_MAP.keys())
    }

if __name__ == "__main__":
    import uvicorn
    # Use port 8052 to avoid conflicts
    port = int(os.getenv("BRIDGE_PORT", "8052"))
    uvicorn.run(app, host="0.0.0.0", port=port)