"""
Safe error handling for MCP responses

This module provides centralized error handling that prevents exposure of
sensitive information while providing helpful user-friendly error messages.
"""
from enum import Enum
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Categories of errors for safe user communication"""
    NETWORK = "network_error"
    API = "api_error"
    DATABASE = "database_error"
    VALIDATION = "validation_error"
    TIMEOUT = "timeout_error"
    INITIALIZATION = "initialization_error"
    CONTENT = "content_error"
    INTERNAL = "internal_error"


# User-friendly error messages
ERROR_MESSAGES = {
    ErrorCategory.NETWORK: "Network connection error. Please check the URL and your internet connection.",
    ErrorCategory.API: "API configuration error. Please verify your API keys are correctly set.",
    ErrorCategory.DATABASE: "Database connection error. Please check your database configuration.",
    ErrorCategory.VALIDATION: "Invalid input provided. Please check your parameters and try again.",
    ErrorCategory.TIMEOUT: "Operation timed out. Try reducing the scope or retry later.",
    ErrorCategory.INITIALIZATION: "Server is still initializing. Please wait a moment and try again.",
    ErrorCategory.CONTENT: "Content processing error. The content may be invalid or unsupported.",
    ErrorCategory.INTERNAL: "An unexpected error occurred. Please try again or contact support."
}

# Error suggestions for users
ERROR_SUGGESTIONS = {
    ErrorCategory.NETWORK: [
        "Verify the URL is correct and accessible",
        "Check your internet connection",
        "Try again with a different URL"
    ],
    ErrorCategory.API: [
        "Check that OPENAI_API_KEY is set correctly",
        "Verify your API key has sufficient credits",
        "Ensure you have access to the required API endpoints"
    ],
    ErrorCategory.DATABASE: [
        "Verify SUPABASE_URL and SUPABASE_SERVICE_KEY are set",
        "Check that your database tables exist",
        "Ensure your Supabase project is active"
    ],
    ErrorCategory.VALIDATION: [
        "Check that all required parameters are provided",
        "Verify parameter formats match expected types",
        "Ensure URLs are valid and accessible"
    ],
    ErrorCategory.TIMEOUT: [
        "Reduce max_depth parameter if crawling",
        "Decrease max_concurrent parameter",
        "Try crawling smaller sections at a time"
    ],
    ErrorCategory.INITIALIZATION: [
        "Wait 30-60 seconds for server initialization to complete",
        "Check server logs for initialization issues",
        "Restart the server if the issue persists"
    ],
    ErrorCategory.CONTENT: [
        "Verify the content is publicly accessible",
        "Check if the website requires authentication",
        "Try a different URL or content type"
    ],
    ErrorCategory.INTERNAL: [
        "Try the operation again",
        "Contact support if the issue persists",
        "Check server logs for more details"
    ]
}


def categorize_error(exception: Exception) -> ErrorCategory:
    """
    Categorize exception into safe error types based on exception details
    
    Args:
        exception: The exception to categorize
        
    Returns:
        ErrorCategory: The appropriate category for safe user communication
    """
    error_str = str(exception).lower()
    exception_type = type(exception).__name__.lower()
    
    # Network and connection errors
    if any(term in error_str for term in ['network', 'connection', 'urllib', 'socket', 'timeout']):
        return ErrorCategory.NETWORK
    
    if any(term in error_str for term in ['connect', 'resolve', 'unreachable', 'refused']):
        return ErrorCategory.NETWORK
    
    # API and authentication errors
    if any(term in error_str for term in ['openai', 'api key', 'unauthorized', '401', '403']):
        return ErrorCategory.API
    
    if any(term in error_str for term in ['rate limit', 'quota', 'billing']):
        return ErrorCategory.API
    
    # Database errors
    if any(term in error_str for term in ['supabase', 'database', 'postgres', 'sql']):
        return ErrorCategory.DATABASE
    
    if any(term in error_str for term in ['table', 'column', 'constraint', 'foreign key']):
        return ErrorCategory.DATABASE
    
    # Validation errors
    if any(term in error_str for term in ['validation', 'invalid', 'missing required', 'bad request']):
        return ErrorCategory.VALIDATION
    
    if exception_type in ['valueerror', 'typeerror', 'keyerror']:
        return ErrorCategory.VALIDATION
    
    # Timeout errors
    if any(term in error_str for term in ['timeout', 'timed out', 'asyncio.timeout']):
        return ErrorCategory.TIMEOUT
    
    # Initialization errors
    if any(term in error_str for term in ['initialization', 'not ready', 'starting up']):
        return ErrorCategory.INITIALIZATION
    
    if 'server initialization not complete' in error_str:
        return ErrorCategory.INITIALIZATION
    
    # Content processing errors
    if any(term in error_str for term in ['content', 'parsing', 'crawling', 'extraction']):
        return ErrorCategory.CONTENT
    
    # Default to internal error
    return ErrorCategory.INTERNAL


def create_safe_error_response(
    exception: Exception, 
    context: str = "", 
    include_suggestions: bool = True,
    log_full_error: bool = True
) -> Dict[str, Any]:
    """
    Create user-safe error response without exposing internals
    
    Args:
        exception: The exception that occurred
        context: Additional context about where the error occurred
        include_suggestions: Whether to include helpful suggestions
        log_full_error: Whether to log the full error details
        
    Returns:
        Dict containing safe error response
    """
    # Log full error details for debugging (with stack trace in debug mode)
    if log_full_error:
        if logger.isEnabledFor(logging.DEBUG):
            logger.error(
                f"Error in {context}: {type(exception).__name__}: {str(exception)}", 
                exc_info=True
            )
        else:
            logger.error(f"Error in {context}: {type(exception).__name__}: {str(exception)}")
    
    # Categorize and get safe message
    category = categorize_error(exception)
    safe_message = ERROR_MESSAGES[category]
    
    response = {
        "success": False,
        "error": safe_message,
        "error_type": category.value
    }
    
    # Add suggestions if requested
    if include_suggestions and category in ERROR_SUGGESTIONS:
        response["suggestions"] = ERROR_SUGGESTIONS[category]
    
    # Add context if provided (but sanitize it)
    if context:
        # Only include safe context information
        safe_context = sanitize_context(context)
        if safe_context:
            response["context"] = safe_context
    
    return response


def sanitize_context(context: str) -> Optional[str]:
    """
    Sanitize context string to remove sensitive information
    
    Args:
        context: Raw context string
        
    Returns:
        Sanitized context string or None if too sensitive
    """
    if not context:
        return None
    
    # Remove sensitive patterns
    sensitive_patterns = [
        r'sk-[a-zA-Z0-9]+',  # API keys
        r'password[=:]\s*\S+',  # Passwords
        r'token[=:]\s*\S+',  # Tokens
        r'/home/<USER>/]+',  # User paths
        r'C:\\Users\\<USER>\\]+',  # Windows user paths
    ]
    
    import re
    sanitized = context
    for pattern in sensitive_patterns:
        sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)
    
    # Only return context if it's short and doesn't contain file paths
    if len(sanitized) < 100 and '/' not in sanitized and '\\' not in sanitized:
        return sanitized
    
    return None


def log_security_event(event_type: str, details: Dict[str, Any]) -> None:
    """
    Log security-related events for monitoring
    
    Args:
        event_type: Type of security event
        details: Event details (will be sanitized)
    """
    sanitized_details = {
        key: sanitize_context(str(value)) if isinstance(value, str) else str(value)
        for key, value in details.items()
    }
    
    logger.warning(f"Security event: {event_type}", extra={"security_event": sanitized_details})


# Convenience functions for common error scenarios
def network_error_response(context: str = "") -> Dict[str, Any]:
    """Create a network error response"""
    return {
        "success": False,
        "error": ERROR_MESSAGES[ErrorCategory.NETWORK],
        "error_type": ErrorCategory.NETWORK.value,
        "suggestions": ERROR_SUGGESTIONS[ErrorCategory.NETWORK]
    }


def validation_error_response(message: str = None, context: str = "") -> Dict[str, Any]:
    """Create a validation error response with optional custom message"""
    return {
        "success": False,
        "error": message or ERROR_MESSAGES[ErrorCategory.VALIDATION],
        "error_type": ErrorCategory.VALIDATION.value,
        "suggestions": ERROR_SUGGESTIONS[ErrorCategory.VALIDATION]
    }


def initialization_error_response(context: str = "") -> Dict[str, Any]:
    """Create an initialization error response"""
    return {
        "success": False,
        "error": ERROR_MESSAGES[ErrorCategory.INITIALIZATION],
        "error_type": ErrorCategory.INITIALIZATION.value,
        "suggestions": ERROR_SUGGESTIONS[ErrorCategory.INITIALIZATION]
    }