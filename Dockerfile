FROM python:3.12-slim

ARG PORT=8051

WORKDIR /app

# Install uv
RUN pip install uv

# Copy the MCP server files
COPY . .

# Install packages directly to the system (no virtual environment)
# First install crawl4ai separately to ensure crawl4ai-setup is available
RUN uv pip install --system crawl4ai==0.6.2 && \
    crawl4ai-setup && \
    uv pip install --system -e .

EXPOSE ${PORT}

# Add health check using the new health endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/ready || exit 1

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Command to run the MCP server
CMD ["python", "src/ay_rag_mcp.py"]
