[07/21/25 22:57:15] INFO     Starting worker with config:      job_worker.py:500
                             redis_url=redis://localhost:6379,                  
                             worker_id=hardcoded-test,                          
                             concurrency=3                                      
                    INFO     Worker initializing with Redis     job_worker.py:45
                             URL: redis://localhost:6379                        
                    INFO     Connected to <PERSON><PERSON> at              job_queue.py:127
                             redis://localhost:6379                             
                    INFO     Worker hardcoded-test initialized  job_worker.py:67
                             successfully                                       
                    INFO     Starting worker hardcoded-test     job_worker.py:80
                             with 3 concurrent tasks                            
                    INFO     Starting worker loop 0 for        job_worker.py:127
                             hardcoded-test                                     
                    INFO     Starting worker loop 1 for        job_worker.py:127
                             hardcoded-test                                     
                    INFO     Starting worker loop 2 for        job_worker.py:127
                             hardcoded-test                                     
[07/21/25 22:57:37] INFO     Received shutdown signal for      job_worker.py:469
                             worker hardcoded-test                              
                    INFO     Received shutdown signal for      job_worker.py:469
                             worker hardcoded-test                              
[07/21/25 22:57:38] INFO     Shutting down worker              job_worker.py:108
                             hardcoded-test                                     
                    INFO     Worker loop 0 cancelled           job_worker.py:145
                    INFO     Worker loop 1 cancelled           job_worker.py:145
                    INFO     Worker loop 2 cancelled           job_worker.py:145
