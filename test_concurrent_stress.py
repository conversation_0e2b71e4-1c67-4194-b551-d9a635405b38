#!/usr/bin/env python3
"""
Concurrent Stress Testing for Redis Queue System
Tests system behavior under extreme load and resource pressure
"""

import asyncio
import time
import json
import statistics
import psutil
import os
import gc
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue, JobRequest, JobStatus


class ConcurrentStressTestSuite:
    """Comprehensive concurrent stress testing suite"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.test_results = []
        self.process = psutil.Process(os.getpid())
        self.system_metrics = []
    
    def get_system_metrics(self) -> Dict[str, float]:
        """Get current system resource usage"""
        return {
            "cpu_percent": self.process.cpu_percent(),
            "memory_mb": self.process.memory_info().rss / 1024 / 1024,
            "memory_percent": self.process.memory_percent(),
            "system_cpu": psutil.cpu_percent(),
            "system_memory_percent": psutil.virtual_memory().percent,
            "open_files": len(self.process.open_files()) if hasattr(self.process, 'open_files') else 0
        }
    
    async def test_extreme_concurrent_load(self, num_jobs: int = 5000, max_workers: int = 50) -> Dict[str, Any]:
        """Test system under extreme concurrent load"""
        print(f"🚀 Testing extreme concurrent load ({num_jobs} jobs, up to {max_workers} workers)...")
        
        queue = RedisJobQueue(self.redis_url, "extreme-load-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("extreme-load-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Track system resources
        initial_metrics = self.get_system_metrics()
        resource_samples = []
        
        # Resource monitoring task
        async def monitor_resources():
            while True:
                resource_samples.append(self.get_system_metrics())
                await asyncio.sleep(0.5)  # Sample every 500ms
        
        monitor_task = asyncio.create_task(monitor_resources())
        
        try:
            start_time = time.time()
            
            # Worker function for concurrent operations
            async def concurrent_worker(worker_id: int, jobs_per_worker: int) -> Dict[str, int]:
                successful_enqueues = 0
                successful_dequeues = 0
                successful_status_updates = 0
                errors = 0
                
                # Create semaphore to control worker concurrency
                semaphore = asyncio.Semaphore(10)
                
                async def process_job(job_index: int):
                    async with semaphore:
                        try:
                            # Create and enqueue job
                            job = JobRequest.create(
                                job_type="extreme_load_test",
                                url=f"https://extreme-{worker_id}-{job_index}.com",
                                parameters={
                                    "worker_id": worker_id,
                                    "job_index": job_index,
                                    "test_data": "x" * 1000  # 1KB payload
                                }
                            )
                            job_id = await queue.enqueue_job(job)
                            nonlocal successful_enqueues
                            successful_enqueues += 1
                            
                            # Status update to processing
                            await queue.update_job_status(job_id, JobStatus.PROCESSING)
                            nonlocal successful_status_updates
                            successful_status_updates += 1
                            
                            # Complete the job
                            await queue.update_job_status(
                                job_id, 
                                JobStatus.COMPLETED,
                                result_data={
                                    "worker": worker_id,
                                    "processed_at": datetime.now(timezone.utc).isoformat(),
                                    "result": "success"
                                }
                            )
                            successful_status_updates += 1
                            
                            # Verify job status
                            status = await queue.get_job_status(job_id)
                            if status and status.status == JobStatus.COMPLETED:
                                nonlocal successful_dequeues
                                successful_dequeues += 1
                                
                        except Exception as e:
                            nonlocal errors
                            errors += 1
                            if errors <= 5:  # Only print first few errors
                                print(f"Worker {worker_id} job {job_index} error: {e}")
                
                # Process all jobs for this worker concurrently
                job_tasks = [process_job(i) for i in range(jobs_per_worker)]
                await asyncio.gather(*job_tasks, return_exceptions=True)
                
                return {
                    "successful_enqueues": successful_enqueues,
                    "successful_dequeues": successful_dequeues,
                    "successful_status_updates": successful_status_updates,
                    "errors": errors
                }
            
            # Scale up workers gradually to test breaking points
            worker_results = []
            jobs_per_worker = num_jobs // max_workers
            
            # Create all workers
            worker_tasks = [
                concurrent_worker(worker_id, jobs_per_worker)
                for worker_id in range(max_workers)
            ]
            
            # Execute all workers concurrently
            print(f"📊 Executing {max_workers} workers with {jobs_per_worker} jobs each...")
            results = await asyncio.gather(*worker_tasks, return_exceptions=True)
            
            total_time = time.time() - start_time
            
            # Cancel monitoring
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
            
            # Aggregate results
            total_enqueues = sum(r["successful_enqueues"] for r in results if isinstance(r, dict))
            total_dequeues = sum(r["successful_dequeues"] for r in results if isinstance(r, dict))
            total_status_updates = sum(r["successful_status_updates"] for r in results if isinstance(r, dict))
            total_errors = sum(r["errors"] for r in results if isinstance(r, dict))
            
            # Calculate resource usage statistics
            final_metrics = self.get_system_metrics()
            if resource_samples:
                peak_cpu = max(sample["cpu_percent"] for sample in resource_samples)
                peak_memory = max(sample["memory_mb"] for sample in resource_samples)
                avg_cpu = statistics.mean(sample["cpu_percent"] for sample in resource_samples)
                avg_memory = statistics.mean(sample["memory_mb"] for sample in resource_samples)
            else:
                peak_cpu = final_metrics["cpu_percent"]
                peak_memory = final_metrics["memory_mb"]
                avg_cpu = final_metrics["cpu_percent"]
                avg_memory = final_metrics["memory_mb"]
            
            total_operations = total_enqueues + total_dequeues + total_status_updates
            success_rate = total_operations / (num_jobs * 4)  # 4 operations per job
            
            result = {
                "test_name": "extreme_concurrent_load",
                "total_jobs": num_jobs,
                "max_workers": max_workers,
                "total_operations": total_operations,
                "successful_enqueues": total_enqueues,
                "successful_dequeues": total_dequeues,
                "successful_status_updates": total_status_updates,
                "total_errors": total_errors,
                "success_rate": success_rate,
                "total_time": total_time,
                "operations_per_second": total_operations / total_time,
                "resource_usage": {
                    "initial_memory_mb": initial_metrics["memory_mb"],
                    "final_memory_mb": final_metrics["memory_mb"],
                    "peak_memory_mb": peak_memory,
                    "avg_memory_mb": avg_memory,
                    "memory_increase_mb": final_metrics["memory_mb"] - initial_metrics["memory_mb"],
                    "peak_cpu_percent": peak_cpu,
                    "avg_cpu_percent": avg_cpu,
                    "final_cpu_percent": final_metrics["cpu_percent"]
                },
                "performance_grade": self._calculate_performance_grade(success_rate, total_operations / total_time, peak_memory - initial_metrics["memory_mb"])
            }
            
            await queue.disconnect()
            
            print(f"   ✅ Success Rate: {success_rate * 100:.1f}%")
            print(f"   ⚡ Operations/sec: {result['operations_per_second']:.2f}")
            print(f"   💾 Peak Memory: {peak_memory:.1f}MB")
            print(f"   🏆 Grade: {result['performance_grade']}")
            
            return result
            
        except Exception as e:
            monitor_task.cancel()
            await queue.disconnect()
            raise e
    
    async def test_memory_pressure_resistance(self, num_large_jobs: int = 100, payload_size_mb: int = 5) -> Dict[str, Any]:
        """Test system behavior under memory pressure with large payloads"""
        print(f"🧠 Testing memory pressure resistance ({num_large_jobs} jobs, {payload_size_mb}MB each)...")
        
        queue = RedisJobQueue(self.redis_url, "memory-pressure-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("memory-pressure-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        initial_metrics = self.get_system_metrics()
        
        # Create large payload
        large_payload = "x" * (payload_size_mb * 1024 * 1024)  # MB-sized payload
        
        successful_operations = 0
        memory_errors = 0
        start_time = time.time()
        memory_samples = []
        
        try:
            for i in range(num_large_jobs):
                try:
                    # Monitor memory every 10 jobs
                    if i % 10 == 0:
                        memory_samples.append(self.get_system_metrics())
                        gc.collect()  # Force garbage collection
                    
                    # Create large job
                    job = JobRequest.create(
                        job_type="memory_pressure_test",
                        url=f"https://large-memory-{i}.com",
                        parameters={
                            "job_id": i,
                            "large_data": large_payload,
                            "metadata": {"size_mb": payload_size_mb, "job_index": i}
                        }
                    )
                    
                    # Process job through lifecycle
                    job_id = await queue.enqueue_job(job)
                    await queue.update_job_status(job_id, JobStatus.PROCESSING)
                    await queue.update_job_status(
                        job_id, 
                        JobStatus.COMPLETED,
                        result_data={"processed": True, "size": len(large_payload)}
                    )
                    
                    # Verify
                    status = await queue.get_job_status(job_id)
                    if status and status.status == JobStatus.COMPLETED:
                        successful_operations += 1
                    
                    # Clear reference to large payload
                    del job
                    
                except MemoryError:
                    memory_errors += 1
                    print(f"Memory error on job {i}")
                except Exception as e:
                    print(f"Error on job {i}: {e}")
            
            total_time = time.time() - start_time
            final_metrics = self.get_system_metrics()
            
            # Calculate memory statistics
            if memory_samples:
                peak_memory = max(sample["memory_mb"] for sample in memory_samples)
                avg_memory = statistics.mean(sample["memory_mb"] for sample in memory_samples)
            else:
                peak_memory = final_metrics["memory_mb"]
                avg_memory = final_metrics["memory_mb"]
            
            success_rate = successful_operations / num_large_jobs
            
            result = {
                "test_name": "memory_pressure_resistance",
                "total_large_jobs": num_large_jobs,
                "payload_size_mb": payload_size_mb,
                "successful_operations": successful_operations,
                "memory_errors": memory_errors,
                "success_rate": success_rate,
                "total_time": total_time,
                "memory_stats": {
                    "initial_memory_mb": initial_metrics["memory_mb"],
                    "final_memory_mb": final_metrics["memory_mb"],
                    "peak_memory_mb": peak_memory,
                    "avg_memory_mb": avg_memory,
                    "memory_increase_mb": final_metrics["memory_mb"] - initial_metrics["memory_mb"],
                    "estimated_peak_usage_mb": peak_memory
                },
                "memory_efficiency": self._calculate_memory_efficiency(peak_memory - initial_metrics["memory_mb"], payload_size_mb * num_large_jobs),
                "memory_resilient": memory_errors == 0 and success_rate > 0.95
            }
            
            await queue.disconnect()
            
            print(f"   ✅ Success Rate: {success_rate * 100:.1f}%")
            print(f"   💾 Peak Memory: {peak_memory:.1f}MB")
            print(f"   🧠 Memory Errors: {memory_errors}")
            
            return result
            
        except Exception as e:
            await queue.disconnect()
            raise e
    
    async def test_connection_pool_stress(self, concurrent_connections: int = 20, operations_per_connection: int = 100) -> Dict[str, Any]:
        """Test Redis connection pool under stress"""
        print(f"🔗 Testing connection pool stress ({concurrent_connections} connections, {operations_per_connection} ops each)...")
        
        initial_metrics = self.get_system_metrics()
        
        async def connection_worker(worker_id: int) -> Dict[str, int]:
            """Worker that creates its own queue instance and performs operations"""
            queue = RedisJobQueue(self.redis_url, f"connection-stress-{worker_id}")
            await queue.connect()
            
            successful_ops = 0
            connection_errors = 0
            
            try:
                for i in range(operations_per_connection):
                    try:
                        # Create job
                        job = JobRequest.create(
                            job_type="connection_stress",
                            url=f"https://conn-{worker_id}-{i}.com",
                            parameters={"worker": worker_id, "op": i}
                        )
                        
                        # Full lifecycle
                        job_id = await queue.enqueue_job(job)
                        await queue.update_job_status(job_id, JobStatus.PROCESSING)
                        await queue.update_job_status(job_id, JobStatus.COMPLETED)
                        
                        # Get stats to stress connection pool
                        stats = await queue.get_queue_stats()
                        if stats:
                            successful_ops += 1
                        
                    except Exception as e:
                        connection_errors += 1
                        if connection_errors <= 3:
                            print(f"Connection worker {worker_id} op {i} error: {e}")
                
            finally:
                await queue.disconnect()
            
            return {
                "successful_operations": successful_ops,
                "connection_errors": connection_errors
            }
        
        start_time = time.time()
        
        # Run all connection workers concurrently
        worker_tasks = [connection_worker(i) for i in range(concurrent_connections)]
        results = await asyncio.gather(*worker_tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        final_metrics = self.get_system_metrics()
        
        # Aggregate results
        total_successful = sum(r["successful_operations"] for r in results if isinstance(r, dict))
        total_connection_errors = sum(r["connection_errors"] for r in results if isinstance(r, dict))
        total_operations = concurrent_connections * operations_per_connection
        
        success_rate = total_successful / total_operations
        
        result = {
            "test_name": "connection_pool_stress",
            "concurrent_connections": concurrent_connections,
            "operations_per_connection": operations_per_connection,
            "total_operations": total_operations,
            "successful_operations": total_successful,
            "connection_errors": total_connection_errors,
            "success_rate": success_rate,
            "total_time": total_time,
            "operations_per_second": total_successful / total_time,
            "connection_efficiency": (concurrent_connections - len([r for r in results if isinstance(r, Exception)])) / concurrent_connections,
            "resource_impact": {
                "memory_increase_mb": final_metrics["memory_mb"] - initial_metrics["memory_mb"],
                "cpu_usage": final_metrics["cpu_percent"]
            }
        }
        
        print(f"   ✅ Success Rate: {success_rate * 100:.1f}%")
        print(f"   🔗 Connection Efficiency: {result['connection_efficiency'] * 100:.1f}%")
        print(f"   ⚡ Operations/sec: {result['operations_per_second']:.2f}")
        
        return result
    
    def _calculate_performance_grade(self, success_rate: float, ops_per_sec: float, memory_increase: float) -> str:
        """Calculate overall performance grade"""
        score = 0
        
        # Success rate scoring (40% weight)
        if success_rate >= 0.98:
            score += 40
        elif success_rate >= 0.95:
            score += 35
        elif success_rate >= 0.90:
            score += 25
        elif success_rate >= 0.80:
            score += 15
        
        # Performance scoring (40% weight)
        if ops_per_sec >= 2000:
            score += 40
        elif ops_per_sec >= 1000:
            score += 35
        elif ops_per_sec >= 500:
            score += 25
        elif ops_per_sec >= 200:
            score += 15
        
        # Memory efficiency scoring (20% weight)
        if memory_increase <= 50:
            score += 20
        elif memory_increase <= 100:
            score += 15
        elif memory_increase <= 200:
            score += 10
        elif memory_increase <= 500:
            score += 5
        
        # Grade assignment
        if score >= 90:
            return "A+ (Excellent)"
        elif score >= 80:
            return "A (Very Good)"
        elif score >= 70:
            return "B (Good)"
        elif score >= 60:
            return "C (Acceptable)"
        else:
            return "D (Needs Improvement)"
    
    def _calculate_memory_efficiency(self, actual_memory_used: float, theoretical_memory: float) -> str:
        """Calculate memory efficiency rating"""
        if theoretical_memory == 0:
            return "N/A"
        
        efficiency_ratio = actual_memory_used / theoretical_memory
        
        if efficiency_ratio <= 1.2:
            return "Excellent (< 1.2x theoretical)"
        elif efficiency_ratio <= 2.0:
            return "Good (< 2x theoretical)"
        elif efficiency_ratio <= 3.0:
            return "Fair (< 3x theoretical)"
        else:
            return "Poor (> 3x theoretical)"
    
    async def run_stress_test_suite(self) -> Dict[str, Any]:
        """Run complete concurrent stress test suite"""
        print("🚀 Starting Concurrent Stress Test Suite")
        print("=" * 60)
        
        # Test Redis connectivity
        try:
            test_queue = RedisJobQueue(self.redis_url, "connectivity-test")
            await test_queue.connect()
            await test_queue.disconnect()
            print("✅ Redis connectivity confirmed")
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            return {"error": "Redis connection failed"}
        
        tests = [
            ("Extreme Concurrent Load", self.test_extreme_concurrent_load, 5000, 50),
            ("Memory Pressure Resistance", self.test_memory_pressure_resistance, 100, 5),
            ("Connection Pool Stress", self.test_connection_pool_stress, 20, 100),
        ]
        
        results = {}
        overall_performance_score = 0
        
        for test_name, test_func, *args in tests:
            print(f"\n📊 Running {test_name}...")
            try:
                result = await test_func(*args)
                results[test_name] = result
                self.test_results.append(result)
                
                # Track overall performance
                if result.get('success_rate', 0) >= 0.95:
                    overall_performance_score += 1
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results[test_name] = {"error": str(e)}
        
        # Overall assessment
        results['overall_stress_test_passed'] = overall_performance_score >= 2
        results['test_timestamp'] = datetime.now(timezone.utc).isoformat()
        results['performance_score'] = f"{overall_performance_score}/3 tests passed"
        
        return results


async def main():
    """Run the concurrent stress test suite"""
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    stress_suite = ConcurrentStressTestSuite(redis_url)
    
    # Run stress tests
    results = await stress_suite.run_stress_test_suite()
    
    # Save raw results as JSON
    results_file = "concurrent_stress_test_results.json"
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print("\n" + "=" * 60)
    print("🎉 Concurrent Stress Testing Complete!")
    print(f"📊 Results saved to: {results_file}")
    
    # Print final status
    if results.get('overall_stress_test_passed', False):
        print("✅ SYSTEM PASSED STRESS TESTING - READY FOR HIGH LOAD")
    else:
        print("⚠️ SOME STRESS TESTS FAILED - REVIEW PERFORMANCE UNDER LOAD")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())