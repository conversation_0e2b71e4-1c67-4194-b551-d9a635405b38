#!/usr/bin/env python3
"""
Test concurrent SSE connections to verify race condition fix.
"""
import asyncio
import httpx
import time
import json

async def test_sse_connection(client: httpx.AsyncClient, connection_id: int):
    """Test a single SSE connection."""
    try:
        print(f"🔗 Connection {connection_id}: Starting SSE connection...")
        start_time = time.time()
        
        response = await client.get("http://localhost:8051/sse", timeout=10.0)
        duration = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ Connection {connection_id}: SUCCESS ({duration:.2f}s)")
            return True, duration, response.status_code
        elif response.status_code == 503:
            try:
                error_data = response.json()
                print(f"⏳ Connection {connection_id}: Server not ready ({duration:.2f}s) - {error_data.get('message', 'Unknown')}")
            except:
                print(f"⏳ Connection {connection_id}: Server not ready ({duration:.2f}s)")
            return False, duration, response.status_code
        else:
            print(f"❌ Connection {connection_id}: Unexpected status {response.status_code} ({duration:.2f}s)")
            return False, duration, response.status_code
            
    except Exception as e:
        duration = time.time() - start_time
        print(f"💥 Connection {connection_id}: Exception: {e} ({duration:.2f}s)")
        return False, duration, None

async def test_concurrent_connections(num_connections: int = 10):
    """Test multiple concurrent SSE connections."""
    print(f"🚀 Testing {num_connections} concurrent SSE connections...")
    print("📋 This tests the race condition fix during server startup")
    
    async with httpx.AsyncClient() as client:
        # Create concurrent connection tasks
        tasks = [
            test_sse_connection(client, i) 
            for i in range(1, num_connections + 1)
        ]
        
        # Execute all connections concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_duration = time.time() - start_time
        
        # Analyze results
        successful = 0
        not_ready = 0
        errors = 0
        durations = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"💥 Connection {i+1}: Task exception: {result}")
                errors += 1
            else:
                success, duration, status_code = result
                durations.append(duration)
                if success:
                    successful += 1
                elif status_code == 503:
                    not_ready += 1
                else:
                    errors += 1
        
        # Print summary
        print(f"\n📊 CONCURRENT CONNECTION TEST RESULTS")
        print(f"═══════════════════════════════════════")
        print(f"🎯 Total Connections: {num_connections}")
        print(f"✅ Successful: {successful}")
        print(f"⏳ Server Not Ready: {not_ready}")
        print(f"❌ Errors: {errors}")
        print(f"⏱️ Total Duration: {total_duration:.2f}s")
        
        if durations:
            print(f"📈 Average Response Time: {sum(durations)/len(durations):.2f}s")
            print(f"📈 Fastest Response: {min(durations):.2f}s")
            print(f"📈 Slowest Response: {max(durations):.2f}s")
        
        # Determine if race condition was avoided
        race_condition_detected = errors > (num_connections * 0.1)  # Allow 10% error tolerance
        
        if race_condition_detected:
            print(f"\n❌ RACE CONDITION DETECTED!")
            print(f"   Too many errors ({errors}/{num_connections}) - indicates race condition")
        else:
            print(f"\n✅ RACE CONDITION AVOIDED!")
            print(f"   Error rate acceptable ({errors}/{num_connections})")
        
        return not race_condition_detected

async def main():
    """Run the concurrent connection test."""
    print("🧪 Race Condition Fix Validation")
    print("==================================")
    
    # Test server health first
    print("\n🏥 Checking server health...")
    async with httpx.AsyncClient() as client:
        try:
            health_response = await client.get("http://localhost:8051/health")
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"✅ Server healthy: {health_data['status']}")
                print(f"🔧 Init complete: {health_data['initialization_complete']}")
            else:
                print(f"⚠️ Server health check returned: {health_response.status_code}")
        except Exception as e:
            print(f"❌ Cannot reach server: {e}")
            return False
    
    # Run concurrent connection test
    print(f"\n🔗 Testing concurrent SSE connections...")
    success = await test_concurrent_connections(15)
    
    if success:
        print(f"\n🎉 Race condition fix SUCCESSFUL!")
        print(f"   Server handles concurrent connections properly")
    else:
        print(f"\n💥 Race condition fix FAILED!")
        print(f"   Server still has race condition issues")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)