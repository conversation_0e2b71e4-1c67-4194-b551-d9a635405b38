#!/usr/bin/env python3
"""
Redis Queue Performance Benchmark
Comprehensive performance testing for the Redis queue system
"""

import asyncio
import time
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import List, Dict, Any
import psutil
import os

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue, JobRequest, JobStatus


@dataclass
class BenchmarkResult:
    operation: str
    total_time: float
    operations_per_second: float
    avg_operation_time: float
    min_operation_time: float
    max_operation_time: float
    memory_usage_mb: float
    success_rate: float
    total_operations: int


class RedisBenchmarkSuite:
    """Comprehensive Redis queue benchmark suite"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.results: List[BenchmarkResult] = []
        self.process = psutil.Process(os.getpid())
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024
    
    async def benchmark_enqueue_performance(self, num_jobs: int = 1000) -> BenchmarkResult:
        """Benchmark job enqueue performance"""
        print(f"🔄 Benchmarking enqueue performance ({num_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "benchmark-enqueue")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("benchmark-enqueue:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Create jobs
        jobs = [
            JobRequest.create(
                job_type="single_crawl",
                url=f"https://benchmark{i}.com",
                parameters={"chunk_size": 1000}
            ) for i in range(num_jobs)
        ]
        
        # Measure memory before
        initial_memory = self.get_memory_usage()
        
        # Benchmark enqueue operations
        operation_times = []
        successful_operations = 0
        
        start_time = time.time()
        
        for job in jobs:
            op_start = time.time()
            try:
                await queue.enqueue_job(job)
                successful_operations += 1
            except Exception as e:
                print(f"Enqueue failed: {e}")
            
            operation_times.append(time.time() - op_start)
        
        total_time = time.time() - start_time
        final_memory = self.get_memory_usage()
        
        await queue.disconnect()
        
        return BenchmarkResult(
            operation="enqueue",
            total_time=total_time,
            operations_per_second=successful_operations / total_time,
            avg_operation_time=statistics.mean(operation_times),
            min_operation_time=min(operation_times),
            max_operation_time=max(operation_times),
            memory_usage_mb=final_memory - initial_memory,
            success_rate=successful_operations / num_jobs,
            total_operations=num_jobs
        )
    
    async def benchmark_dequeue_performance(self, num_jobs: int = 1000) -> BenchmarkResult:
        """Benchmark job dequeue performance"""
        print(f"🔄 Benchmarking dequeue performance ({num_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "benchmark-dequeue")
        await queue.connect()
        
        # Clean up and populate queue
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("benchmark-dequeue:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Pre-populate queue with jobs
        jobs = [
            JobRequest.create(
                job_type="single_crawl",
                url=f"https://dequeue{i}.com",
                parameters={}
            ) for i in range(num_jobs)
        ]
        
        for job in jobs:
            await queue.enqueue_job(job)
        
        # Measure memory before
        initial_memory = self.get_memory_usage()
        
        # Benchmark dequeue operations
        operation_times = []
        successful_operations = 0
        
        start_time = time.time()
        
        for _ in range(num_jobs):
            op_start = time.time()
            try:
                job = await queue.dequeue_job()
                if job is not None:
                    successful_operations += 1
            except Exception as e:
                print(f"Dequeue failed: {e}")
            
            operation_times.append(time.time() - op_start)
        
        total_time = time.time() - start_time
        final_memory = self.get_memory_usage()
        
        await queue.disconnect()
        
        return BenchmarkResult(
            operation="dequeue",
            total_time=total_time,
            operations_per_second=successful_operations / total_time,
            avg_operation_time=statistics.mean(operation_times),
            min_operation_time=min(operation_times),
            max_operation_time=max(operation_times),
            memory_usage_mb=final_memory - initial_memory,
            success_rate=successful_operations / num_jobs,
            total_operations=num_jobs
        )
    
    async def benchmark_concurrent_operations(self, num_jobs: int = 500, concurrency: int = 10) -> BenchmarkResult:
        """Benchmark concurrent queue operations"""
        print(f"🔄 Benchmarking concurrent operations ({num_jobs} jobs, {concurrency} concurrent)...")
        
        queue = RedisJobQueue(self.redis_url, "benchmark-concurrent")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("benchmark-concurrent:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Measure memory before
        initial_memory = self.get_memory_usage()
        
        async def concurrent_enqueue_dequeue(batch_size: int) -> int:
            """Perform concurrent enqueue and dequeue operations"""
            successful_ops = 0
            
            # Create jobs for this batch
            jobs = [
                JobRequest.create(
                    job_type="concurrent_test",
                    url=f"https://concurrent{i}.com",
                    parameters={}
                ) for i in range(batch_size)
            ]
            
            # Enqueue jobs
            enqueue_tasks = [queue.enqueue_job(job) for job in jobs]
            await asyncio.gather(*enqueue_tasks, return_exceptions=True)
            
            # Dequeue jobs
            dequeue_tasks = [queue.dequeue_job() for _ in range(batch_size)]
            results = await asyncio.gather(*dequeue_tasks, return_exceptions=True)
            
            # Count successful operations
            for result in results:
                if not isinstance(result, Exception) and result is not None:
                    successful_ops += 1
            
            return successful_ops
        
        # Run concurrent batches
        batch_size = num_jobs // concurrency
        start_time = time.time()
        
        concurrent_tasks = [
            concurrent_enqueue_dequeue(batch_size) 
            for _ in range(concurrency)
        ]
        
        batch_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        total_time = time.time() - start_time
        final_memory = self.get_memory_usage()
        
        # Calculate success rate
        successful_operations = sum(
            result for result in batch_results 
            if not isinstance(result, Exception)
        )
        
        await queue.disconnect()
        
        return BenchmarkResult(
            operation="concurrent",
            total_time=total_time,
            operations_per_second=successful_operations / total_time,
            avg_operation_time=total_time / successful_operations if successful_operations > 0 else 0,
            min_operation_time=0,  # Not measured for concurrent ops
            max_operation_time=0,  # Not measured for concurrent ops
            memory_usage_mb=final_memory - initial_memory,
            success_rate=successful_operations / num_jobs,
            total_operations=num_jobs
        )
    
    async def benchmark_status_updates(self, num_jobs: int = 500) -> BenchmarkResult:
        """Benchmark job status update operations"""
        print(f"🔄 Benchmarking status updates ({num_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "benchmark-status")
        await queue.connect()
        
        # Clean up and create jobs
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("benchmark-status:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Create and enqueue jobs
        job_ids = []
        for i in range(num_jobs):
            job = JobRequest.create(
                job_type="status_test",
                url=f"https://status{i}.com",
                parameters={}
            )
            job_id = await queue.enqueue_job(job)
            job_ids.append(job_id)
        
        # Measure memory before
        initial_memory = self.get_memory_usage()
        
        # Benchmark status update operations
        operation_times = []
        successful_operations = 0
        
        start_time = time.time()
        
        for job_id in job_ids:
            op_start = time.time()
            try:
                # Simulate job lifecycle: queued -> processing -> completed
                await queue.update_job_status(job_id, JobStatus.PROCESSING)
                await queue.update_job_status(
                    job_id, 
                    JobStatus.COMPLETED, 
                    result_data={"success": True, "processed": True}
                )
                successful_operations += 1
            except Exception as e:
                print(f"Status update failed: {e}")
            
            operation_times.append(time.time() - op_start)
        
        total_time = time.time() - start_time
        final_memory = self.get_memory_usage()
        
        await queue.disconnect()
        
        return BenchmarkResult(
            operation="status_update",
            total_time=total_time,
            operations_per_second=successful_operations / total_time,
            avg_operation_time=statistics.mean(operation_times),
            min_operation_time=min(operation_times),
            max_operation_time=max(operation_times),
            memory_usage_mb=final_memory - initial_memory,
            success_rate=successful_operations / num_jobs,
            total_operations=num_jobs
        )
    
    async def benchmark_large_payloads(self, num_jobs: int = 100, payload_size_kb: int = 50) -> BenchmarkResult:
        """Benchmark operations with large job payloads"""
        print(f"🔄 Benchmarking large payloads ({num_jobs} jobs, {payload_size_kb}KB each)...")
        
        queue = RedisJobQueue(self.redis_url, "benchmark-large")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("benchmark-large:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Create large payload
        large_payload = "x" * (payload_size_kb * 1024)
        
        # Measure memory before
        initial_memory = self.get_memory_usage()
        
        # Benchmark with large payloads
        operation_times = []
        successful_operations = 0
        
        start_time = time.time()
        
        # Enqueue large jobs
        for i in range(num_jobs):
            op_start = time.time()
            try:
                job = JobRequest.create(
                    job_type="large_payload_test",
                    url=f"https://large{i}.com",
                    parameters={"large_data": large_payload}
                )
                await queue.enqueue_job(job)
                successful_operations += 1
            except Exception as e:
                print(f"Large payload operation failed: {e}")
            
            operation_times.append(time.time() - op_start)
        
        total_time = time.time() - start_time
        final_memory = self.get_memory_usage()
        
        await queue.disconnect()
        
        return BenchmarkResult(
            operation="large_payload",
            total_time=total_time,
            operations_per_second=successful_operations / total_time,
            avg_operation_time=statistics.mean(operation_times),
            min_operation_time=min(operation_times),
            max_operation_time=max(operation_times),
            memory_usage_mb=final_memory - initial_memory,
            success_rate=successful_operations / num_jobs,
            total_operations=num_jobs
        )
    
    async def run_full_benchmark_suite(self) -> Dict[str, Any]:
        """Run complete benchmark suite"""
        print("🚀 Starting Redis Queue Performance Benchmark Suite")
        print("=" * 60)
        
        # Test Redis connectivity
        try:
            test_queue = RedisJobQueue(self.redis_url, "connectivity-test")
            await test_queue.connect()
            await test_queue.disconnect()
            print("✅ Redis connectivity confirmed")
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            return {"error": "Redis connection failed"}
        
        benchmarks = [
            ("Enqueue Performance", self.benchmark_enqueue_performance, 1000),
            ("Dequeue Performance", self.benchmark_dequeue_performance, 1000),
            ("Concurrent Operations", self.benchmark_concurrent_operations, 500),
            ("Status Updates", self.benchmark_status_updates, 500),
            ("Large Payloads", self.benchmark_large_payloads, 100),
        ]
        
        results = {}
        
        for name, benchmark_func, *args in benchmarks:
            print(f"\n📊 Running {name}...")
            try:
                if args:
                    result = await benchmark_func(*args)
                else:
                    result = await benchmark_func()
                
                results[name] = result
                self.results.append(result)
                
                print(f"   ✅ {result.operations_per_second:.2f} ops/sec")
                print(f"   ⏱️  Avg: {result.avg_operation_time*1000:.2f}ms")
                print(f"   💾 Memory: {result.memory_usage_mb:.2f}MB")
                print(f"   ✔️  Success: {result.success_rate*100:.1f}%")
                
            except Exception as e:
                print(f"   ❌ Benchmark failed: {e}")
                results[name] = {"error": str(e)}
        
        return results
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive benchmark report"""
        report = []
        report.append("# Redis Queue Performance Benchmark Report")
        report.append("=" * 50)
        report.append(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Redis URL: {self.redis_url}")
        report.append("")
        
        # Summary table
        report.append("## Performance Summary")
        report.append("| Operation | Ops/Sec | Avg Time (ms) | Memory (MB) | Success Rate |")
        report.append("|-----------|---------|---------------|-------------|--------------|")
        
        for result in self.results:
            report.append(
                f"| {result.operation} | {result.operations_per_second:.2f} | "
                f"{result.avg_operation_time*1000:.2f} | {result.memory_usage_mb:.2f} | "
                f"{result.success_rate*100:.1f}% |"
            )
        
        report.append("")
        
        # Detailed results
        report.append("## Detailed Results")
        
        for result in self.results:
            report.append(f"\n### {result.operation.title()} Performance")
            report.append(f"- **Total Operations**: {result.total_operations:,}")
            report.append(f"- **Total Time**: {result.total_time:.2f} seconds")
            report.append(f"- **Throughput**: {result.operations_per_second:.2f} operations/second")
            report.append(f"- **Average Operation Time**: {result.avg_operation_time*1000:.2f}ms")
            report.append(f"- **Min Operation Time**: {result.min_operation_time*1000:.2f}ms")
            report.append(f"- **Max Operation Time**: {result.max_operation_time*1000:.2f}ms")
            report.append(f"- **Memory Usage**: {result.memory_usage_mb:.2f}MB")
            report.append(f"- **Success Rate**: {result.success_rate*100:.1f}%")
        
        # Performance assessment
        report.append("\n## Performance Assessment")
        
        # Analyze results
        avg_throughput = statistics.mean([r.operations_per_second for r in self.results])
        avg_success_rate = statistics.mean([r.success_rate for r in self.results])
        total_memory = sum([r.memory_usage_mb for r in self.results])
        
        if avg_throughput > 500:
            report.append("✅ **Excellent throughput** - System can handle high-volume operations")
        elif avg_throughput > 200:
            report.append("⚠️ **Good throughput** - Suitable for moderate workloads")
        else:
            report.append("❌ **Low throughput** - May need optimization for production use")
        
        if avg_success_rate > 0.95:
            report.append("✅ **High reliability** - Excellent error handling")
        elif avg_success_rate > 0.90:
            report.append("⚠️ **Good reliability** - Acceptable for most use cases")
        else:
            report.append("❌ **Low reliability** - Error handling needs improvement")
        
        if total_memory < 100:
            report.append("✅ **Efficient memory usage** - Low memory footprint")
        elif total_memory < 500:
            report.append("⚠️ **Moderate memory usage** - Monitor in production")
        else:
            report.append("❌ **High memory usage** - Consider memory optimization")
        
        return "\n".join(report)


async def main():
    """Run the benchmark suite and generate report"""
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    benchmark = RedisBenchmarkSuite(redis_url)
    
    # Run benchmarks
    results = await benchmark.run_full_benchmark_suite()
    
    # Generate and save report
    report = benchmark.generate_report(results)
    
    # Save to file
    report_file = "redis_queue_benchmark_report.md"
    with open(report_file, "w") as f:
        f.write(report)
    
    # Save raw results as JSON
    results_file = "redis_queue_benchmark_results.json"
    with open(results_file, "w") as f:
        json.dump({
            "timestamp": time.time(),
            "results": [
                {
                    "operation": r.operation,
                    "total_time": r.total_time,
                    "operations_per_second": r.operations_per_second,
                    "avg_operation_time": r.avg_operation_time,
                    "min_operation_time": r.min_operation_time,
                    "max_operation_time": r.max_operation_time,
                    "memory_usage_mb": r.memory_usage_mb,
                    "success_rate": r.success_rate,
                    "total_operations": r.total_operations
                } for r in benchmark.results
            ]
        }, f, indent=2)
    
    print("\n" + "=" * 60)
    print("🎉 Benchmark Complete!")
    print(f"📄 Report saved to: {report_file}")
    print(f"📊 Raw results saved to: {results_file}")
    print("=" * 60)
    print("\n" + report)


if __name__ == "__main__":
    asyncio.run(main())