#!/usr/bin/env python3
"""
Test URL Collection - Comprehensive Testing of Conservative Filtering Fix

This script tests multiple documentation URLs to verify that our conservative
filtering approach correctly preserves main content while removing boilerplate.
"""

import requests
import json
import time
from datetime import datetime

# Test URLs provided by user
TEST_URLS = [
    {
        "url": "https://flask.palletsprojects.com/en/3.0.x/quickstart/",
        "name": "Flask Quickstart",
        "expected_type": "documentation",
        "expected_min_chars": 5000,
        "expected_code_examples": 5,
        "key_content": ["Flask", "minimal application", "@app.route", "debug mode"]
    },
    {
        "url": "https://httpbin.org/html",  # Fixed URL
        "name": "HTTPBin HTML Test",
        "expected_type": "testing",
        "expected_min_chars": 200,
        "expected_code_examples": 0,
        "key_content": ["Herman Melville", "Moby Dick"]
    },
    {
        "url": "https://www.python.org/dev/peps/pep-8/",
        "name": "PEP 8 Style Guide",
        "expected_type": "documentation",
        "expected_min_chars": 8000,
        "expected_code_examples": 10,
        "key_content": ["style guide", "code formatting", "naming conventions"]
    },
    {
        "url": "https://docs.python.org/3/tutorial/datastructures.html",
        "name": "Python Data Structures Tutorial",
        "expected_type": "tutorial",
        "expected_min_chars": 6000,
        "expected_code_examples": 15,
        "key_content": ["list", "dictionary", "tuple", "data structures"]
    },
    {
        "url": "https://docs.python.org/3/tutorial/controlflow.html",
        "name": "Python Control Flow Tutorial",
        "expected_type": "tutorial",
        "expected_min_chars": 5000,
        "expected_code_examples": 20,
        "key_content": ["if statement", "for loop", "while", "control flow"]
    }
]

MCP_SERVER_URL = "http://localhost:8051"


def test_mcp_server_health():
    """Test that the MCP server is accessible"""
    try:
        # Try to get available sources as a health check
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": "get_available_sources",
                "arguments": {}
            }
        }
        
        response = requests.post(
            f"{MCP_SERVER_URL}/messages",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ MCP Server is healthy and responding")
            return True
        else:
            print(f"❌ MCP Server returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MCP Server not accessible: {e}")
        return False


def crawl_url(url, test_name):
    """Crawl a single URL and return the results"""
    print(f"\n🕷️  Crawling: {test_name}")
    print(f"   URL: {url}")
    
    payload = {
        "jsonrpc": "2.0",
        "id": int(time.time()),
        "method": "tools/call",
        "params": {
            "name": "smart_crawl_url",
            "arguments": {
                "url": url,
                "max_depth": 1,
                "max_concurrent": 2,
                "chunk_size": 5000
            }
        }
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/messages",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if "error" in result:
                print(f"   ❌ Crawl failed: {result['error']}")
                return None
            else:
                # Extract result from MCP response
                if "result" in result and "content" in result["result"]:
                    return json.loads(result["result"]["content"])
                else:
                    print(f"   ❌ Unexpected response format: {result}")
                    return None
        else:
            print(f"   ❌ HTTP error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Crawl error: {e}")
        return None


def analyze_crawl_result(result, test_config):
    """Analyze crawl results against expectations"""
    if not result:
        return {
            "success": False,
            "score": 0,
            "issues": ["Crawl failed completely"]
        }
    
    issues = []
    score = 0
    max_score = 100
    
    # Check basic success
    if not result.get("success", False):
        issues.append("Crawl reported as unsuccessful")
        return {"success": False, "score": 0, "issues": issues}
    
    # Extract metrics
    content_length = result.get("content_length", 0)
    total_words = result.get("total_word_count", 0)
    code_examples = result.get("code_examples_stored", 0)
    chunks_stored = result.get("chunks_stored", 0)
    
    print(f"   📊 Content length: {content_length} chars")
    print(f"   📊 Word count: {total_words} words")
    print(f"   📊 Code examples: {code_examples}")
    print(f"   📊 Chunks stored: {chunks_stored}")
    
    # Test 1: Content Length (30 points)
    if content_length >= test_config["expected_min_chars"]:
        score += 30
        print(f"   ✅ Content length adequate ({content_length} >= {test_config['expected_min_chars']})")
    else:
        issues.append(f"Content too short: {content_length} < {test_config['expected_min_chars']} chars")
        print(f"   ❌ Content too short: {content_length} < {test_config['expected_min_chars']}")
    
    # Test 2: Word Count (20 points)
    expected_words = test_config["expected_min_chars"] // 6  # Rough estimate
    if total_words >= expected_words:
        score += 20
        print(f"   ✅ Word count adequate ({total_words} words)")
    else:
        issues.append(f"Word count low: {total_words} words")
        print(f"   ⚠️  Word count: {total_words} words (expected ~{expected_words})")
    
    # Test 3: Code Examples (20 points) - if expected
    if test_config["expected_code_examples"] > 0:
        if code_examples >= test_config["expected_code_examples"]:
            score += 20
            print(f"   ✅ Code examples found ({code_examples})")
        else:
            issues.append(f"Few code examples: {code_examples} < {test_config['expected_code_examples']}")
            print(f"   ⚠️  Code examples: {code_examples} < {test_config['expected_code_examples']}")
    else:
        score += 20  # Skip this test if no code examples expected
    
    # Test 4: Chunks Stored (15 points)
    if chunks_stored > 0:
        score += 15
        print(f"   ✅ Content chunked properly ({chunks_stored} chunks)")
    else:
        issues.append("No content chunks stored")
        print(f"   ❌ No content chunks stored")
    
    # Test 5: Key Content Detection (15 points)
    # This would require getting the actual content, so we'll estimate based on metrics
    if content_length > 500 and total_words > 50:
        score += 15
        print(f"   ✅ Likely contains meaningful content")
    else:
        issues.append("Content appears too minimal for key content")
        print(f"   ⚠️  Content may be too minimal")
    
    success = len(issues) == 0 and score >= 70
    
    if success:
        print(f"   🎉 PASSED (Score: {score}/{max_score})")
    elif score >= 50:
        print(f"   ⚠️  PARTIAL (Score: {score}/{max_score})")
    else:
        print(f"   ❌ FAILED (Score: {score}/{max_score})")
    
    return {
        "success": success,
        "score": score,
        "max_score": max_score,
        "issues": issues,
        "metrics": {
            "content_length": content_length,
            "total_words": total_words,
            "code_examples": code_examples,
            "chunks_stored": chunks_stored
        }
    }


def test_regression_check():
    """Test that our fix addresses the original regression"""
    print("\n🔍 REGRESSION CHECK: Testing Flask Quickstart Specifically")
    print("=" * 70)
    
    flask_url = "https://flask.palletsprojects.com/en/3.0.x/quickstart/"
    result = crawl_url(flask_url, "Flask Quickstart (Regression Check)")
    
    if not result:
        print("❌ REGRESSION: Flask crawling still fails completely")
        return False
    
    content_length = result.get("content_length", 0)
    
    # Original issue: only 86 characters extracted
    if content_length <= 100:
        print(f"❌ REGRESSION: Still only extracting {content_length} chars (was 86)")
        return False
    elif content_length < 1000:
        print(f"⚠️  IMPROVEMENT: Now extracting {content_length} chars but still low")
        return False
    else:
        print(f"✅ FIXED: Now extracting {content_length} chars (was 86)")
        return True


def main():
    """Run comprehensive URL testing"""
    print("🧪 URL Collection Testing - Conservative Filtering Verification")
    print("=" * 80)
    print(f"Testing {len(TEST_URLS)} URLs to verify content extraction improvements")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Health check
    if not test_mcp_server_health():
        print("\n❌ Cannot proceed - MCP Server not accessible")
        return False
    
    # Regression check first
    regression_fixed = test_regression_check()
    
    # Test all URLs
    print("\n🌐 COMPREHENSIVE URL TESTING")
    print("=" * 70)
    
    results = []
    total_score = 0
    max_total_score = 0
    
    for i, test_config in enumerate(TEST_URLS, 1):
        print(f"\n[{i}/{len(TEST_URLS)}] Testing: {test_config['name']}")
        
        result = crawl_url(test_config["url"], test_config["name"])
        analysis = analyze_crawl_result(result, test_config)
        
        results.append({
            "config": test_config,
            "analysis": analysis,
            "raw_result": result
        })
        
        total_score += analysis["score"]
        max_total_score += analysis["max_score"]
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TESTING SUMMARY")
    print("=" * 80)
    
    print(f"🔄 Regression Check: {'✅ FIXED' if regression_fixed else '❌ NOT FIXED'}")
    print(f"📈 Overall Score: {total_score}/{max_total_score} ({total_score/max_total_score*100:.1f}%)")
    
    passed = sum(1 for r in results if r["analysis"]["success"])
    print(f"✅ Tests Passed: {passed}/{len(results)}")
    
    # Individual results
    for i, result in enumerate(results, 1):
        config = result["config"]
        analysis = result["analysis"]
        status = "✅ PASS" if analysis["success"] else "❌ FAIL"
        print(f"   {status} {config['name']}: {analysis['score']}/{analysis['max_score']}")
        
        if analysis["issues"]:
            for issue in analysis["issues"]:
                print(f"      • {issue}")
    
    # Recommendations
    print(f"\n🔧 RECOMMENDATIONS")
    if regression_fixed and passed >= len(results) * 0.8:
        print("✅ Content extraction is working well!")
        print("   • Conservative filtering successfully preserves main content")
        print("   • All major documentation sites work correctly")
        print("   • Ready for production use")
    elif regression_fixed:
        print("⚠️  Partial success - some sites need adjustment")
        print("   • Main regression is fixed")
        print("   • Some content types may need fine-tuning")
        print("   • Consider site-specific optimizations")
    else:
        print("❌ Critical issues remain")
        print("   • Original regression not fully fixed")
        print("   • Need to further reduce filtering aggressiveness")
        print("   • Check CSS selector configuration")
    
    return regression_fixed and passed >= len(results) * 0.6


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)