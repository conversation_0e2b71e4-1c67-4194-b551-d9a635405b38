{"Extreme Concurrent Load": {"test_name": "extreme_concurrent_load", "total_jobs": 5000, "max_workers": 50, "total_operations": 19671, "successful_enqueues": 4941, "successful_dequeues": 4848, "successful_status_updates": 9882, "total_errors": 59, "success_rate": 0.98355, "total_time": 5.573856592178345, "operations_per_second": 3529.1543071997635, "resource_usage": {"initial_memory_mb": 30.4609375, "final_memory_mb": 55.40625, "peak_memory_mb": 55.40625, "avg_memory_mb": 53.233723958333336, "memory_increase_mb": 24.9453125, "peak_cpu_percent": 101.9, "avg_cpu_percent": 91.55833333333334, "final_cpu_percent": 104.1}, "performance_grade": "A+ (Excellent)"}, "Memory Pressure Resistance": {"test_name": "memory_pressure_resistance", "total_large_jobs": 100, "payload_size_mb": 5, "successful_operations": 100, "memory_errors": 0, "success_rate": 1.0, "total_time": 2.6374764442443848, "memory_stats": {"initial_memory_mb": 55.40625, "final_memory_mb": 65.16796875, "peak_memory_mb": 65.1640625, "avg_memory_mb": 64.58671875, "memory_increase_mb": 9.76171875, "estimated_peak_usage_mb": 65.1640625}, "memory_efficiency": "Excellent (< 1.2x theoretical)", "memory_resilient": true}, "Connection Pool Stress": {"test_name": "connection_pool_stress", "concurrent_connections": 20, "operations_per_connection": 100, "total_operations": 2000, "successful_operations": 2000, "connection_errors": 0, "success_rate": 1.0, "total_time": 1.7327725887298584, "operations_per_second": 1154.219551375765, "connection_efficiency": 1.0, "resource_impact": {"memory_increase_mb": 0.0, "cpu_usage": 99.2}}, "overall_stress_test_passed": true, "test_timestamp": "2025-07-21T12:22:18.815521+00:00", "performance_score": "3/3 tests passed"}