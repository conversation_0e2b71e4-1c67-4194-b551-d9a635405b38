#!/usr/bin/env python3
"""
MCP Client Integration Demo

This script demonstrates how to use the MCP client wrapper
to communicate with the AY RAG MCP Server.
"""
import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from dashboard.mcp_client import MCPClientWrapper, CrawlOptions, SearchFilters


async def demo_health_check():
    """Demonstrate health check functionality"""
    print("=== Health Check Demo ===")
    
    async with MC<PERSON>lient<PERSON>rapper() as client:
        print(f"Connecting to: {client.server_url}")
        
        # Check server health
        health = await client.health_check()
        print(f"Server Status: {health.status}")
        print(f"Initialization Complete: {health.initialization_complete}")
        print(f"Components: {health.components}")
        
        # Get connection info
        info = client.get_connection_info()
        print(f"Connection State: {info['connection_state']}")
        print(f"Circuit Breaker State: {info['circuit_breaker_state']}")


async def demo_crawl_operation():
    """Demonstrate crawling functionality"""
    print("\n=== Crawl Operation Demo ===")
    
    async with MCPClientWrapper() as client:
        # Configure crawl options
        options = CrawlOptions(
            max_depth=2,
            max_concurrent=5,
            chunk_size=3000
        )
        
        # Crawl a test URL
        test_url = "https://example.com"
        print(f"Crawling: {test_url}")
        
        result = await client.crawl_url(test_url, options)
        
        print(f"Success: {result.success}")
        if result.success:
            print(f"Chunks Stored: {result.chunks_stored}")
            print(f"Content Length: {result.content_length}")
            print(f"Source ID: {result.source_id}")
        else:
            print(f"Error: {result.error}")


async def demo_search_operation():
    """Demonstrate search functionality"""
    print("\n=== Search Operation Demo ===")
    
    async with MCPClientWrapper() as client:
        # Configure search filters
        filters = SearchFilters(
            max_results=10,
            min_score=0.5
        )
        
        # Perform search
        query = "example content"
        print(f"Searching for: {query}")
        
        result = await client.search_documents(query, filters)
        
        print(f"Success: {result.success}")
        if result.success:
            print(f"Total Results: {result.total_results}")
            print(f"Execution Time: {result.execution_time:.3f}s")
            
            for i, doc in enumerate(result.results[:3]):  # Show first 3 results
                print(f"  Result {i+1}: {doc.get('content', '')[:100]}...")
        else:
            print(f"Error: {result.error}")


async def demo_circuit_breaker():
    """Demonstrate circuit breaker functionality"""
    print("\n=== Circuit Breaker Demo ===")
    
    # Create client with aggressive circuit breaker settings for demo
    client = MCPClientWrapper()
    client.circuit_breaker.failure_threshold = 2
    client.circuit_breaker.recovery_timeout = 5
    
    print(f"Initial Circuit Breaker State: {client.circuit_breaker.state.value}")
    
    # Simulate failures
    for i in range(3):
        client.circuit_breaker.record_failure()
        print(f"After failure {i+1}: {client.circuit_breaker.state.value}")
        print(f"Can Execute: {client.circuit_breaker.can_execute()}")
    
    # Simulate recovery
    print("\nWaiting for recovery timeout...")
    await asyncio.sleep(6)  # Wait longer than recovery timeout
    
    print(f"After timeout - Can Execute: {client.circuit_breaker.can_execute()}")
    print(f"Circuit Breaker State: {client.circuit_breaker.state.value}")
    
    # Simulate success
    client.circuit_breaker.record_success()
    print(f"After success: {client.circuit_breaker.state.value}")


async def main():
    """Run all demos"""
    print("MCP Client Integration Demo")
    print("=" * 40)
    
    try:
        await demo_health_check()
        await demo_circuit_breaker()
        
        # Only run crawl and search demos if server is available
        async with MCPClientWrapper() as client:
            health = await client.health_check()
            if health.status == "healthy":
                await demo_crawl_operation()
                await demo_search_operation()
            else:
                print(f"\nSkipping crawl/search demos - server not healthy: {health.status}")
                
    except Exception as e:
        print(f"Demo failed: {e}")
        print("Make sure the AY RAG MCP Server is running on the configured URL")


if __name__ == "__main__":
    asyncio.run(main())