#!/usr/bin/env python3
"""
Test Script for Crawling Fix Verification

This script tests the actual crawling functionality to verify that the CSS selector
bug fix works correctly in production. It tests both single-page and smart crawling
with the enhanced content filtering.
"""

import asyncio
import json
import sys
import requests
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

MCP_SERVER_URL = "http://localhost:8051"


def test_mcp_server_ready():
    """Test that the MCP server is ready and accessible"""
    try:
        response = requests.get(f"{MCP_SERVER_URL}/ready", timeout=10)
        if response.status_code == 200:
            print("✅ MCP Server is ready and accessible")
            return True
        else:
            print(f"❌ MCP Server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MCP Server not accessible: {e}")
        return False


def test_crawl_single_page():
    """Test single page crawling with the CSS selector fix"""
    print("\n🔍 Testing Single Page Crawling...")
    
    test_url = "https://httpbin.org/json"
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "crawl_single_page",
            "arguments": {
                "url": test_url
            }
        }
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/messages",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print(f"❌ Single page crawling failed: {result['error']}")
                return False
            else:
                print("✅ Single page crawling successful")
                print(f"   Response: {str(result)[:200]}...")
                return True
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Single page crawling error: {e}")
        return False


def test_smart_crawl():
    """Test smart crawling with enhanced content filtering"""
    print("\n🕷️  Testing Smart Crawling...")
    
    test_url = "https://httpbin.org/json"  # Simple test URL to avoid complexity
    payload = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/call",
        "params": {
            "name": "smart_crawl_url",
            "arguments": {
                "url": test_url,
                "max_depth": 1,  # Keep it simple for testing
                "max_concurrent": 2
            }
        }
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/messages",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # Longer timeout for smart crawl
        )
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print(f"❌ Smart crawling failed: {result['error']}")
                # Check if it's the CSS selector error
                error_msg = str(result['error'])
                if "'list' object has no attribute 'split'" in error_msg:
                    print("🚨 CSS SELECTOR BUG DETECTED! The fix didn't work!")
                    return False
                return False
            else:
                print("✅ Smart crawling successful")
                print(f"   Response: {str(result)[:200]}...")
                return True
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Smart crawling error: {e}")
        return False


def test_enhanced_content_filtering():
    """Test that enhanced content filtering is working"""
    print("\n🧹 Testing Enhanced Content Filtering...")
    
    # Test with a documentation URL that should trigger enhanced filtering
    test_url = "https://httpbin.org/html"
    payload = {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "tools/call",
        "params": {
            "name": "crawl_single_page",
            "arguments": {
                "url": test_url
            }
        }
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/messages",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                error_msg = str(result['error'])
                if "'list' object has no attribute 'split'" in error_msg:
                    print("🚨 CSS SELECTOR BUG DETECTED! Enhanced filtering not working!")
                    return False
                else:
                    print(f"❌ Other error in enhanced filtering: {result['error']}")
                    return False
            else:
                print("✅ Enhanced content filtering working")
                # Check if there's any mention of content reduction (indicating filtering worked)
                response_str = str(result)
                if "reduction" in response_str.lower():
                    print("   🧹 Content filtering applied successfully")
                return True
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced filtering test error: {e}")
        return False


def test_get_available_sources():
    """Test that the database integration still works"""
    print("\n📊 Testing Database Integration...")
    
    payload = {
        "jsonrpc": "2.0",
        "id": 4,
        "method": "tools/call",
        "params": {
            "name": "get_available_sources",
            "arguments": {}
        }
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/messages",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print(f"❌ Database integration failed: {result['error']}")
                return False
            else:
                print("✅ Database integration working")
                return True
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Database integration error: {e}")
        return False


def main():
    """Run all tests to verify the crawling fix"""
    print("🚀 CRAWLING FIX VERIFICATION TEST SUITE")
    print("=" * 50)
    print("Testing the CSS selector bug fix and enhanced content filtering")
    
    tests = [
        ("Server Ready", test_mcp_server_ready),
        ("Single Page Crawling", test_crawl_single_page),
        ("Smart Crawling", test_smart_crawl),
        ("Enhanced Content Filtering", test_enhanced_content_filtering),
        ("Database Integration", test_get_available_sources),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
        
        if not success:
            print(f"🔴 {test_name} FAILED")
        else:
            print(f"🟢 {test_name} PASSED")
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("   ✅ CSS selector bug is fixed")
        print("   ✅ Crawling functionality restored") 
        print("   ✅ Enhanced content filtering working")
        print("   ✅ Production ready")
        return True
    else:
        print("\n🚨 SOME TESTS FAILED!")
        print("   ❌ Issues still exist")
        print("   ⚠️  Not ready for production")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)