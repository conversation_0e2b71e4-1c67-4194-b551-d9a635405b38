{"Network Interruption Recovery": {"test_name": "network_interruption_recovery", "total_jobs": 100, "successful_before_interruption": 33, "failed_during_interruption": 0, "successful_after_recovery": 33, "connection_errors": 0, "accessible_jobs": 99, "total_created_jobs": 99, "recovery_rate": 1.0, "data_persistence_rate": 1.0, "network_resilient": true}, "Malformed Data Handling": {"test_name": "malformed_data_handling", "total_test_cases": 6, "successful_recoveries": 1, "graceful_failures": 5, "system_crashes": 0, "robustness_score": 1.0, "data_corruption_resilient": true}, "Job Timeout Handling": {"test_name": "job_timeout_handling", "timeout_jobs": 20, "cleaned_jobs": 0, "jobs_marked_failed": 0, "cleanup_effectiveness": 0.0, "timeout_handling_works": false}, "Redis Reconnection": {"test_name": "redis_reconnection", "test_operations": 50, "successful_ops_before": 25, "successful_ops_after": 25, "reconnection_errors": 0, "reconnection_success_rate": 1.0, "reconnection_works": true}, "Concurrent Error Scenarios": {"test_name": "concurrent_error_scenarios", "error_jobs": 100, "total_successful": 41, "total_handled_errors": 43, "total_system_failures": 0, "error_handling_rate": 1.0, "total_time": 0.09008264541625977, "error_resilient": true}, "overall_resilience_score": 4, "total_resilience_tests": 5, "resilience_percentage": 80.0, "system_resilient": true, "test_timestamp": "2025-07-21T12:24:09.616399+00:00"}