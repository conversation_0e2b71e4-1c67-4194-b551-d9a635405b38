-- Database schema updates for content risk tracking
-- Add these columns to existing tables to support content validation

-- Update crawled_pages table to include risk tracking
ALTER TABLE crawled_pages 
ADD COLUMN IF NOT EXISTS risk_level VARCHAR(20) DEFAULT 'safe' CHECK (risk_level IN ('safe', 'suspicious', 'blocked')),
ADD COLUMN IF NOT EXISTS risk_reasons TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS risk_confidence DECIMAL(3,2) DEFAULT 0.0 CHECK (risk_confidence >= 0.0 AND risk_confidence <= 1.0),
ADD COLUMN IF NOT EXISTS validated_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
ADD COLUMN IF NOT EXISTS validation_version VARCHAR(10) DEFAULT '1.0';

-- Update code_examples table to include risk tracking
ALTER TABLE code_examples 
ADD COLUMN IF NOT EXISTS risk_level VARCHAR(20) DEFAULT 'safe' CHECK (risk_level IN ('safe', 'suspicious', 'blocked')),
ADD COLUMN IF NOT EXISTS risk_reasons TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS risk_confidence DECIMAL(3,2) DEFAULT 0.0 CHECK (risk_confidence >= 0.0 AND risk_confidence <= 1.0),
ADD COLUMN IF NOT EXISTS validated_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- Create indexes for efficient querying by risk level
CREATE INDEX IF NOT EXISTS idx_crawled_pages_risk_level ON crawled_pages(risk_level);
CREATE INDEX IF NOT EXISTS idx_crawled_pages_validated_at ON crawled_pages(validated_at);
CREATE INDEX IF NOT EXISTS idx_code_examples_risk_level ON code_examples(risk_level);

-- Create a view for content management dashboard
CREATE OR REPLACE VIEW content_risk_summary AS
SELECT 
    risk_level,
    COUNT(*) as total_items,
    COUNT(CASE WHEN validated_at IS NOT NULL THEN 1 END) as validated_items,
    AVG(risk_confidence) as avg_confidence,
    MIN(created_at) as oldest_content,
    MAX(created_at) as newest_content
FROM crawled_pages
GROUP BY risk_level;

-- Create a function to automatically validate content on insert/update
CREATE OR REPLACE FUNCTION validate_content_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Only validate if risk_level is not already set or content has changed
    IF (TG_OP = 'INSERT' AND NEW.risk_level = 'safe') OR 
       (TG_OP = 'UPDATE' AND (OLD.content IS DISTINCT FROM NEW.content OR OLD.risk_level IS DISTINCT FROM NEW.risk_level)) THEN
        
        -- Set validation timestamp
        NEW.validated_at = NOW();
        
        -- Note: Actual content validation would be done by the application
        -- This trigger just ensures the timestamp is set
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic validation timestamping
DROP TRIGGER IF EXISTS crawled_pages_validate_trigger ON crawled_pages;
CREATE TRIGGER crawled_pages_validate_trigger
    BEFORE INSERT OR UPDATE ON crawled_pages
    FOR EACH ROW
    EXECUTE FUNCTION validate_content_trigger();

DROP TRIGGER IF EXISTS code_examples_validate_trigger ON code_examples;
CREATE TRIGGER code_examples_validate_trigger
    BEFORE INSERT OR UPDATE ON code_examples
    FOR EACH ROW
    EXECUTE FUNCTION validate_content_trigger();

-- Create a table for tracking validation events (audit log)
CREATE TABLE IF NOT EXISTS content_validation_log (
    id BIGSERIAL PRIMARY KEY,
    content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('page', 'code_example')),
    content_id BIGINT NOT NULL,
    url TEXT NOT NULL,
    old_risk_level VARCHAR(20),
    new_risk_level VARCHAR(20) NOT NULL,
    risk_reasons TEXT[] DEFAULT '{}',
    risk_confidence DECIMAL(3,2) DEFAULT 0.0,
    validation_method VARCHAR(50) DEFAULT 'automatic',
    validated_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for audit log queries
CREATE INDEX IF NOT EXISTS idx_validation_log_content ON content_validation_log(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_validation_log_url ON content_validation_log(url);
CREATE INDEX IF NOT EXISTS idx_validation_log_created_at ON content_validation_log(created_at);

-- Function to log validation changes
CREATE OR REPLACE FUNCTION log_validation_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Log validation changes for auditing
    IF TG_OP = 'UPDATE' AND OLD.risk_level IS DISTINCT FROM NEW.risk_level THEN
        INSERT INTO content_validation_log (
            content_type,
            content_id,
            url,
            old_risk_level,
            new_risk_level,
            risk_reasons,
            risk_confidence,
            validation_method
        ) VALUES (
            CASE TG_TABLE_NAME 
                WHEN 'crawled_pages' THEN 'page'
                WHEN 'code_examples' THEN 'code_example'
            END,
            NEW.id,
            NEW.url,
            OLD.risk_level,
            NEW.risk_level,
            NEW.risk_reasons,
            NEW.risk_confidence,
            'manual'  -- Assume manual changes for now
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create audit triggers
DROP TRIGGER IF EXISTS crawled_pages_audit_trigger ON crawled_pages;
CREATE TRIGGER crawled_pages_audit_trigger
    AFTER UPDATE ON crawled_pages
    FOR EACH ROW
    EXECUTE FUNCTION log_validation_change();

DROP TRIGGER IF EXISTS code_examples_audit_trigger ON code_examples;
CREATE TRIGGER code_examples_audit_trigger
    AFTER UPDATE ON code_examples
    FOR EACH ROW
    EXECUTE FUNCTION log_validation_change();

-- Create useful queries for content management

-- Query to find all suspicious content
CREATE OR REPLACE VIEW suspicious_content AS
SELECT 
    'page' as content_type,
    id,
    url,
    title,
    source,
    risk_level,
    risk_reasons,
    risk_confidence,
    created_at,
    validated_at
FROM crawled_pages
WHERE risk_level IN ('suspicious', 'blocked')
UNION ALL
SELECT 
    'code_example' as content_type,
    id,
    url,
    NULL as title,
    source_id as source,
    risk_level,
    risk_reasons,
    risk_confidence,
    created_at,
    validated_at
FROM code_examples
WHERE risk_level IN ('suspicious', 'blocked')
ORDER BY created_at DESC;

-- Query to find content needing validation
CREATE OR REPLACE VIEW content_needing_validation AS
SELECT 
    'page' as content_type,
    id,
    url,
    title,
    source,
    risk_level,
    created_at
FROM crawled_pages
WHERE validated_at IS NULL OR validated_at < (created_at + INTERVAL '1 day')
UNION ALL
SELECT 
    'code_example' as content_type,
    id,
    url,
    NULL as title,
    source_id as source,
    risk_level,
    created_at
FROM code_examples
WHERE validated_at IS NULL OR validated_at < (created_at + INTERVAL '1 day')
ORDER BY created_at ASC;

-- Grant necessary permissions (adjust role names as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON crawled_pages TO ay_rag_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON code_examples TO ay_rag_user;
-- GRANT SELECT, INSERT ON content_validation_log TO ay_rag_user;
-- GRANT SELECT ON content_risk_summary TO ay_rag_user;
-- GRANT SELECT ON suspicious_content TO ay_rag_user;
-- GRANT SELECT ON content_needing_validation TO ay_rag_user;

-- Add comments for documentation
COMMENT ON COLUMN crawled_pages.risk_level IS 'Content safety classification: safe, suspicious, or blocked';
COMMENT ON COLUMN crawled_pages.risk_reasons IS 'Array of reasons why content was flagged';
COMMENT ON COLUMN crawled_pages.risk_confidence IS 'Confidence score (0.0-1.0) for risk assessment';
COMMENT ON COLUMN crawled_pages.validated_at IS 'Timestamp when content was last validated';
COMMENT ON COLUMN crawled_pages.validation_version IS 'Version of validation algorithm used';

COMMENT ON TABLE content_validation_log IS 'Audit log for all content validation changes';
COMMENT ON VIEW content_risk_summary IS 'Summary statistics of content risk levels';
COMMENT ON VIEW suspicious_content IS 'All content flagged as suspicious or blocked';
COMMENT ON VIEW content_needing_validation IS 'Content that needs validation or re-validation';