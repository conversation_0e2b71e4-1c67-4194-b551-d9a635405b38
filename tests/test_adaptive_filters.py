"""
Test suite for adaptive false positive filtering system
"""

import pytest
from src.adaptive_false_positive_filters import (
    AdaptiveFalsePositiveFilterSystem,
    AdaptiveFilterResult,
    EnhancedFalsePositiveFilterSystem
)
from src.content_type_classifier import ContentType, FilterStrategy
from src.false_positive_filters import FilterReason


class TestAdaptiveFalsePositiveFilterSystem:
    """Test adaptive false positive filtering functionality"""
    
    def setup_method(self):
        self.adaptive_filter = AdaptiveFalsePositiveFilterSystem()
    
    def test_marketing_page_strict_filtering(self):
        """Test that marketing pages get strict filtering"""
        marketing_page_content = """
        # Premium Business Solutions
        
        Why choose our enterprise platform?
        - Industry-leading features
        - 24/7 customer support
        - Trusted by Fortune 500 companies
        
        [Contact Sales Today!](/contact-sales)
        [Get Free Trial](/trial)
        
        Special pricing for enterprise customers.
        """
        
        # Test with a simple code-like block that should be filtered on marketing pages
        questionable_code = """
        [Home](/home)
        [About](/about)
        [Services](/services)
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=questionable_code,
            context_before="Our navigation:",
            context_after="Choose your path.",
            url="https://example.com/pricing",
            page_content=marketing_page_content
        )
        
        assert result.classification.content_type == ContentType.MARKETING
        assert result.strategy_applied == FilterStrategy.STRICT
        assert not result.base_result.is_valid_code  # Should be filtered out
        assert FilterReason.NAVIGATION_LINK in result.base_result.filter_reasons
    
    def test_tutorial_page_lenient_filtering(self):
        """Test that tutorial pages get lenient filtering"""
        tutorial_page_content = """
        # React Tutorial: Building Your First Component
        
        In this step-by-step tutorial, you will learn how to create
        a React component. Follow along and practice as we build
        together.
        
        ## Step 1: Set up the project
        Let's start by creating a new React app...
        """
        
        # Test with educational code that should pass in tutorials
        educational_code = """
        // Step 1: Create component
        function Welcome() {
            return <h1>Hello, World!</h1>;
        }
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=educational_code,
            context_before="In this step, we create our first component:",
            context_after="Next, we'll add some styling...",
            url="https://learn.react.dev/tutorial/step-1",
            page_content=tutorial_page_content
        )
        
        assert result.classification.content_type == ContentType.TUTORIAL
        assert result.strategy_applied == FilterStrategy.LENIENT
        assert result.base_result.is_valid_code  # Should pass with lenient filtering
        assert result.adaptive_confidence > 0.3
    
    def test_documentation_balanced_filtering(self):
        """Test that documentation pages get balanced filtering"""
        docs_page_content = """
        # API Documentation
        
        This documentation covers the REST API endpoints
        and provides code examples for integration.
        
        ## Authentication
        All API requests require authentication.
        
        ## Rate Limiting
        API calls are rate limited to 1000 requests per hour.
        """
        
        # Test with API documentation code
        api_example = """
        GET /api/v1/users
        Authorization: Bearer YOUR_TOKEN
        Content-Type: application/json
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=api_example,
            context_before="Example API request:",
            context_after="This returns a list of users.",
            url="https://docs.example.com/api/users",
            page_content=docs_page_content
        )
        
        assert result.classification.content_type == ContentType.DOCUMENTATION
        assert result.strategy_applied == FilterStrategy.BALANCED
        # Should have moderate confidence
        assert 0.2 <= result.adaptive_confidence <= 0.8
    
    def test_github_repo_consistent_filtering(self):
        """Test that GitHub repos get consistent filtering"""
        github_page_content = """
        # My Awesome Library
        
        A JavaScript library for data visualization.
        
        ## Installation
        ```bash
        npm install awesome-lib
        ```
        
        ## Contributing
        Pull requests welcome!
        
        ## License
        MIT
        """
        
        # Test with installation command
        install_code = """
        npm install awesome-lib
        cd awesome-lib
        npm start
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=install_code,
            context_before="To install and run:",
            context_after="The development server will start.",
            url="https://github.com/user/awesome-lib",
            page_content=github_page_content
        )
        
        assert result.classification.content_type == ContentType.GITHUB_REPO
        assert result.strategy_applied == FilterStrategy.CONSISTENT
        # GitHub repos should apply consistent rules
        assert result.base_result.confidence_score > 0
    
    def test_api_reference_lenient_filtering(self):
        """Test that API reference pages get lenient filtering"""
        api_docs_content = """
        # Users API Endpoints
        
        ## GET /users/{id}
        
        Retrieve user information by ID.
        
        ### Parameters
        - id: User identifier
        
        ### Response
        Returns user object with profile data.
        """
        
        # Test with API endpoint definition
        endpoint_code = """
        GET /api/users/123
        Host: api.example.com
        Authorization: Bearer token
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=endpoint_code,
            context_before="Example request:",
            context_after="Response will include user data.",
            url="https://api-docs.example.com/users",
            page_content=api_docs_content
        )
        
        assert result.classification.content_type == ContentType.API_REFERENCE
        assert result.strategy_applied == FilterStrategy.LENIENT
        # API reference should be lenient with examples
        assert result.adaptive_confidence > 0.2
    
    def test_batch_filtering_with_content_classification(self):
        """Test batch filtering with consistent content classification"""
        tutorial_content = """
        # Python Tutorial: Working with Lists
        
        Learn how to manipulate lists in Python with these examples.
        """
        
        code_blocks = [
            {
                'code': '''
def create_list():
    return [1, 2, 3, 4, 5]

numbers = create_list()
print(numbers)
                ''',
                'context_before': 'Here is how to create a list:',
                'context_after': 'This will output the numbers.'
            },
            {
                'code': '''
[Home](/home)
[About](/about)
                ''',
                'context_before': 'Navigation links:',
                'context_after': 'Choose your section.'
            },
            {
                'code': '''
# Add item to list
numbers.append(6)
print(f"Updated list: {numbers}")
                ''',
                'context_before': 'To add an item:',
                'context_after': 'The list now has 6 items.'
            }
        ]
        
        filtered_blocks = self.adaptive_filter.batch_filter_adaptive(
            code_blocks,
            url="https://pythontutorial.com/lists",
            page_content=tutorial_content
        )
        
        # Should keep valid Python code but filter navigation
        assert len(filtered_blocks) == 2
        
        # Check that valid blocks have adaptive metadata
        for block in filtered_blocks:
            assert 'adaptive_classification' in block
            assert block['adaptive_classification']['content_type'] == 'tutorial'
            assert block['adaptive_classification']['strategy'] == 'lenient'
            assert 'filter_confidence' in block
            assert 'syntax_indicators' in block
    
    def test_review_recommendation_logic(self):
        """Test manual review recommendation logic"""
        # Low confidence content should be recommended for review
        ambiguous_content = """
        Some text that might be code
        or might just be regular content
        with some = signs and () parentheses
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=ambiguous_content,
            page_content="Ambiguous page content without clear type indicators"
        )
        
        # Low confidence should trigger review recommendation
        if result.classification.confidence_score < 0.3:
            assert result.review_recommended
        
        # High confidence tutorial content should not need review
        clear_tutorial_code = """
        # Step 3: Create the main function
        def main():
            print("Hello from our tutorial!")
            return True
        
        if __name__ == "__main__":
            main()
        """
        
        clear_result = self.adaptive_filter.filter_code_block_adaptive(
            content=clear_tutorial_code,
            context_before="In this step of the tutorial:",
            context_after="Run this code to see the output.",
            page_content="Step-by-step Python tutorial for beginners"
        )
        
        if clear_result.classification.confidence_score > 0.6:
            assert not clear_result.review_recommended
    
    def test_adaptive_confidence_calculation(self):
        """Test adaptive confidence score calculation"""
        # High quality code in appropriate context should have high adaptive confidence
        quality_code = """
        class DataProcessor:
            def __init__(self, data_source):
                self.data_source = data_source
                self.processed_data = []
            
            def process(self):
                for item in self.data_source:
                    processed = self.transform(item)
                    self.processed_data.append(processed)
                return self.processed_data
            
            def transform(self, item):
                return item.upper().strip()
        """
        
        result = self.adaptive_filter.filter_code_block_adaptive(
            content=quality_code,
            context_before="Here's the complete DataProcessor class:",
            context_after="This class handles data transformation efficiently.",
            page_content="Python documentation for data processing utilities"
        )
        
        # Should have high adaptive confidence
        assert result.adaptive_confidence > 0.5
        assert result.base_result.is_valid_code
        
        # Navigation links should have low adaptive confidence
        nav_content = """
        [Home](/)
        [Products](/products)
        [Contact](/contact)
        """
        
        nav_result = self.adaptive_filter.filter_code_block_adaptive(
            content=nav_content,
            page_content="Welcome to our website with amazing products and services!"
        )
        
        # Should have low adaptive confidence
        assert nav_result.adaptive_confidence < 0.5
        assert not nav_result.base_result.is_valid_code
    
    def test_statistics_tracking(self):
        """Test that statistics are properly tracked"""
        initial_total = self.adaptive_filter.filter_stats['total_processed']
        
        # Process a few different types of content
        test_cases = [
            ("valid_code", "def test(): return True", True),
            ("navigation", "[Home](/home)", False),
            ("tutorial_code", "print('Hello, tutorial!')", True)
        ]
        
        for case_name, content, expected_valid in test_cases:
            result = self.adaptive_filter.filter_code_block_adaptive(
                content=content,
                page_content="Tutorial content for beginners"
            )
            
            # Verify result matches expectation (approximately)
            if expected_valid:
                # High-quality code should generally pass
                if 'def ' in content or 'print(' in content:
                    assert result.base_result.is_valid_code
            else:
                # Navigation should generally be filtered
                if '[' in content and '](' in content:
                    assert not result.base_result.is_valid_code
        
        # Check that statistics were updated
        final_total = self.adaptive_filter.filter_stats['total_processed']
        assert final_total == initial_total + len(test_cases)
        
        # Get comprehensive stats
        stats = self.adaptive_filter.get_adaptive_filter_stats()
        assert 'adaptive_filtering' in stats
        assert 'content_classification' in stats
        assert stats['adaptive_filtering']['total_processed'] >= len(test_cases)


class TestEnhancedFalsePositiveFilterSystem:
    """Test backward compatibility wrapper"""
    
    def setup_method(self):
        self.enhanced_filter = EnhancedFalsePositiveFilterSystem()
    
    def test_backward_compatible_filter_code_block(self):
        """Test backward compatible filter_code_block method"""
        code_content = """
        function calculateTotal(items) {
            return items.reduce((sum, item) => sum + item.price, 0);
        }
        """
        
        result = self.enhanced_filter.filter_code_block(
            content=code_content,
            context_before="Here's our calculation function:",
            context_after="This function sums all item prices."
        )
        
        # Should return standard FilterResult
        assert hasattr(result, 'is_valid_code')
        assert hasattr(result, 'confidence_score')
        assert hasattr(result, 'filter_reasons')
        assert hasattr(result, 'syntax_indicators')
        
        # Should correctly identify valid code
        assert result.is_valid_code
        assert result.confidence_score > 0.3
    
    def test_backward_compatible_batch_filter(self):
        """Test backward compatible batch_filter method"""
        code_blocks = [
            {
                'code': 'def hello(): print("Hello")',
                'context_before': 'Simple function:',
                'context_after': 'This prints a greeting.'
            },
            {
                'code': '[Home](/home)\n[About](/about)',
                'context_before': 'Navigation:',
                'context_after': 'Site sections.'
            }
        ]
        
        filtered_blocks = self.enhanced_filter.batch_filter(code_blocks)
        
        # Should filter out navigation but keep valid function
        assert len(filtered_blocks) == 1
        assert 'def hello()' in filtered_blocks[0]['code']
        
        # Should have adaptive metadata added
        assert 'adaptive_classification' in filtered_blocks[0]


class TestAdaptiveFilterIntegration:
    """Integration tests for adaptive filtering system"""
    
    def setup_method(self):
        self.adaptive_filter = AdaptiveFalsePositiveFilterSystem()
    
    def test_end_to_end_adaptive_filtering(self):
        """Test complete workflow from content classification to filtering"""
        test_scenarios = [
            {
                'name': 'marketing_strict',
                'page_content': """
                # Buy Now - Limited Time Offer!
                Get 50% off our premium service.
                [Sign Up Today!](/signup)
                Contact our sales team for enterprise pricing.
                """,
                'url': 'https://example.com/pricing',
                'code_blocks': [
                    {'code': '[Home](/)\n[Pricing](/pricing)', 'should_pass': False},
                    {'code': 'def process_payment(): pass', 'should_pass': True}
                ]
            },
            {
                'name': 'tutorial_lenient',
                'page_content': """
                # JavaScript Tutorial - Step by Step
                Learn JavaScript fundamentals with practical examples.
                Follow along and practice each concept.
                """,
                'url': 'https://tutorial.js.org/basics',
                'code_blocks': [
                    {'code': 'console.log("Hello");', 'should_pass': True},
                    {'code': 'var x = 1;\nfunction test() {}', 'should_pass': True}
                ]
            },
            {
                'name': 'documentation_balanced',
                'page_content': """
                # API Reference Documentation
                Complete reference for our REST API endpoints.
                Includes examples and parameter descriptions.
                """,
                'url': 'https://docs.api.com/reference',
                'code_blocks': [
                    {'code': 'GET /api/users\nAuthorization: Bearer token', 'should_pass': True},
                    {'code': '[Overview](/overview)\n[Reference](/ref)', 'should_pass': False}
                ]
            }
        ]
        
        for scenario in test_scenarios:
            for i, block in enumerate(scenario['code_blocks']):
                result = self.adaptive_filter.filter_code_block_adaptive(
                    content=block['code'],
                    context_before=f"Example {i+1}:",
                    context_after="See the result above.",
                    url=scenario['url'],
                    page_content=scenario['page_content']
                )
                
                # Verify filtering outcome matches expectation
                expected_pass = block['should_pass']
                actual_pass = result.base_result.is_valid_code
                
                if expected_pass != actual_pass:
                    print(f"Mismatch in {scenario['name']}: expected {expected_pass}, got {actual_pass}")
                    print(f"Content: {block['code'][:50]}...")
                    print(f"Classification: {result.classification.content_type}")
                    print(f"Strategy: {result.strategy_applied}")
                    print(f"Confidence: {result.adaptive_confidence}")
                
                # For strict scenarios, be more rigid about expectations
                if 'strict' in scenario['name']:
                    assert actual_pass == expected_pass, f"Strict filtering failed for {block['code'][:30]}..."
    
    def test_performance_monitoring_integration(self):
        """Test that performance monitoring works with adaptive filtering"""
        # Process multiple operations to generate performance data
        operations = [
            ("Marketing page with CTA", "https://shop.com/buy", "[Buy Now!](/buy)"),
            ("Tutorial code example", "https://learn.dev/python", "def hello(): print('hi')"),
            ("API documentation", "https://docs.api.com/users", "GET /users/{id}"),
            ("GitHub repository", "https://github.com/user/repo", "npm install package"),
            ("Blog post code", "https://blog.com/post", "console.log('example');")
        ]
        
        for desc, url, code in operations:
            self.adaptive_filter.filter_code_block_adaptive(
                content=code,
                context_before=f"Example from {desc}:",
                context_after="End of example.",
                url=url,
                page_content=f"Page content for {desc}"
            )
        
        # Get performance statistics
        stats = self.adaptive_filter.get_adaptive_filter_stats()
        
        # Verify comprehensive statistics are available
        assert stats['adaptive_filtering']['total_processed'] == len(operations)
        assert 'strategy_performance' in stats['adaptive_filtering']
        assert 'strategy_effectiveness' in stats['adaptive_filtering']
        
        # Check that different strategies were applied
        strategy_counts = sum(
            stats['adaptive_filtering']['strategy_performance'][strategy]['total']
            for strategy in stats['adaptive_filtering']['strategy_performance']
        )
        assert strategy_counts == len(operations)
        
        # Verify content classification metrics are included
        assert 'content_classification' in stats
        if stats['content_classification'].get('total_classifications', 0) > 0:
            assert 'content_type_distribution' in stats['content_classification']


if __name__ == "__main__":
    pytest.main([__file__, "-v"])