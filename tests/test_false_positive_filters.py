"""
Comprehensive test suite for false positive filtering system
"""

import pytest
from src.false_positive_filters import (
    NavigationLinkFilter,
    ContentStructureFilter,
    CodeSyntaxValidator,
    FalsePositiveFilterSystem,
    FilterReason
)


class TestNavigationLinkFilter:
    """Test navigation link detection and filtering"""
    
    def setup_method(self):
        self.filter = NavigationLinkFilter()
    
    def test_markdown_navigation_links(self):
        """Test detection of markdown navigation links"""
        content = """
        [Home](/home)
        [About](/about)
        [Contact](/contact)
        [Blog](/blog)
        """
        is_nav, reasons = self.filter.is_navigation_content(content)
        assert is_nav
        assert "multiple_navigation_links" in reasons
    
    def test_url_patterns(self):
        """Test detection of URL patterns"""
        content = """
        https://example.com/api/users
        https://example.com/docs
        /static/css/style.css
        ../components/header.js
        """
        is_nav, reasons = self.filter.is_navigation_content(content)
        assert is_nav
        assert "multiple_urls" in reasons
    
    def test_path_like_content(self):
        """Test detection of path-like content"""
        content = """
        src/components/Header.jsx
        src/utils/api.js
        public/images/logo.png
        docs/README.md
        """
        is_nav, reasons = self.filter.is_navigation_content(content)
        assert is_nav
        assert "path_like_content" in reasons
    
    def test_navigation_list(self):
        """Test detection of simple navigation lists"""
        content = """
        Home
        About
        Services
        Contact
        """
        is_nav, reasons = self.filter.is_navigation_content(content)
        assert is_nav
        assert "navigation_list" in reasons
    
    def test_valid_code_not_navigation(self):
        """Test that valid code is not flagged as navigation"""
        content = """
        def calculate_total(items):
            total = 0
            for item in items:
                total += item.price
            return total
        """
        is_nav, reasons = self.filter.is_navigation_content(content)
        assert not is_nav
        assert len(reasons) == 0


class TestContentStructureFilter:
    """Test content structure filtering"""
    
    def setup_method(self):
        self.filter = ContentStructureFilter()
    
    def test_bullet_point_detection(self):
        """Test detection of bullet point lists"""
        content = """
        - First item
        - Second item
        - Third item
        - Fourth item
        """
        is_bullets, reasons = self.filter.is_bullet_point_list(content)
        assert is_bullets
        assert "high_bullet_ratio" in reasons
        assert "no_code_in_bullets" in reasons
    
    def test_numbered_list_detection(self):
        """Test detection of numbered lists"""
        content = """
        1. First step
        2. Second step
        3. Third step
        4. Fourth step
        """
        is_bullets, reasons = self.filter.is_bullet_point_list(content)
        assert is_bullets
        assert "high_bullet_ratio" in reasons
    
    def test_mixed_list_with_code(self):
        """Test that lists with code syntax are not filtered"""
        content = """
        - def function_one():
        - class MyClass():
        - import numpy as np
        - for i in range(10):
        """
        is_bullets, reasons = self.filter.is_bullet_point_list(content)
        # Should detect bullets but allow due to code syntax
        assert "high_bullet_ratio" in reasons
        # Should not have "no_code_in_bullets" reason
        assert "no_code_in_bullets" not in reasons
    
    def test_markup_detection(self):
        """Test detection of pure markup content"""
        content = """
        # Main Header
        ## Sub Header
        **Bold text** and *italic text*
        [Link text](http://example.com)
        `inline code`
        """
        is_markup, reasons = self.filter.is_pure_markup(content)
        assert is_markup
        assert "high_markup_ratio" in reasons
    
    def test_header_heavy_content(self):
        """Test detection of header-heavy content"""
        content = """
        # Header 1
        ## Header 2
        ### Header 3
        #### Header 4
        ##### Header 5
        ###### Header 6
        """
        is_markup, reasons = self.filter.is_pure_markup(content)
        assert is_markup
        assert "mostly_headers" in reasons
    
    def test_valid_code_with_comments(self):
        """Test that code with comments is not flagged as markup"""
        content = """
        # Calculate fibonacci numbers
        def fibonacci(n):
            if n <= 1:
                return n
            return fibonacci(n-1) + fibonacci(n-2)
        """
        is_markup, reasons = self.filter.is_pure_markup(content)
        assert not is_markup or "mostly_headers" not in reasons


class TestCodeSyntaxValidator:
    """Test code syntax validation"""
    
    def setup_method(self):
        self.validator = CodeSyntaxValidator()
    
    def test_python_code_validation(self):
        """Test validation of Python code"""
        content = """
        def calculate_area(radius):
            import math
            return math.pi * radius ** 2
        
        class Circle:
            def __init__(self, radius):
                self.radius = radius
        """
        is_valid, confidence, indicators = self.validator.validate_code_syntax(content)
        assert is_valid
        assert confidence >= 0.3
        assert "python_keywords" in indicators
        assert "function_definition" in indicators
        assert "class_definition" in indicators
    
    def test_javascript_code_validation(self):
        """Test validation of JavaScript code"""
        content = """
        function calculateTotal(items) {
            let total = 0;
            for (const item of items) {
                total += item.price;
            }
            return total;
        }
        
        const myClass = class {
            constructor(name) {
                this.name = name;
            }
        };
        """
        is_valid, confidence, indicators = self.validator.validate_code_syntax(content)
        assert is_valid
        assert confidence >= 0.3
        assert "javascript_keywords" in indicators
        assert "function_definition" in indicators
    
    def test_java_code_validation(self):
        """Test validation of Java code"""
        content = """
        public class Calculator {
            private int value;
            
            public Calculator(int initialValue) {
                this.value = initialValue;
            }
            
            public int add(int number) {
                return this.value + number;
            }
        }
        """
        is_valid, confidence, indicators = self.validator.validate_code_syntax(content)
        assert is_valid
        assert confidence >= 0.3
        assert "java_keywords" in indicators
        assert "class_definition" in indicators
    
    def test_csharp_code_validation(self):
        """Test validation of C# code"""
        content = """
        using System;
        
        namespace MyApplication
        {
            public class Program
            {
                public static void Main(string[] args)
                {
                    Console.WriteLine("Hello World!");
                }
            }
        }
        """
        is_valid, confidence, indicators = self.validator.validate_code_syntax(content)
        assert is_valid
        assert confidence >= 0.3
        assert "csharp_keywords" in indicators
    
    def test_invalid_text_validation(self):
        """Test that non-code text is correctly rejected"""
        content = """
        This is just regular text content.
        It contains no programming syntax.
        Just normal sentences with some words.
        Nothing that looks like code here.
        """
        is_valid, confidence, indicators = self.validator.validate_code_syntax(content)
        assert not is_valid
        assert confidence < 0.3
        assert len(indicators) < 2
    
    def test_minimal_code_syntax(self):
        """Test edge case with minimal code syntax"""
        content = """
        function test() {
            return true;
        }
        """
        is_valid, confidence, indicators = self.validator.validate_code_syntax(content)
        assert is_valid
        assert "function_definition" in indicators
        assert "function_call" in indicators or "code_block" in indicators
    
    def test_complexity_calculation(self):
        """Test complexity scoring"""
        simple_content = "hello world"
        complex_content = """
        if (condition && other_condition) {
            array[index] = object.property;
            result = func(param1, param2);
        }
        """
        
        simple_complexity = self.validator._calculate_complexity(simple_content)
        complex_complexity = self.validator._calculate_complexity(complex_content)
        
        assert complex_complexity > simple_complexity
        assert complex_complexity > 0.3


class TestFalsePositiveFilterSystem:
    """Test the integrated filter system"""
    
    def setup_method(self):
        self.filter_system = FalsePositiveFilterSystem()
    
    def test_valid_python_code(self):
        """Test that valid Python code passes all filters"""
        content = """
        def fibonacci(n):
            if n <= 1:
                return n
            else:
                return fibonacci(n-1) + fibonacci(n-2)
        
        # Test the function
        for i in range(10):
            print(f"fib({i}) = {fibonacci(i)}")
        """
        result = self.filter_system.filter_code_block(content)
        assert result.is_valid_code
        assert result.confidence_score >= 0.3
        assert len(result.filter_reasons) == 0
    
    def test_navigation_links_filtered(self):
        """Test that navigation links are filtered out"""
        content = """
        [Home](/home)
        [About](/about)
        [Services](/services)
        [Contact](/contact)
        """
        result = self.filter_system.filter_code_block(content)
        assert not result.is_valid_code
        assert FilterReason.NAVIGATION_LINK in result.filter_reasons
    
    def test_bullet_points_filtered(self):
        """Test that bullet point lists are filtered out"""
        content = """
        - First item in the list
        - Second item in the list
        - Third item in the list
        - Fourth item in the list
        """
        result = self.filter_system.filter_code_block(content)
        assert not result.is_valid_code
        assert FilterReason.BULLET_POINT_LIST in result.filter_reasons
    
    def test_pure_markup_filtered(self):
        """Test that pure markup is filtered out"""
        content = """
        # Main Title
        ## Subtitle
        **Bold text** and *italic text*
        ### Another header
        """
        result = self.filter_system.filter_code_block(content)
        assert not result.is_valid_code
        assert FilterReason.PURE_MARKUP in result.filter_reasons
    
    def test_insufficient_complexity_filtered(self):
        """Test that content with insufficient complexity is filtered"""
        content = """
        hello world programming
        this is just text content
        no actual code syntax here
        """
        result = self.filter_system.filter_code_block(content)
        assert not result.is_valid_code
        assert (FilterReason.INSUFFICIENT_COMPLEXITY in result.filter_reasons or 
                FilterReason.NO_CODE_SYNTAX in result.filter_reasons)
    
    def test_batch_filtering(self):
        """Test batch filtering of multiple code blocks"""
        code_blocks = [
            {
                'code': 'def test(): return True',
                'context_before': '',
                'context_after': ''
            },
            {
                'code': '[Home](/home)\n[About](/about)',
                'context_before': '',
                'context_after': ''
            },
            {
                'code': '- item 1\n- item 2\n- item 3',
                'context_before': '',
                'context_after': ''
            },
            {
                'code': '''
                class Calculator:
                    def add(self, a, b):
                        return a + b
                ''',
                'context_before': '',
                'context_after': ''
            }
        ]
        
        filtered_blocks = self.filter_system.batch_filter(code_blocks)
        
        # Should keep valid code blocks (first and last)
        assert len(filtered_blocks) == 2
        assert 'def test()' in filtered_blocks[0]['code']
        assert 'class Calculator' in filtered_blocks[1]['code']
        
        # Check that filtered blocks have metadata
        for block in code_blocks:
            if block.get('filtered', False):
                assert 'filter_reasons' in block
                assert 'filter_confidence' in block
    
    def test_filter_statistics(self):
        """Test filter statistics generation"""
        code_blocks = [
            {'code': 'def test(): pass', 'filter_confidence': 0.8},
            {'code': '[Home](/)', 'filtered': True, 'filter_reasons': ['navigation_link'], 'filter_confidence': 0.1},
            {'code': '- item', 'filtered': True, 'filter_reasons': ['bullet_point_list'], 'filter_confidence': 0.2},
        ]
        
        stats = self.filter_system.get_filter_stats(code_blocks)
        
        assert stats['total_blocks'] == 3
        assert stats['valid_blocks'] == 1
        assert stats['filtered_blocks'] == 2
        assert stats['filter_rate'] == 2/3
        assert 'navigation_link' in stats['filter_reasons']
        assert 'bullet_point_list' in stats['filter_reasons']
        assert stats['avg_confidence'] == 0.8
    
    def test_edge_cases(self):
        """Test various edge cases"""
        # Empty content
        result = self.filter_system.filter_code_block("")
        assert not result.is_valid_code
        
        # Very short content
        result = self.filter_system.filter_code_block("x")
        assert not result.is_valid_code
        
        # Mixed content with URLs in code context
        content = """
        function test() {
            const apiUrl = "https://api.example.com";
            window.location = apiUrl + "/redirect";
            return fetch(apiUrl);
        }
        """
        result = self.filter_system.filter_code_block(content)
        # Should pass because it has strong programming syntax despite URLs
        assert result.is_valid_code
    
    def test_confidence_threshold_adjustment(self):
        """Test behavior with different confidence thresholds"""
        content = """
        somewhat ambiguous content
        might be code or might not
        has some = signs and ()
        """
        
        # Strict threshold
        strict_filter = FalsePositiveFilterSystem(confidence_threshold=0.5)
        strict_result = strict_filter.filter_code_block(content)
        
        # Lenient threshold
        lenient_filter = FalsePositiveFilterSystem(confidence_threshold=0.1)
        lenient_result = lenient_filter.filter_code_block(content)
        
        # Strict should be more likely to reject
        assert lenient_result.confidence_score >= strict_result.confidence_score


if __name__ == "__main__":
    pytest.main([__file__, "-v"])