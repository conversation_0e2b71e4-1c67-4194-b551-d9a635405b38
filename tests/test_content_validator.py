"""
Tests for the content validation module

This module tests the content safety validation functionality to ensure
suspicious and malicious content is properly detected and flagged.
"""
import pytest
from src.content_validator import (
    ContentRisk,
    RiskReason,
    ContentSafetyValidator,
    validate_url_content,
    is_content_safe
)


class TestContentSafetyValidator:
    """Test the content safety validator"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = ContentSafetyValidator()
    
    def test_safe_content(self):
        """Test that safe content is properly identified"""
        safe_content = """
        <h1>About Our Company</h1>
        <p>We are a technology company focused on innovation.</p>
        <p>Our mission is to create great software.</p>
        <a href="/contact">Contact Us</a>
        """
        
        result = self.validator.validate_content(safe_content, "https://example.com/about")
        
        assert result.risk_level == ContentRisk.SAFE
        assert result.safe_to_store is True
        assert len(result.reasons) == 0
        assert result.confidence < 0.4
    
    def test_password_form_detection(self):
        """Test detection of password forms"""
        password_form_content = """
        <form action="/login" method="post">
            <input type="text" name="username" placeholder="Username">
            <input type="password" name="password" placeholder="Password">
            <button type="submit">Login</button>
        </form>
        """
        
        result = self.validator.validate_content(password_form_content, "https://example.com/login")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.PASSWORD_FORM in result.reasons
        assert result.confidence > 0.4
    
    def test_payment_form_detection(self):
        """Test detection of payment forms"""
        payment_content = """
        <h2>Order Pizza Online</h2>
        <form action="/checkout" method="post">
            <input type="text" name="credit_card" placeholder="Credit Card Number">
            <input type="text" name="cvv" placeholder="CVV">
            <input type="text" name="amount" value="$25.99">
            <button type="submit">Complete Payment</button>
        </form>
        """
        
        result = self.validator.validate_content(payment_content, "https://pizza.com/order")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.PAYMENT_FORM in result.reasons
        assert "Credit card information" in str(result.details)
    
    def test_personal_data_collection(self):
        """Test detection of personal data collection"""
        pii_content = """
        <form>
            <input type="text" name="ssn" placeholder="Social Security Number">
            <input type="date" name="date_of_birth">
            <input type="text" name="drivers_license">
        </form>
        """
        
        result = self.validator.validate_content(pii_content, "https://forms.com/personal")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.PERSONAL_DATA_FORM in result.reasons
    
    def test_phishing_indicators(self):
        """Test detection of phishing content"""
        phishing_content = """
        <h1>URGENT: Your Account Has Been Suspended</h1>
        <p>Your account will be terminated unless you verify your identity immediately.</p>
        <p>Click here now to confirm your account information.</p>
        <a href="http://fake-bank.com/verify">Verify Account</a>
        """
        
        result = self.validator.validate_content(phishing_content, "https://suspicious.com/alert")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert any(reason in [RiskReason.ACCOUNT_VERIFICATION, RiskReason.URGENT_ACTION] 
                  for reason in result.reasons)
    
    def test_script_injection_detection(self):
        """Test detection of script injection"""
        script_content = """
        <p>Welcome to our site</p>
        <script>
            // Malicious script
            eval(atob('bWFsaWNpb3VzX2NvZGU='));
        </script>
        <div onclick="maliciousFunction()">Click me</div>
        """
        
        result = self.validator.validate_content(script_content, "https://malicious.com/page")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.SCRIPT_INJECTION in result.reasons
    
    def test_excessive_links(self):
        """Test detection of excessive links (potential spam)"""
        link_spam = """
        <p>Check out these great deals:</p>
        """ + "\n".join([f'<a href="http://spam{i}.com">Link {i}</a>' for i in range(25)])
        
        result = self.validator.validate_content(link_spam, "https://spam.com/links")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.EXCESSIVE_LINKS in result.reasons
        assert "25 links" in str(result.details)
    
    def test_suspicious_links(self):
        """Test detection of suspicious URL patterns"""
        suspicious_content = """
        <p>Download from: <a href="http://***********/malware.exe">here</a></p>
        <p>Or try: <a href="http://bit.ly/suspicious">shortened link</a></p>
        <p>Visit: <a href="http://evil.tk/phishing">this site</a></p>
        """
        
        result = self.validator.validate_content(suspicious_content, "https://bad.com/downloads")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.SUSPICIOUS_LINKS in result.reasons
    
    def test_spam_content_detection(self):
        """Test detection of spam-like content"""
        spam_content = """
        <h1>BUY NOW - Limited Time Offer!</h1>
        <p>Make easy cash working from home! Free money guaranteed!</p>
        <p>Click here now for this amazing deal!</p>
        """
        
        result = self.validator.validate_content(spam_content, "https://spam.com/offer")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.SPAM_CONTENT in result.reasons
    
    def test_multiple_risk_factors(self):
        """Test content with multiple risk factors"""
        high_risk_content = """
        <h1>URGENT: Verify Your Payment Information</h1>
        <form action="/steal-data" method="post">
            <input type="password" name="password">
            <input type="text" name="credit_card">
            <input type="text" name="ssn">
        </form>
        <script>stealUserData();</script>
        <p>Act now or your account will be suspended!</p>
        """
        
        result = self.validator.validate_content(high_risk_content, "https://phishing.com/urgent")
        
        assert result.risk_level == ContentRisk.BLOCKED
        assert len(result.reasons) >= 3  # Multiple risk factors
        assert result.confidence > 0.8
        assert result.safe_to_store is False
    
    def test_borderline_suspicious_content(self):
        """Test content that should be marked suspicious but not blocked"""
        suspicious_content = """
        <h2>User Registration</h2>
        <form>
            <input type="text" name="username">
            <input type="email" name="email">
            <input type="password" name="password">
        </form>
        <p>By registering, you agree to share your data with third parties.</p>
        """
        
        result = self.validator.validate_content(suspicious_content, "https://site.com/register")
        
        # Should be suspicious due to password form + data sharing
        assert result.risk_level == ContentRisk.SUSPICIOUS
        assert result.safe_to_store is True  # Can store but flag for review
        assert 0.4 <= result.confidence < 0.8


class TestValidationResults:
    """Test validation result interpretation"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = ContentSafetyValidator()
    
    def test_safe_for_indexing(self):
        """Test that safe content is marked for indexing"""
        safe_content = "<h1>Welcome</h1><p>This is safe content.</p>"
        result = self.validator.validate_content(safe_content)
        
        assert self.validator.is_safe_for_indexing(result) is True
    
    def test_unsafe_for_indexing(self):
        """Test that dangerous content is not marked for indexing"""
        dangerous_content = """
        <form><input type="password"></form>
        <script>maliciousCode();</script>
        """
        result = self.validator.validate_content(dangerous_content)
        
        assert self.validator.is_safe_for_indexing(result) is False
    
    def test_blocked_content_not_safe_to_store(self):
        """Test that blocked content is not safe to store"""
        blocked_content = """
        <script>eval('malicious code');</script>
        <form><input type="password"><input name="credit_card"></form>
        <h1>URGENT: Verify account now!</h1>
        """
        result = self.validator.validate_content(blocked_content)
        
        assert result.risk_level == ContentRisk.BLOCKED
        assert result.safe_to_store is False


class TestConvenienceFunctions:
    """Test convenience functions"""
    
    def test_validate_url_content(self):
        """Test the convenience function for URL content validation"""
        content = "<h1>Safe Page</h1>"
        url = "https://example.com/safe"
        title = "Safe Page Title"
        
        result = validate_url_content(content, url, title)
        
        assert isinstance(result, type(self.validator.validate_content(content)))
        assert result.risk_level == ContentRisk.SAFE
    
    def test_is_content_safe_function(self):
        """Test the quick safety check function"""
        safe_content = "<h1>Safe content</h1>"
        unsafe_content = "<script>evil();</script><form><input type='password'></form>"
        
        assert is_content_safe(safe_content) is True
        assert is_content_safe(unsafe_content) is False
    
    def test_pizza_form_example(self):
        """Test the specific pizza form example mentioned in requirements"""
        pizza_form = """
        <h1>Order Pizza Online</h1>
        <form action="/order" method="post">
            <select name="size">
                <option>Small</option>
                <option>Large</option>
            </select>
            <input type="text" name="delivery_address" placeholder="Address">
            <input type="text" name="credit_card" placeholder="Card Number">
            <button type="submit">Place Order</button>
        </form>
        """
        
        result = validate_url_content(pizza_form, "https://pizza.com/order")
        
        # Should be flagged due to payment form patterns
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert any("payment" in detail.lower() or "credit card" in detail.lower() 
                  for detail in result.details)


if __name__ == "__main__":
    pytest.main([__file__])