#!/usr/bin/env python3
"""
Test script to validate the refactored architecture.

This ensures that:
1. Docker tools are properly imported and registered
2. Tool names are correct
3. No references to deleted async files remain
"""
import sys
import importlib.util

def test_imports():
    """Test that all imports work correctly."""
    print("Testing imports...")
    
    # Test that deleted files don't exist
    deleted_files = [
        "src/async_mcp_tools.py",
        "src/async_job_manager.py"
    ]
    
    for file_path in deleted_files:
        spec = importlib.util.find_spec(file_path.replace('/', '.').replace('.py', ''))
        if spec is not None:
            print(f"❌ ERROR: {file_path} still exists but should be deleted!")
            return False
        else:
            print(f"✅ Confirmed {file_path} is deleted")
    
    # Test that docker tools can be imported
    try:
        from src.docker_mcp_tools import (
            crawl_with_timeout,
            get_crawl_status,
            cancel_crawl,
            list_crawl_tasks,
            stream_crawl_progress
        )
        print("✅ Docker tools imported successfully")
        
        # Verify function names
        expected_tools = [
            "crawl_with_timeout",
            "get_crawl_status", 
            "cancel_crawl",
            "list_crawl_tasks",
            "stream_crawl_progress"
        ]
        
        for tool_name in expected_tools:
            if tool_name in locals():
                print(f"✅ Tool '{tool_name}' exists")
            else:
                print(f"❌ ERROR: Tool '{tool_name}' not found!")
                return False
                
    except ImportError as e:
        print(f"❌ ERROR importing docker tools: {e}")
        return False
    
    # Test main server imports
    try:
        from src.ay_rag_mcp import mcp
        print("✅ Main server imported successfully")
    except ImportError as e:
        print(f"❌ ERROR importing main server: {e}")
        return False
    
    return True

def test_no_async_references():
    """Check that no ASYNC_MODE_ENABLED references remain in key files."""
    print("\nChecking for removed async references...")
    
    files_to_check = [
        "src/ay_rag_mcp.py",
        "docker-compose.yml"
    ]
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                if "ASYNC_MODE_ENABLED" in content:
                    print(f"❌ ERROR: {file_path} still contains ASYNC_MODE_ENABLED!")
                    return False
                else:
                    print(f"✅ {file_path} has no async mode references")
        except FileNotFoundError:
            print(f"⚠️  Warning: {file_path} not found")
    
    return True

def main():
    """Run all tests."""
    print("🔍 Validating Refactored Architecture\n")
    
    all_passed = True
    
    if not test_imports():
        all_passed = False
    
    if not test_no_async_references():
        all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("✅ All tests passed! Architecture refactoring successful.")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()