"""
Unit tests for MCP Client Integration Layer
"""
import pytest
import asyncio
import json
import time
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
from aiohttp import ClientResponseError, ClientError

try:
    from dashboard.mcp_client import (
        MCPClientWrapper,
        CircuitBreaker,
        CircuitBreakerState,
        MCPConnectionState,
        CrawlOptions,
        SearchFilters,
        CrawlResult,
        SearchResult,
        HealthStatus,
        transform_crawl_result_for_ui,
        transform_search_result_for_ui,
        transform_health_status_for_ui
    )
    DASHBOARD_AVAILABLE = True
except ImportError:
    # Dashboard module not available - create stubs for testing
    MCPClientWrapper = None
    CircuitBreaker = None
    CircuitBreakerState = None
    MCPConnectionState = None
    CrawlOptions = None
    SearchFilters = None
    CrawlResult = None
    SearchResult = None
    HealthStatus = None
    transform_crawl_result_for_ui = None
    transform_search_result_for_ui = None
    transform_health_status_for_ui = None
    DASHBOARD_AVAILABLE = False


@pytest.mark.skipif(not DASHBOARD_AVAILABLE, reason="Dashboard module not available")
class TestCircuitBreaker:
    """Test circuit breaker functionality"""
    
    def test_initial_state(self):
        """Test circuit breaker initial state"""
        cb = CircuitBreaker()
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0
        assert cb.can_execute() is True
    
    def test_failure_threshold(self):
        """Test circuit breaker opens after failure threshold"""
        cb = CircuitBreaker(failure_threshold=3)
        
        # Record failures up to threshold
        for i in range(2):
            cb.record_failure()
            assert cb.state == CircuitBreakerState.CLOSED
            assert cb.can_execute() is True
        
        # Third failure should open circuit
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.can_execute() is False
    
    def test_recovery_timeout(self):
        """Test circuit breaker recovery after timeout"""
        cb = CircuitBreaker(failure_threshold=1, recovery_timeout=1)
        
        # Trigger circuit open
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.can_execute() is False
        
        # Wait for recovery timeout
        time.sleep(1.1)
        
        # Should allow execution (half-open state)
        assert cb.can_execute() is True
        assert cb.state == CircuitBreakerState.HALF_OPEN
    
    def test_success_resets_circuit(self):
        """Test successful operation resets circuit breaker"""
        cb = CircuitBreaker(failure_threshold=1)
        
        # Open circuit
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        
        # Allow recovery attempt
        time.sleep(cb.recovery_timeout + 1)
        cb.can_execute()  # Moves to half-open
        
        # Record success should close circuit
        cb.record_success()
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0


class TestMCPClientWrapper:
    """Test MCP client wrapper functionality"""
    
    @pytest.fixture
    def client(self):
        """Create MCP client for testing"""
        return MCPClientWrapper(
            server_url="http://test-server:8051",
            timeout=5,
            retry_attempts=2
        )
    
    @pytest.fixture
    def mock_session(self):
        """Create mock aiohttp session"""
        session = AsyncMock()
        session.closed = False
        return session
    
    @pytest.mark.asyncio
    async def test_context_manager(self, client):
        """Test async context manager functionality"""
        with patch.object(client, '_ensure_session') as mock_ensure:
            with patch.object(client, 'close') as mock_close:
                async with client:
                    mock_ensure.assert_called_once()
                mock_close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, client):
        """Test successful health check"""
        mock_response = {
            "status": "healthy",
            "timestamp": **********.0,
            "initialization_complete": True,
            "components": {"crawl4ai": "available", "supabase": "available"}
        }
        
        with patch.object(client, '_make_request', return_value=mock_response):
            result = await client.health_check()
            
            assert isinstance(result, HealthStatus)
            assert result.status == "healthy"
            assert result.initialization_complete is True
            assert "crawl4ai" in result.components
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, client):
        """Test health check failure handling"""
        with patch.object(client, '_make_request', side_effect=ConnectionError("Connection failed")):
            result = await client.health_check()
            
            assert isinstance(result, HealthStatus)
            assert result.status == "error"
            assert result.initialization_complete is False
    
    @pytest.mark.asyncio
    async def test_crawl_url_success(self, client):
        """Test successful crawl operation"""
        mock_response = {
            "content": [{
                "text": json.dumps({
                    "success": True,
                    "url": "https://example.com",
                    "chunks_stored": 5,
                    "code_examples_stored": 2,
                    "content_length": 1000,
                    "total_word_count": 200,
                    "source_id": "example.com"
                })
            }]
        }
        
        with patch.object(client, '_make_request', return_value=mock_response):
            options = CrawlOptions(max_depth=2, max_concurrent=5)
            result = await client.crawl_url("https://example.com", options)
            
            assert isinstance(result, CrawlResult)
            assert result.success is True
            assert result.url == "https://example.com"
            assert result.chunks_stored == 5
            assert result.code_examples_stored == 2
    
    @pytest.mark.asyncio
    async def test_crawl_url_failure(self, client):
        """Test crawl operation failure handling"""
        with patch.object(client, '_make_request', side_effect=ConnectionError("MCP server unavailable")):
            result = await client.crawl_url("https://example.com")
            
            assert isinstance(result, CrawlResult)
            assert result.success is False
            assert result.error == "MCP server unavailable"
    
    @pytest.mark.asyncio
    async def test_search_documents_success(self, client):
        """Test successful search operation"""
        mock_response = {
            "content": [{
                "text": json.dumps({
                    "results": [
                        {"content": "Test content", "score": 0.9},
                        {"content": "Another result", "score": 0.8}
                    ]
                })
            }]
        }
        
        with patch.object(client, '_make_request', return_value=mock_response):
            filters = SearchFilters(max_results=10)
            result = await client.search_documents("test query", filters)
            
            assert isinstance(result, SearchResult)
            assert result.success is True
            assert result.query == "test query"
            assert len(result.results) == 2
            assert result.total_results == 2
    
    @pytest.mark.asyncio
    async def test_search_documents_failure(self, client):
        """Test search operation failure handling"""
        with patch.object(client, '_make_request', side_effect=ConnectionError("Search failed")):
            result = await client.search_documents("test query")
            
            assert isinstance(result, SearchResult)
            assert result.success is False
            assert result.error == "Search failed"
    
    def test_circuit_breaker_state_check(self, client):
        """Test circuit breaker state checking"""
        # Initially closed
        assert client.circuit_breaker.can_execute() is True
        
        # Open circuit breaker and set recent failure time
        client.circuit_breaker.state = CircuitBreakerState.OPEN
        client.circuit_breaker.last_failure_time = time.time()  # Recent failure
        assert client.circuit_breaker.can_execute() is False
    
    @pytest.mark.asyncio
    async def test_connection_info(self, client):
        """Test connection info retrieval"""
        info = client.get_connection_info()
        
        assert info["server_url"] == "http://test-server:8051"
        assert info["timeout"] == 5
        assert info["retry_attempts"] == 2
        assert "connection_state" in info
        assert "circuit_breaker_state" in info


class TestDataTransformation:
    """Test data transformation utilities"""
    
    def test_transform_crawl_result_for_ui(self):
        """Test crawl result transformation for UI"""
        result = CrawlResult(
            success=True,
            url="https://example.com",
            chunks_stored=5,
            code_examples_stored=2,
            content_length=1000,
            total_word_count=200,
            source_id="example.com",
            links_count={"internal": 10, "external": 5}
        )
        
        ui_data = transform_crawl_result_for_ui(result)
        
        assert ui_data["success"] is True
        assert ui_data["url"] == "https://example.com"
        assert ui_data["summary"]["chunks_stored"] == 5
        assert ui_data["status_class"] == "success"
        assert ui_data["status_text"] == "Completed successfully"
    
    def test_transform_crawl_result_for_ui_failure(self):
        """Test crawl result transformation for UI with failure"""
        result = CrawlResult(
            success=False,
            url="https://example.com",
            error="Connection timeout"
        )
        
        ui_data = transform_crawl_result_for_ui(result)
        
        assert ui_data["success"] is False
        assert ui_data["error"] == "Connection timeout"
        assert ui_data["status_class"] == "error"
        assert ui_data["status_text"] == "Failed"
    
    def test_transform_search_result_for_ui(self):
        """Test search result transformation for UI"""
        result = SearchResult(
            success=True,
            query="test query",
            results=[{"content": "result 1"}, {"content": "result 2"}],
            total_results=2,
            execution_time=0.123
        )
        
        ui_data = transform_search_result_for_ui(result)
        
        assert ui_data["success"] is True
        assert ui_data["query"] == "test query"
        assert len(ui_data["results"]) == 2
        assert ui_data["execution_time"] == 0.123
        assert ui_data["has_results"] is True
        assert ui_data["status_class"] == "success"
    
    def test_transform_health_status_for_ui(self):
        """Test health status transformation for UI"""
        status = HealthStatus(
            status="healthy",
            timestamp=**********.0,
            initialization_complete=True,
            components={"crawl4ai": "available", "supabase": "running"}
        )
        
        ui_data = transform_health_status_for_ui(status)
        
        assert ui_data["status"] == "healthy"
        assert ui_data["status_class"] == "success"
        assert ui_data["initialization_complete"] is True
        assert len(ui_data["components"]) == 2
        
        # Check component transformation
        crawl4ai_component = next(c for c in ui_data["components"] if c["name"] == "Crawl4Ai")
        assert crawl4ai_component["status"] == "available"
        assert crawl4ai_component["status_class"] == "success"


@pytest.mark.asyncio
async def test_global_client_management():
    """Test global MCP client management functions"""
    from dashboard.mcp_client import get_mcp_client, close_mcp_client, _mcp_client
    
    # Initially no client
    assert _mcp_client is None
    
    # Get client creates instance
    client1 = await get_mcp_client()
    assert client1 is not None
    
    # Second call returns same instance
    client2 = await get_mcp_client()
    assert client1 is client2
    
    # Close client
    with patch.object(client1, 'close') as mock_close:
        await close_mcp_client()
        mock_close.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])