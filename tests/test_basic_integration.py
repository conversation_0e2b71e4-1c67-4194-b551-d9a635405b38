#!/usr/bin/env python3
"""
Basic integration test for Crawl4AI async functionality.
Tests core components without external dependencies.
"""
import os
import sys
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock

# Add src directory to path for imports
sys.path.insert(0, '/home/<USER>/dev/tools/mcp_servers/mcp-crawl4ai-rag/src')

# Set test environment
os.environ["ASYNC_MODE_ENABLED"] = "true"
os.environ["CRAWL4AI_DOCKER_HOST"] = "http://localhost:11235"

print("🔬 Starting Basic Crawl4AI Async Integration Tests")

async def test_async_job_manager():
    """Test AsyncJobManager basic functionality."""
    print("\n📋 Testing AsyncJobManager...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager, JobStatus
        
        # Test 1: Manager creation
        manager = AsyncCrawlJobManager()
        print("✅ Job manager created successfully")
        
        # Test 2: Payload preparation
        payload = manager._prepare_payload(["https://example.com"])
        assert "urls" in payload
        assert "browser_config" in payload
        assert "crawler_config" in payload
        assert payload["urls"] == ["https://example.com"]
        print("✅ Payload preparation works correctly")
        
        # Test 3: Manager cleanup
        await manager.close()
        print("✅ Job manager cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ AsyncJobManager test failed: {e}")
        return False

async def test_mcp_tools_import():
    """Test that MCP tools can be imported and are properly configured."""
    print("\n🛠️  Testing MCP Tools Import...")
    
    try:
        from async_mcp_tools import (
            submit_smart_crawl_job,
            get_crawl_job_status,
            cancel_crawl_job,
            list_active_crawl_jobs,
            stream_smart_crawl
        )
        print("✅ All async MCP tools imported successfully")
        
        # Verify tools are async functions
        assert asyncio.iscoroutinefunction(submit_smart_crawl_job)
        assert asyncio.iscoroutinefunction(get_crawl_job_status)
        assert asyncio.iscoroutinefunction(cancel_crawl_job)
        assert asyncio.iscoroutinefunction(list_active_crawl_jobs)
        assert asyncio.iscoroutinefunction(stream_smart_crawl)
        print("✅ All tools are properly async functions")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP tools import test failed: {e}")
        return False

async def test_mcp_tools_disabled_mode():
    """Test MCP tools behavior when async mode is disabled."""
    print("\n🚫 Testing Disabled Mode...")
    
    try:
        # Temporarily disable async mode
        original_value = os.environ.get("ASYNC_MODE_ENABLED")
        os.environ["ASYNC_MODE_ENABLED"] = "false"
        
        from async_mcp_tools import submit_smart_crawl_job
        
        # Create mock context
        mock_context = MagicMock()
        mock_context.request_context.lifespan_context.supabase_client = MagicMock()
        
        # Test that it returns disabled error
        result = await submit_smart_crawl_job(mock_context, "https://example.com")
        result_data = json.loads(result)
        
        assert result_data["success"] is False
        assert "not enabled" in result_data["error"]
        print("✅ Correctly handles disabled async mode")
        
        # Restore original value
        if original_value is not None:
            os.environ["ASYNC_MODE_ENABLED"] = original_value
        
        return True
        
    except Exception as e:
        print(f"❌ Disabled mode test failed: {e}")
        return False

async def test_error_handling():
    """Test error handling mechanisms."""
    print("\n🚨 Testing Error Handling...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager
        
        manager = AsyncCrawlJobManager(docker_host="http://invalid-host:9999")
        
        # Test health check with invalid host
        is_healthy = await manager.health_check()
        assert is_healthy is False
        print("✅ Health check correctly identifies unhealthy service")
        
        await manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

async def test_main_integration():
    """Test integration with main MCP server module."""
    print("\n🔗 Testing Main Integration...")
    
    try:
        # Test that async mode flag properly controls imports
        os.environ["ASYNC_MODE_ENABLED"] = "true"
        
        # This should not raise an error since async mode is enabled
        from ay_rag_mcp import mcp
        print("✅ Main MCP server integration successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Main integration test failed: {e}")
        return False

async def test_docker_service_check():
    """Test if Docker service is available (non-blocking)."""
    print("\n🐳 Testing Docker Service Availability...")
    
    try:
        import httpx
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            try:
                response = await client.get("http://localhost:11235/health")
                if response.status_code == 200:
                    print("✅ Crawl4AI Docker service is available")
                    return True
                else:
                    print(f"⚠️  Crawl4AI service responded with status {response.status_code}")
                    return False
            except httpx.ConnectError:
                print("⚠️  Crawl4AI Docker service not available (expected if not started)")
                return False
                
    except Exception as e:
        print(f"⚠️  Docker service check failed: {e}")
        return False

async def run_all_tests():
    """Run all integration tests."""
    print("🚀 Running Crawl4AI Async Integration Tests\n")
    
    tests = [
        ("AsyncJobManager", test_async_job_manager),
        ("MCP Tools Import", test_mcp_tools_import),
        ("Disabled Mode", test_mcp_tools_disabled_mode),
        ("Error Handling", test_error_handling),
        ("Main Integration", test_main_integration),
        ("Docker Service", test_docker_service_check),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"💥 {total - passed} test(s) failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)