"""
Test suite for RAG configuration system.
"""
import pytest
import os
from unittest.mock import patch
from src.rag_config import (
    RAGConfiguration,
    RAGStrategy,
    ScoringMode,
    get_basic_config,
    get_advanced_config,
    get_performance_config,
    get_comprehensive_config,
    get_rag_config,
    reload_config
)

class TestRAGConfiguration:
    """Test RAG configuration functionality."""
    
    def test_default_configuration(self):
        """Test default configuration values."""
        config = RAGConfiguration()
        
        # Test strategy defaults
        assert config.use_contextual_embeddings is False
        assert config.use_hybrid_search is True
        assert config.use_agentic_rag is False
        assert config.use_reranking is True
        assert config.use_knowledge_graph is False
        
        # Test scoring defaults
        assert config.scoring_mode == ScoringMode.NORMALIZED
        assert config.vector_weight == 0.5
        assert config.keyword_weight == 0.3
        assert config.rerank_weight == 0.2
    
    def test_from_environment_basic(self):
        """Test configuration from environment variables."""
        env_vars = {
            'USE_CONTEXTUAL_EMBEDDINGS': 'true',
            'USE_HYBRID_SEARCH': 'false',
            'USE_RERANKING': 'true',
            'VECTOR_WEIGHT': '0.6',
            'KEYWORD_WEIGHT': '0.4',
            'SCORING_MODE': 'adaptive'
        }
        
        with patch.dict(os.environ, env_vars):
            config = RAGConfiguration.from_environment()
            
            assert config.use_contextual_embeddings is True
            assert config.use_hybrid_search is False
            assert config.use_reranking is True
            assert config.vector_weight == 0.6
            assert config.keyword_weight == 0.4
            assert config.scoring_mode == ScoringMode.ADAPTIVE
    
    def test_from_environment_invalid_values(self):
        """Test handling of invalid environment values."""
        env_vars = {
            'VECTOR_WEIGHT': 'invalid_float',
            'MAX_CONCURRENT_SEARCHES': 'not_an_int',
            'SCORING_MODE': 'invalid_mode'
        }
        
        with patch.dict(os.environ, env_vars):
            config = RAGConfiguration.from_environment()
            
            # Should use defaults for invalid values
            assert config.vector_weight == 0.5  # default
            assert config.max_concurrent_searches == 3  # default
            assert config.scoring_mode == ScoringMode.NORMALIZED  # default
    
    def test_get_enabled_strategies(self):
        """Test strategy enumeration."""
        config = RAGConfiguration(
            use_contextual_embeddings=True,
            use_hybrid_search=True,
            use_agentic_rag=False,
            use_reranking=True,
            use_knowledge_graph=False
        )
        
        strategies = config.get_enabled_strategies()
        expected = [
            RAGStrategy.CONTEXTUAL_EMBEDDINGS,
            RAGStrategy.HYBRID_SEARCH,
            RAGStrategy.RERANKING
        ]
        
        assert set(strategies) == set(expected)
    
    def test_get_enabled_strategies_basic_fallback(self):
        """Test fallback to basic strategy when none enabled."""
        config = RAGConfiguration(
            use_contextual_embeddings=False,
            use_hybrid_search=False,
            use_agentic_rag=False,
            use_reranking=False,
            use_knowledge_graph=False
        )
        
        strategies = config.get_enabled_strategies()
        assert RAGStrategy.BASIC in strategies
    
    def test_validate_configuration_warnings(self):
        """Test configuration validation warnings."""
        config = RAGConfiguration(
            vector_weight=0.6,
            keyword_weight=0.3,
            rerank_weight=0.3,  # Sum > 1.0
            max_concurrent_searches=15,  # High value
            embedding_batch_size=150,  # Large batch
            min_code_length=1000,
            max_code_length=500  # Invalid range
        )
        
        warnings = config.validate_configuration()
        
        assert len(warnings) > 0
        assert any("weights sum" in w for w in warnings)
        assert any("concurrent searches" in w for w in warnings)
        assert any("batch size" in w for w in warnings)
        assert any("min_code_length" in w for w in warnings)
    
    def test_validate_configuration_dependencies(self):
        """Test dependency validation warnings."""
        config = RAGConfiguration(
            use_reranking=True,
            use_hybrid_search=False,  # Missing dependency
            use_semantic_caching=True,
            enable_embedding_cache=False  # Missing dependency
        )
        
        warnings = config.validate_configuration()
        
        assert any("reranking" in w and "hybrid search" in w for w in warnings)
        assert any("semantic caching" in w and "embedding cache" in w for w in warnings)
    
    def test_to_dict_serialization(self):
        """Test configuration serialization."""
        config = RAGConfiguration(
            use_hybrid_search=True,
            scoring_mode=ScoringMode.ADAPTIVE
        )
        
        result = config.to_dict()
        
        assert isinstance(result, dict)
        assert result['use_hybrid_search'] is True
        assert result['scoring_mode'] == 'adaptive'  # Enum converted to value
    
    def test_str_representation(self):
        """Test string representation."""
        config = RAGConfiguration(
            use_contextual_embeddings=True,
            use_hybrid_search=True,
            use_enhanced_chunking=True
        )
        
        str_repr = str(config)
        
        assert "RAG Configuration:" in str_repr
        assert "contextual_embeddings" in str_repr
        assert "hybrid_search" in str_repr
        assert "chunking" in str_repr

class TestPredefinedConfigurations:
    """Test predefined configuration presets."""
    
    def test_basic_config(self):
        """Test basic configuration preset."""
        config = get_basic_config()
        
        assert config.use_contextual_embeddings is False
        assert config.use_hybrid_search is True
        assert config.use_agentic_rag is False
        assert config.use_reranking is False
        assert config.scoring_mode == ScoringMode.SIMPLE
    
    def test_advanced_config(self):
        """Test advanced configuration preset."""
        config = get_advanced_config()
        
        assert config.use_contextual_embeddings is True
        assert config.use_hybrid_search is True
        assert config.use_agentic_rag is True
        assert config.use_reranking is True
        assert config.use_enhanced_chunking is True
        assert config.scoring_mode == ScoringMode.ADAPTIVE
    
    def test_performance_config(self):
        """Test performance configuration preset."""
        config = get_performance_config()
        
        assert config.use_contextual_embeddings is False  # Fast
        assert config.use_hybrid_search is True
        assert config.use_reranking is True
        assert config.use_semantic_caching is True
        assert config.enable_parallel_processing is True
        assert config.max_concurrent_searches == 5
    
    def test_comprehensive_config(self):
        """Test comprehensive configuration preset."""
        config = get_comprehensive_config()
        
        # Should have all features enabled
        assert config.use_contextual_embeddings is True
        assert config.use_hybrid_search is True
        assert config.use_agentic_rag is True
        assert config.use_reranking is True
        assert config.use_knowledge_graph is True
        assert config.use_enhanced_chunking is True
        assert config.scoring_mode == ScoringMode.ADAPTIVE

class TestGlobalConfiguration:
    """Test global configuration management."""
    
    def teardown_method(self):
        """Clean up global config after each test."""
        # Reset global config
        import src.rag_config as rag_config_module
        rag_config_module._config = None
    
    def test_get_rag_config_singleton(self):
        """Test global configuration singleton behavior."""
        config1 = get_rag_config()
        config2 = get_rag_config()
        
        # Should be the same instance
        assert config1 is config2
    
    def test_reload_config(self):
        """Test configuration reloading."""
        # Get initial config
        config1 = get_rag_config()
        
        # Reload should create new instance
        with patch.dict(os.environ, {'USE_HYBRID_SEARCH': 'false'}):
            config2 = reload_config()
        
        assert config1 is not config2
        assert config2.use_hybrid_search is False
    
    @patch('src.rag_config.logger')
    def test_config_validation_logging(self, mock_logger):
        """Test that configuration warnings are logged."""
        env_vars = {
            'VECTOR_WEIGHT': '0.8',
            'KEYWORD_WEIGHT': '0.3',
            'RERANK_WEIGHT': '0.2'  # Sum > 1.0
        }
        
        with patch.dict(os.environ, env_vars):
            reload_config()
        
        # Should log warnings
        mock_logger.warning.assert_called()

class TestEnvironmentVariableHandling:
    """Test environment variable parsing."""
    
    def test_boolean_parsing(self):
        """Test boolean environment variable parsing."""
        true_values = ['true', '1', 'yes', 'on', 'True', 'TRUE']
        false_values = ['false', '0', 'no', 'off', 'False', 'FALSE', '', 'invalid']
        
        for true_val in true_values:
            with patch.dict(os.environ, {'USE_HYBRID_SEARCH': true_val}):
                config = RAGConfiguration.from_environment()
                assert config.use_hybrid_search is True
        
        for false_val in false_values:
            with patch.dict(os.environ, {'USE_HYBRID_SEARCH': false_val}):
                config = RAGConfiguration.from_environment()
                assert config.use_hybrid_search is False
    
    def test_numeric_parsing_fallback(self):
        """Test numeric parsing with invalid values."""
        env_vars = {
            'VECTOR_WEIGHT': 'not_a_number',
            'MAX_CONCURRENT_SEARCHES': 'invalid_int',
            'CACHE_TTL_SECONDS': '3.14'  # Float instead of int
        }
        
        with patch.dict(os.environ, env_vars):
            config = RAGConfiguration.from_environment()
            
            # Should fall back to defaults
            assert config.vector_weight == 0.5
            assert config.max_concurrent_searches == 3
            assert config.cache_ttl_seconds == 3  # Should convert float to int
    
    def test_comprehensive_environment_coverage(self):
        """Test that all environment variables are properly handled."""
        env_vars = {
            # Core strategies
            'USE_CONTEXTUAL_EMBEDDINGS': 'true',
            'USE_HYBRID_SEARCH': 'true',
            'USE_AGENTIC_RAG': 'true',
            'USE_RERANKING': 'true',
            'USE_KNOWLEDGE_GRAPH': 'false',
            
            # Enhanced features
            'USE_ENHANCED_CHUNKING': 'true',
            'USE_QUERY_EXPANSION': 'false',
            'USE_RESULT_DEDUPLICATION': 'true',
            'USE_SEMANTIC_CACHING': 'true',
            
            # Scoring
            'SCORING_MODE': 'adaptive',
            'VECTOR_WEIGHT': '0.6',
            'KEYWORD_WEIGHT': '0.25',
            'RERANK_WEIGHT': '0.15',
            'DUAL_MATCH_BOOST': '1.3',
            'RERANK_THRESHOLD': '-1.5',
            
            # Performance
            'MAX_CONCURRENT_SEARCHES': '5',
            'EMBEDDING_BATCH_SIZE': '75',
            'QUERY_TIMEOUT_SECONDS': '45',
            'ENABLE_PARALLEL_PROCESSING': 'true',
            
            # Code analysis
            'MIN_CODE_LENGTH': '100',
            'MAX_CODE_LENGTH': '5000',
            'CODE_EXTRACTION_TIMEOUT': '15',
            'ENABLE_CODE_SUMMARIZATION': 'false',
            
            # Quality filters
            'ENABLE_CONTENT_VALIDATION': 'false',
            'MINIMUM_RELEVANCE_SCORE': '0.2',
            'MAXIMUM_DUPLICATE_SIMILARITY': '0.9',
            
            # Caching
            'CACHE_TTL_SECONDS': '7200',
            'MAX_CACHE_SIZE': '2000',
            'ENABLE_QUERY_CACHE': 'false',
            'ENABLE_EMBEDDING_CACHE': 'true',
            
            # Monitoring
            'ENABLE_PERFORMANCE_METRICS': 'false',
            'ENABLE_QUERY_LOGGING': 'true',
            'LOG_SLOW_QUERIES_THRESHOLD': '3.0'
        }
        
        with patch.dict(os.environ, env_vars):
            config = RAGConfiguration.from_environment()
            
            # Verify all values are correctly parsed
            assert config.use_contextual_embeddings is True
            assert config.use_query_expansion is False
            assert config.use_result_deduplication is True
            assert config.scoring_mode == ScoringMode.ADAPTIVE
            assert config.vector_weight == 0.6
            assert config.max_concurrent_searches == 5
            assert config.min_code_length == 100
            assert config.enable_content_validation is False
            assert config.minimum_relevance_score == 0.2
            assert config.cache_ttl_seconds == 7200
            assert config.enable_query_cache is False
            assert config.enable_performance_metrics is False
            assert config.log_slow_queries_threshold == 3.0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])