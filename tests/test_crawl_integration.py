"""
Integration tests for crawling workflow
"""
import pytest
import async<PERSON>
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from datetime import datetime

try:
    from dashboard.main import app
    from dashboard.mcp_client import CrawlResult, CrawlOptions
    from dashboard.routes.htmx import active_crawls, crawl_history
    DASHBOARD_AVAILABLE = True
except ImportError:
    # Dashboard module not available - create stubs for testing
    app = None
    CrawlResult = None
    CrawlOptions = None
    active_crawls = None
    crawl_history = None
    DASHBOARD_AVAILABLE = False


@pytest.mark.skipif(not DASHBOARD_AVAILABLE, reason="Dashboard module not available")
@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
def mock_mcp_client():
    """Mock MCP client fixture"""
    mock_client = AsyncMock()
    mock_client.health_check.return_value = AsyncMock(status="healthy")
    return mock_client


@pytest.fixture(autouse=True)
def clear_crawl_data():
    """Clear crawl data before each test"""
    active_crawls.clear()
    crawl_history.clear()
    yield
    active_crawls.clear()
    crawl_history.clear()


class TestCrawlInterface:
    """Test crawl interface page"""
    
    @patch('dashboard.routes.crawl.get_mcp_client')
    def test_crawl_interface_server_available(self, mock_get_client, client, mock_mcp_client):
        """Test crawl interface when server is available"""
        mock_get_client.return_value.__aenter__.return_value = mock_mcp_client
        
        response = client.get("/crawl/")
        
        assert response.status_code == 200
        assert "Web Crawling" in response.text
        assert "Start Web Crawl" in response.text
        assert "MCP Server Unavailable" not in response.text
    
    @patch('dashboard.routes.crawl.get_mcp_client')
    def test_crawl_interface_server_unavailable(self, mock_get_client, client):
        """Test crawl interface when server is unavailable"""
        mock_get_client.return_value.__aenter__.side_effect = ConnectionError("Server unavailable")
        
        response = client.get("/crawl/")
        
        assert response.status_code == 200
        assert "Web Crawling" in response.text
        assert "MCP Server Unavailable" in response.text
    
    def test_crawl_interface_with_active_crawls(self, client):
        """Test crawl interface with active crawls"""
        # Add mock active crawl
        active_crawls["test-id"] = {
            "task_id": "test-id",
            "url": "https://example.com",
            "status": "crawling",
            "progress": 50,
            "start_time": datetime.now()
        }
        
        with patch('dashboard.routes.crawl.get_mcp_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.health_check.return_value = AsyncMock(status="healthy")
            mock_get_client.return_value.__aenter__.return_value = mock_client
            
            response = client.get("/crawl/")
            
            assert response.status_code == 200
            assert "Active Crawls" in response.text
            assert "https://example.com" in response.text
    
    def test_crawl_interface_with_history(self, client):
        """Test crawl interface with crawl history"""
        # Add mock history
        crawl_history.append({
            "task_id": "test-id",
            "url": "https://example.com",
            "success": True,
            "chunks_stored": 10,
            "timestamp": "2024-01-01 12:00:00",
            "duration": 5.5
        })
        
        with patch('dashboard.routes.crawl.get_mcp_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.health_check.return_value = AsyncMock(status="healthy")
            mock_get_client.return_value.__aenter__.return_value = mock_client
            
            response = client.get("/crawl/")
            
            assert response.status_code == 200
            assert "Recent Crawls" in response.text
            assert "https://example.com" in response.text


class TestCrawlHTMX:
    """Test HTMX crawl endpoints"""
    
    @patch('dashboard.routes.htmx.get_mcp_client')
    def test_crawl_start_success(self, mock_get_client, client, mock_mcp_client):
        """Test successful crawl start"""
        # Mock successful crawl result
        mock_result = CrawlResult(
            success=True,
            url="https://example.com",
            chunks_stored=10,
            code_examples_stored=2,
            content_length=5000,
            total_word_count=1000
        )
        mock_mcp_client.crawl_url.return_value = mock_result
        mock_get_client.return_value.__aenter__.return_value = mock_mcp_client
        
        response = client.post("/htmx/crawl/start", data={
            "url": "https://example.com",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000",
            "force_direct": "false"
        })
        
        assert response.status_code == 200
        assert "Crawling in Progress" in response.text
        assert "https://example.com" in response.text
        assert len(active_crawls) == 1
    
    def test_crawl_start_invalid_url(self, client):
        """Test crawl start with invalid URL"""
        response = client.post("/htmx/crawl/start", data={
            "url": "invalid-url",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert response.status_code == 200
        assert "Invalid URL format" in response.text
        assert len(active_crawls) == 0
    
    @patch('dashboard.routes.htmx.get_mcp_client')
    def test_crawl_start_server_error(self, mock_get_client, client):
        """Test crawl start with server error"""
        mock_get_client.return_value.__aenter__.side_effect = Exception("Server error")
        
        response = client.post("/htmx/crawl/start", data={
            "url": "https://example.com",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert response.status_code == 200
        assert "MCP server unavailable" in response.text
    
    def test_crawl_status_not_found(self, client):
        """Test crawl status for non-existent task"""
        response = client.get("/htmx/crawl/status/non-existent-id")
        
        assert response.status_code == 404
        assert "Crawl operation not found" in response.text
    
    def test_crawl_status_in_progress(self, client):
        """Test crawl status for in-progress task"""
        # Add mock active crawl
        task_id = "test-id"
        active_crawls[task_id] = {
            "task_id": task_id,
            "url": "https://example.com",
            "status": "crawling",
            "progress": 50,
            "start_time": datetime.now(),
            "end_time": None,
            "result": None
        }
        
        response = client.get(f"/htmx/crawl/status/{task_id}")
        
        assert response.status_code == 200
        assert "Crawling in Progress" in response.text
        assert "50%" in response.text
    
    def test_crawl_status_completed_success(self, client):
        """Test crawl status for completed successful task"""
        # Add mock completed crawl
        task_id = "test-id"
        mock_result = CrawlResult(
            success=True,
            url="https://example.com",
            chunks_stored=10,
            code_examples_stored=2,
            content_length=5000,
            total_word_count=1000
        )
        
        active_crawls[task_id] = {
            "task_id": task_id,
            "url": "https://example.com",
            "status": "completed",
            "progress": 100,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "result": mock_result
        }
        
        response = client.get(f"/htmx/crawl/status/{task_id}")
        
        assert response.status_code == 200
        assert "Crawl Complete" in response.text
        assert "Success" in response.text
        assert "10" in response.text  # chunks stored
    
    def test_crawl_status_completed_failure(self, client):
        """Test crawl status for completed failed task"""
        # Add mock failed crawl
        task_id = "test-id"
        mock_result = CrawlResult(
            success=False,
            url="https://example.com",
            error="Connection timeout"
        )
        
        active_crawls[task_id] = {
            "task_id": task_id,
            "url": "https://example.com",
            "status": "completed",
            "progress": 0,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "result": mock_result
        }
        
        response = client.get(f"/htmx/crawl/status/{task_id}")
        
        assert response.status_code == 200
        assert "Crawl Complete" in response.text
        assert "Failed" in response.text
        assert "Connection timeout" in response.text
    
    def test_crawl_history_empty(self, client):
        """Test crawl history when empty"""
        response = client.get("/htmx/crawl/history")
        
        assert response.status_code == 200
        assert "No crawl history" in response.text
    
    def test_crawl_history_with_items(self, client):
        """Test crawl history with items"""
        # Add mock history
        crawl_history.extend([
            {
                "task_id": "test-1",
                "url": "https://example1.com",
                "success": True,
                "chunks_stored": 10,
                "timestamp": "2024-01-01 12:00:00",
                "duration": 5.5
            },
            {
                "task_id": "test-2",
                "url": "https://example2.com",
                "success": False,
                "chunks_stored": 0,
                "timestamp": "2024-01-01 11:00:00",
                "duration": 2.1
            }
        ])
        
        response = client.get("/htmx/crawl/history")
        
        assert response.status_code == 200
        assert "https://example1.com" in response.text
        assert "https://example2.com" in response.text
        assert "Success" in response.text
        assert "Failed" in response.text


class TestCrawlWorkflow:
    """Test complete crawl workflow"""
    
    @patch('dashboard.routes.htmx.get_mcp_client')
    @pytest.mark.asyncio
    async def test_complete_crawl_workflow(self, mock_get_client, client, mock_mcp_client):
        """Test complete crawl workflow from start to finish"""
        # Mock successful crawl result
        mock_result = CrawlResult(
            success=True,
            url="https://example.com",
            chunks_stored=15,
            code_examples_stored=3,
            content_length=8000,
            total_word_count=1500,
            source_id="src_123",
            links_count={"internal": 5, "external": 2}
        )
        mock_mcp_client.crawl_url.return_value = mock_result
        mock_get_client.return_value.__aenter__.return_value = mock_mcp_client
        
        # 1. Start crawl
        start_response = client.post("/htmx/crawl/start", data={
            "url": "https://example.com",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert start_response.status_code == 200
        assert "Crawling in Progress" in start_response.text
        assert len(active_crawls) == 1
        
        # Get task ID
        task_id = list(active_crawls.keys())[0]
        
        # 2. Wait for crawl to complete (simulate background processing)
        await asyncio.sleep(0.1)  # Allow background task to start
        
        # 3. Check status - should show completion
        # Manually complete the crawl for testing
        active_crawls[task_id]["status"] = "completed"
        active_crawls[task_id]["progress"] = 100
        active_crawls[task_id]["end_time"] = datetime.now()
        active_crawls[task_id]["result"] = mock_result
        
        status_response = client.get(f"/htmx/crawl/status/{task_id}")
        
        assert status_response.status_code == 200
        assert "Crawl Complete" in status_response.text
        assert "Success" in status_response.text
        assert "15" in status_response.text  # chunks stored
        assert "3" in status_response.text   # code examples
        assert "src_123" in status_response.text  # source ID
    
    @patch('dashboard.routes.htmx.get_mcp_client')
    def test_crawl_options_validation(self, mock_get_client, client, mock_mcp_client):
        """Test crawl options validation"""
        mock_result = CrawlResult(success=True, url="https://example.com")
        mock_mcp_client.crawl_url.return_value = mock_result
        mock_get_client.return_value.__aenter__.return_value = mock_mcp_client
        
        # Test with custom options
        response = client.post("/htmx/crawl/start", data={
            "url": "https://example.com",
            "max_depth": "5",
            "max_concurrent": "10",
            "chunk_size": "2000",
            "force_direct": "true"
        })
        
        assert response.status_code == 200
        assert len(active_crawls) == 1
        
        # Verify options were passed correctly
        task_id = list(active_crawls.keys())[0]
        crawl_info = active_crawls[task_id]
        options = crawl_info["options"]
        
        assert options.max_depth == 5
        assert options.max_concurrent == 10
        assert options.chunk_size == 2000
        assert options.force_direct == True
    
    def test_concurrent_crawls(self, client):
        """Test handling multiple concurrent crawls"""
        with patch('dashboard.routes.htmx.get_mcp_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.crawl_url.return_value = CrawlResult(success=True, url="test")
            mock_get_client.return_value.__aenter__.return_value = mock_client
            
            # Start multiple crawls
            urls = ["https://example1.com", "https://example2.com", "https://example3.com"]
            
            for url in urls:
                response = client.post("/htmx/crawl/start", data={
                    "url": url,
                    "max_depth": "3",
                    "max_concurrent": "5",
                    "chunk_size": "1000"
                })
                assert response.status_code == 200
            
            # Should have 3 active crawls
            assert len(active_crawls) == 3
            
            # All should be in starting/crawling state
            for crawl_info in active_crawls.values():
                assert crawl_info["status"] in ["starting", "crawling"]


class TestCrawlErrorHandling:
    """Test error handling in crawl operations"""
    
    @patch('dashboard.routes.htmx.get_mcp_client')
    def test_mcp_client_connection_error(self, mock_get_client, client):
        """Test handling MCP client connection errors"""
        mock_get_client.return_value.__aenter__.side_effect = ConnectionError("Cannot connect to MCP server")
        
        response = client.post("/htmx/crawl/start", data={
            "url": "https://example.com",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert response.status_code == 200
        assert "MCP server unavailable" in response.text
        assert "Cannot connect to MCP server" in response.text
    
    @patch('dashboard.routes.htmx.get_mcp_client')
    def test_crawl_operation_failure(self, mock_get_client, client, mock_mcp_client):
        """Test handling crawl operation failures"""
        # Mock failed crawl result
        mock_result = CrawlResult(
            success=False,
            url="https://example.com",
            error="Website is blocking crawlers"
        )
        mock_mcp_client.crawl_url.return_value = mock_result
        mock_get_client.return_value.__aenter__.return_value = mock_mcp_client
        
        response = client.post("/htmx/crawl/start", data={
            "url": "https://example.com",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert response.status_code == 200
        assert "Crawling in Progress" in response.text
        
        # Get task ID and simulate completion
        task_id = list(active_crawls.keys())[0]
        active_crawls[task_id]["status"] = "completed"
        active_crawls[task_id]["result"] = mock_result
        active_crawls[task_id]["end_time"] = datetime.now()
        
        status_response = client.get(f"/htmx/crawl/status/{task_id}")
        
        assert status_response.status_code == 200
        assert "Crawl Complete" in status_response.text
        assert "Failed" in status_response.text
        assert "Website is blocking crawlers" in status_response.text
    
    def test_invalid_form_data(self, client):
        """Test handling invalid form data"""
        # Missing required URL
        response = client.post("/htmx/crawl/start", data={
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert response.status_code == 422  # Validation error
    
    def test_malformed_url(self, client):
        """Test handling malformed URLs"""
        response = client.post("/htmx/crawl/start", data={
            "url": "not-a-url",
            "max_depth": "3",
            "max_concurrent": "5",
            "chunk_size": "1000"
        })
        
        assert response.status_code == 200
        assert "Invalid URL format" in response.text


if __name__ == "__main__":
    pytest.main([__file__, "-v"])