#!/usr/bin/env python3
"""
Live integration test with actual Crawl4AI Docker API.
Tests real async job submission and status checking.
"""
import os
import sys
import asyncio
import json
import time
from unittest.mock import MagicMock

# Add src directory to path for imports
sys.path.insert(0, '/home/<USER>/dev/tools/mcp_servers/mcp-crawl4ai-rag/src')

# Set test environment
os.environ["ASYNC_MODE_ENABLED"] = "true"
os.environ["CRAWL4AI_DOCKER_HOST"] = "http://localhost:11235"

print("🔬 Starting Live Crawl4AI Integration Tests")

async def test_real_health_check():
    """Test real health check against running service."""
    print("\n💗 Testing Real Health Check...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager
        
        manager = AsyncCrawlJobManager()
        is_healthy = await manager.health_check()
        await manager.close()
        
        if is_healthy:
            print("✅ Crawl4AI service is healthy and responding")
            return True
        else:
            print("❌ Crawl4AI service health check failed")
            return False
            
    except Exception as e:
        print(f"❌ Health check test failed: {e}")
        return False

async def test_real_job_submission():
    """Test real job submission to Crawl4AI API."""
    print("\n📤 Testing Real Job Submission...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager, JobStatus
        
        manager = AsyncCrawlJobManager()
        
        # Submit a simple job
        job = await manager.submit_job(
            urls=["https://httpbin.org/html"],
            browser_config={"headless": True},
            crawler_config={"cache_mode": "bypass"}
        )
        
        print(f"✅ Job submitted successfully: {job.task_id}")
        print(f"   Status: {job.status}")
        print(f"   URLs: {job.urls}")
        
        # Wait a moment for processing
        await asyncio.sleep(2)
        
        # Check status
        updated_job = await manager.get_job_status(job.task_id)
        print(f"✅ Job status retrieved: {updated_job.status}")
        
        await manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Job submission test failed: {e}")
        return False

async def test_mcp_tools_with_mock():
    """Test MCP tools with mock context but real service."""
    print("\n🔧 Testing MCP Tools with Real Service...")
    
    try:
        from async_mcp_tools import submit_smart_crawl_job, get_crawl_job_status
        
        # Create mock context
        mock_context = MagicMock()
        mock_supabase = MagicMock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Test job submission
        result = await submit_smart_crawl_job(
            mock_context,
            "https://httpbin.org/html",
            max_depth=1,
            max_concurrent=2,
            chunk_size=1000
        )
        
        result_data = json.loads(result)
        print(f"✅ MCP job submission result: {result_data['success']}")
        
        if result_data["success"]:
            task_id = result_data["task_id"]
            print(f"   Task ID: {task_id}")
            
            # Wait a moment
            await asyncio.sleep(3)
            
            # Check status
            status_result = await get_crawl_job_status(mock_context, task_id)
            status_data = json.loads(status_result)
            print(f"✅ MCP status check: {status_data['status']}")
            
        return result_data["success"]
        
    except Exception as e:
        print(f"❌ MCP tools test failed: {e}")
        return False

async def test_streaming_api():
    """Test streaming API functionality."""
    print("\n🌊 Testing Streaming API...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager
        
        manager = AsyncCrawlJobManager()
        
        results = []
        async for result in manager.stream_crawl(
            urls=["https://httpbin.org/html"],
            browser_config={"headless": True},
            crawler_config={"cache_mode": "bypass", "stream": True}
        ):
            results.append(result)
            print(f"   Streamed result: {result.get('url', 'No URL')}")
            
            # Don't collect too many results in test
            if len(results) >= 3:
                break
        
        await manager.close()
        
        if results:
            print(f"✅ Streaming API works, received {len(results)} results")
            return True
        else:
            print("⚠️  No results from streaming API")
            return False
            
    except Exception as e:
        print(f"❌ Streaming API test failed: {e}")
        return False

async def test_error_scenarios():
    """Test various error scenarios."""
    print("\n🚨 Testing Error Scenarios...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager
        
        # Test invalid URL
        manager = AsyncCrawlJobManager()
        
        try:
            job = await manager.submit_job(["invalid-url-format"])
            print("⚠️  Invalid URL was accepted (may be handled by Crawl4AI)")
        except Exception:
            print("✅ Invalid URL properly rejected")
        
        # Test with empty URL list
        try:
            job = await manager.submit_job([])
            print("⚠️  Empty URL list was accepted")
        except Exception:
            print("✅ Empty URL list properly rejected")
        
        await manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Error scenarios test failed: {e}")
        return False

async def test_job_lifecycle():
    """Test complete job lifecycle."""
    print("\n🔄 Testing Complete Job Lifecycle...")
    
    try:
        from async_job_manager import AsyncCrawlJobManager, JobStatus
        
        manager = AsyncCrawlJobManager()
        
        # Submit job
        job = await manager.submit_job(["https://httpbin.org/html"])
        initial_time = time.time()
        
        print(f"   Job submitted: {job.task_id}")
        
        # Poll until completion or timeout
        max_wait = 30  # 30 seconds max
        while time.time() - initial_time < max_wait:
            updated_job = await manager.get_job_status(job.task_id)
            print(f"   Status: {updated_job.status.value}")
            
            if updated_job.status in [JobStatus.COMPLETED, JobStatus.FAILED]:
                if updated_job.status == JobStatus.COMPLETED:
                    print(f"✅ Job completed successfully in {time.time() - initial_time:.1f}s")
                    if updated_job.results:
                        print(f"   Results: {len(updated_job.results)} items")
                    return True
                else:
                    print(f"❌ Job failed: {updated_job.error}")
                    return False
            
            await asyncio.sleep(2)
        
        print("⚠️  Job did not complete within timeout")
        await manager.close()
        return False
        
    except Exception as e:
        print(f"❌ Job lifecycle test failed: {e}")
        return False

async def run_live_tests():
    """Run all live integration tests."""
    print("🚀 Running Live Crawl4AI Integration Tests\n")
    
    tests = [
        ("Real Health Check", test_real_health_check),
        ("Real Job Submission", test_real_job_submission),
        ("MCP Tools with Service", test_mcp_tools_with_mock),
        ("Streaming API", test_streaming_api),
        ("Error Scenarios", test_error_scenarios),
        ("Complete Job Lifecycle", test_job_lifecycle),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"Running {test_name}...")
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Live Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All live tests passed!")
        return True
    else:
        print(f"💥 {total - passed} test(s) failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_live_tests())
    sys.exit(0 if success else 1)