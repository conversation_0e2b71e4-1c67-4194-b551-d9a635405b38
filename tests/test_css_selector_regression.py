"""
Regression Test Suite for CSS Selector Integration

This test suite prevents regression of the critical CSS selector bug where
enhanced content filtering was returning lists that Crawl4AI expected as strings.

Critical Bug History:
- Issue: 'list' object has no attribute 'split' error in Crawl4AI async_crawler_strategy.py:965
- Cause: Enhanced content filtering returns css_selector_to_exclude as list, but Crawl4AI expects string
- Fix: Convert list to comma-separated string using ','.join()
- Impact: Complete crawling functionality failure → Full restoration
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, List, Any

# Add src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from enhanced_content_filter import (
    EnhancedContentFilter,
    FilteringLevel,
    create_enhanced_crawler_config
)


class TestCssSelectorRegression:
    """Regression tests for CSS selector integration bug"""
    
    def test_enhanced_crawler_config_returns_list(self):
        """Test that enhanced content filtering correctly returns CSS selectors as lists"""
        config = create_enhanced_crawler_config(
            url="https://docs.example.com",
            filtering_level=FilteringLevel.STANDARD
        )
        
        # Should return CSS selectors as a list
        css_selectors = config.get('css_selector_to_exclude', [])
        assert isinstance(css_selectors, list), "CSS selectors should be returned as list"
        assert len(css_selectors) > 0, "Should have some CSS selectors"
        assert 'nav' in css_selectors, "Should include common navigation selectors"
        
        # Verify list contains string elements
        for selector in css_selectors:
            assert isinstance(selector, str), f"All selectors should be strings, got {type(selector)}"
    
    def test_css_selector_string_conversion(self):
        """Test that list CSS selectors can be properly converted to comma-separated strings"""
        config = create_enhanced_crawler_config(
            url="https://docs.example.com",
            filtering_level=FilteringLevel.STANDARD
        )
        
        css_selectors = config.get('css_selector_to_exclude', [])
        
        # Test conversion to comma-separated string
        css_string = ','.join(css_selectors)
        
        # Verify the string format
        assert isinstance(css_string, str), "Converted CSS selectors should be string"
        assert ',' in css_string or len(css_selectors) == 1, "Should be comma-separated if multiple selectors"
        
        # Test that string can be split back to original list
        reconstructed_list = [s.strip() for s in css_string.split(',')]
        assert reconstructed_list == css_selectors, "Should be able to reconstruct original list"
    
    def test_empty_css_selector_handling(self):
        """Test handling of empty CSS selector lists"""
        # Test with empty list
        empty_list = []
        result = ','.join(empty_list)
        assert result == "", "Empty list should result in empty string"
        
        # Test that empty string splits correctly
        reconstructed = [s.strip() for s in result.split(',') if s.strip()]
        assert reconstructed == [], "Empty string should split to empty list"
    
    def test_single_css_selector_handling(self):
        """Test handling of single CSS selector in list"""
        single_selector = ['nav']
        result = ','.join(single_selector)
        assert result == "nav", "Single selector should not have commas"
        
        # Test splitting single selector
        reconstructed = [s.strip() for s in result.split(',')]
        assert reconstructed == single_selector, "Single selector should reconstruct correctly"
    
    def test_multiple_css_selectors_handling(self):
        """Test handling of multiple CSS selectors"""
        multiple_selectors = ['nav', 'header', 'footer', '.sidebar']
        result = ','.join(multiple_selectors)
        expected = "nav,header,footer,.sidebar"
        assert result == expected, f"Expected '{expected}', got '{result}'"
        
        # Test splitting multiple selectors
        reconstructed = [s.strip() for s in result.split(',')]
        assert reconstructed == multiple_selectors, "Multiple selectors should reconstruct correctly"
    
    def test_css_selectors_with_spaces(self):
        """Test CSS selectors that contain spaces"""
        selectors_with_spaces = ['.navigation menu', '[role="navigation"]', 'div.sidebar nav']
        result = ','.join(selectors_with_spaces)
        
        # Should preserve spaces within selectors
        reconstructed = [s.strip() for s in result.split(',')]
        assert reconstructed == selectors_with_spaces, "Spaces within selectors should be preserved"
    
    def test_all_filtering_levels_return_lists(self):
        """Test that all filtering levels return CSS selectors as lists"""
        url = "https://docs.example.com"
        
        for level in FilteringLevel:
            config = create_enhanced_crawler_config(url=url, filtering_level=level)
            css_selectors = config.get('css_selector_to_exclude', [])
            
            assert isinstance(css_selectors, list), f"Level {level} should return list"
            
            # Test conversion works for all levels
            css_string = ','.join(css_selectors)
            assert isinstance(css_string, str), f"Level {level} should convert to string"
    
    def test_different_content_types_css_selectors(self):
        """Test CSS selectors for different content types"""
        test_urls = [
            ("https://docs.example.com/api", "documentation"),
            ("https://learn.example.com/tutorial", "tutorial"),
            ("https://example.com/pricing", "marketing"),
            ("https://github.com/user/repo", "github")
        ]
        
        for url, content_type in test_urls:
            config = create_enhanced_crawler_config(url=url, filtering_level=FilteringLevel.STANDARD)
            css_selectors = config.get('css_selector_to_exclude', [])
            
            # All should return lists
            assert isinstance(css_selectors, list), f"{content_type} should return list"
            
            # All should convert to valid strings
            css_string = ','.join(css_selectors)
            assert isinstance(css_string, str), f"{content_type} should convert to string"
            
            # Should have content-type-specific selectors
            assert len(css_selectors) > 0, f"{content_type} should have some selectors"


class TestCrawlConfigIntegration:
    """Test integration with actual crawling configuration"""
    
    def test_crawler_run_config_mock_integration(self):
        """Test that CrawlerRunConfig would receive correct CSS selector format"""
        config = create_enhanced_crawler_config(
            url="https://docs.example.com",
            filtering_level=FilteringLevel.STANDARD
        )
        
        # Simulate what happens in the actual code
        css_selector_list = config.get('css_selector_to_exclude', [])
        css_selector_string = ','.join(css_selector_list)
        
        # Mock CrawlerRunConfig to verify it receives a string
        with patch('crawl4ai.CrawlerRunConfig') as mock_config:
            mock_config.return_value = Mock()
            
            # This is the pattern used in the fixed code
            run_config_kwargs = {
                'cache_mode': 'CacheMode.BYPASS',
                'stream': False,
                'word_count_threshold': config.get('word_count_threshold', 20),
                'excluded_tags': config.get('excluded_tags', []),
                'css_selector': css_selector_string  # This should be a string
            }
            
            # Verify css_selector is a string
            assert isinstance(run_config_kwargs['css_selector'], str), "css_selector must be string for Crawl4AI"
            
            # Verify excluded_tags remains a list (this is correct)
            assert isinstance(run_config_kwargs['excluded_tags'], list), "excluded_tags should remain list"
    
    def test_crawl4ai_css_selector_split_simulation(self):
        """Simulate the Crawl4AI split operation that was failing"""
        config = create_enhanced_crawler_config(
            url="https://docs.example.com",
            filtering_level=FilteringLevel.STANDARD
        )
        
        css_selector_list = config.get('css_selector_to_exclude', [])
        css_selector_string = ','.join(css_selector_list)
        
        # Simulate the Crawl4AI operation that was failing
        try:
            # This is the line that was failing: line 965 in async_crawler_strategy.py
            selectors = [s.strip() for s in css_selector_string.split(',')]
            
            # Should not raise an error now
            assert isinstance(selectors, list), "Should successfully split string to list"
            assert all(isinstance(s, str) for s in selectors), "All split items should be strings"
            
            # Should match original selectors (minus empty strings)
            original_selectors = [s for s in css_selector_list if s.strip()]
            clean_selectors = [s for s in selectors if s.strip()]
            assert clean_selectors == original_selectors, "Split result should match original"
            
        except AttributeError as e:
            pytest.fail(f"CSS selector split failed: {e}. This indicates the regression bug is present.")
    
    def test_edge_case_css_selectors(self):
        """Test edge cases that could cause the split operation to fail"""
        edge_cases = [
            [],  # Empty list
            [""],  # List with empty string
            ["nav"],  # Single selector
            ["nav", ""],  # Mixed with empty
            ["nav", "header", "footer"],  # Multiple selectors
            [".complex[attr='value']"],  # Complex selector
            ["nav, .menu"],  # Selector with internal comma (this could be problematic)
        ]
        
        for css_list in edge_cases:
            css_string = ','.join(css_list)
            
            # Should not fail the Crawl4AI split operation
            try:
                selectors = [s.strip() for s in css_string.split(',')]
                # Clean empty selectors (matching actual Crawl4AI behavior)
                clean_selectors = [s for s in selectors if s.strip()]
                
                # Should produce reasonable results
                assert isinstance(clean_selectors, list), f"Failed for input {css_list}"
                
            except Exception as e:
                pytest.fail(f"Edge case {css_list} failed: {e}")


class TestProductionScenarios:
    """Test real production scenarios that could trigger the bug"""
    
    def test_documentation_site_crawling_config(self):
        """Test configuration for documentation sites"""
        doc_urls = [
            "https://fastapi.tiangolo.com/tutorial/",
            "https://docs.python.org/3/tutorial/",
            "https://docs.npmjs.com/",
            "https://expressjs.com/en/guide/"
        ]
        
        for url in doc_urls:
            config = create_enhanced_crawler_config(url=url, filtering_level=FilteringLevel.STANDARD)
            
            # Extract CSS selectors and convert to string format
            css_selectors = config.get('css_selector_to_exclude', [])
            css_string = ','.join(css_selectors)
            
            # Verify this would work with Crawl4AI
            selectors = [s.strip() for s in css_string.split(',')]
            clean_selectors = [s for s in selectors if s.strip()]
            
            assert len(clean_selectors) > 0, f"Should have selectors for {url}"
            assert 'nav' in clean_selectors, f"Should include nav selector for {url}"
    
    @pytest.mark.asyncio
    async def test_async_crawling_mock_integration(self):
        """Test async crawling integration with proper CSS selector format"""
        # Mock the AsyncWebCrawler
        with patch('crawl4ai.AsyncWebCrawler') as mock_crawler_class:
            mock_crawler = AsyncMock()
            mock_crawler_class.return_value = mock_crawler
            
            # Mock successful crawl result
            mock_result = Mock()
            mock_result.markdown = "# Test Content\nSome content here"
            mock_result.success = True
            mock_crawler.arun = AsyncMock(return_value=mock_result)
            
            # This simulates the fixed crawl_single_page function
            url = "https://docs.example.com/test"
            config = create_enhanced_crawler_config(url=url, filtering_level=FilteringLevel.STANDARD)
            
            # Create run configuration with fixed CSS selector format
            run_config_kwargs = {
                'cache_mode': 'CacheMode.BYPASS',
                'stream': False,
                'word_count_threshold': config.get('word_count_threshold', 20),
                'excluded_tags': config.get('excluded_tags', []),
                'css_selector': ','.join(config.get('css_selector_to_exclude', []))  # Fixed format
            }
            
            # This should not fail
            css_selector = run_config_kwargs['css_selector']
            assert isinstance(css_selector, str), "CSS selector must be string"
            
            # Simulate what Crawl4AI does internally
            selectors = [s.strip() for s in css_selector.split(',')]
            assert isinstance(selectors, list), "Should successfully create selector list"


def test_regression_prevention_summary():
    """Summary test that verifies the regression is prevented"""
    print("\n" + "="*80)
    print("CSS SELECTOR REGRESSION PREVENTION SUMMARY")
    print("="*80)
    
    # Test the critical path that was failing
    config = create_enhanced_crawler_config(
        url="https://fastapi.tiangolo.com/tutorial/",
        filtering_level=FilteringLevel.STANDARD
    )
    
    css_selectors = config.get('css_selector_to_exclude', [])
    print(f"✅ Enhanced filtering returns list: {type(css_selectors).__name__} with {len(css_selectors)} selectors")
    
    css_string = ','.join(css_selectors)
    print(f"✅ List converts to string: {type(css_string).__name__} = '{css_string[:50]}...'")
    
    # Test the exact operation that was failing in Crawl4AI
    try:
        selectors = [s.strip() for s in css_string.split(',')]
        print(f"✅ Crawl4AI split operation succeeds: {len(selectors)} selectors extracted")
        print(f"✅ Sample selectors: {selectors[:3]}")
        
        print("\n🎉 REGRESSION PREVENTION SUCCESSFUL!")
        print("   The CSS selector bug cannot reoccur with these safeguards.")
        
    except AttributeError as e:
        print(f"❌ REGRESSION DETECTED: {e}")
        print("   The CSS selector bug is still present!")
        raise
    
    print("="*80)


if __name__ == "__main__":
    # Run the regression prevention test
    test_regression_prevention_summary()
    
    # Run all tests
    pytest.main([__file__, "-v"])