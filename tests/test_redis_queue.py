"""
Comprehensive Redis Queue Testing Suite
Tests the Redis-based job queue system for AY RAG MCP Server
"""

import pytest
import asyncio
import json
import time
from unittest.mock import Mock, patch, AsyncMock
from dataclasses import asdict

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from job_queue import (
    RedisJobQueue, JobRequest, JobResult, JobStatus,
    get_job_queue, shutdown_job_queue
)
from job_worker import CrawlJobWorker


class TestRedisJobQueue:
    """Test Redis job queue functionality"""
    
    @pytest.fixture
    async def job_queue(self):
        """Create and connect job queue for testing"""
        queue = RedisJobQueue("redis://localhost:6379", "test-ay-rag")
        await queue.connect()
        
        # Clean up any existing test data
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("test-ay-rag:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        yield queue
        await queue.disconnect()
    
    @pytest.fixture
    def sample_job_request(self):
        """Create sample job request for testing"""
        return JobRequest.create(
            job_type="smart_crawl",
            url="https://example.com",
            parameters={"max_depth": 2, "chunk_size": 1000},
            priority=5,
            max_retries=3,
            timeout=300
        )
    
    @pytest.mark.asyncio
    async def test_job_queue_connection(self, job_queue):
        """Test Redis connection and disconnection"""
        assert job_queue.redis_pool is not None
        
        # Test basic Redis operations
        redis_client = job_queue._get_redis_client()
        await redis_client.set("test_key", "test_value")
        value = await redis_client.get("test_key")
        assert value == "test_value"
        await redis_client.delete("test_key")
        await redis_client.aclose()
    
    @pytest.mark.asyncio
    async def test_enqueue_job(self, job_queue, sample_job_request):
        """Test job enqueue functionality"""
        job_id = await job_queue.enqueue_job(sample_job_request)
        
        assert job_id == sample_job_request.job_id
        
        # Verify job is in queue
        stats = await job_queue.get_queue_stats()
        assert stats["queued_jobs"] == 1
        assert stats["processing_jobs"] == 0
    
    @pytest.mark.asyncio
    async def test_dequeue_job(self, job_queue, sample_job_request):
        """Test job dequeue functionality"""
        # First enqueue a job
        await job_queue.enqueue_job(sample_job_request)
        
        # Then dequeue it
        dequeued_job = await job_queue.dequeue_job()
        
        assert dequeued_job is not None
        assert dequeued_job.job_id == sample_job_request.job_id
        assert dequeued_job.job_type == sample_job_request.job_type
        assert dequeued_job.url == sample_job_request.url
        
        # Verify job moved from queue to processing
        stats = await job_queue.get_queue_stats()
        assert stats["queued_jobs"] == 0
        assert stats["processing_jobs"] == 1
    
    @pytest.mark.asyncio
    async def test_job_status_updates(self, job_queue, sample_job_request):
        """Test job status tracking"""
        job_id = await job_queue.enqueue_job(sample_job_request)
        
        # Check initial status
        status = await job_queue.get_job_status(job_id)
        assert status.status == JobStatus.QUEUED
        
        # Update to processing
        await job_queue.update_job_status(job_id, JobStatus.PROCESSING)
        status = await job_queue.get_job_status(job_id)
        assert status.status == JobStatus.PROCESSING
        
        # Update to completed with results
        result_data = {"success": True, "pages_crawled": 5, "chunks_stored": 25}
        await job_queue.update_job_status(job_id, JobStatus.COMPLETED, result_data=result_data)
        
        status = await job_queue.get_job_status(job_id)
        assert status.status == JobStatus.COMPLETED
        assert status.result_data == result_data
        assert status.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_job_priority_ordering(self, job_queue):
        """Test job priority ordering in queue"""
        # Create jobs with different priorities
        high_priority_job = JobRequest.create(
            job_type="single_crawl", url="https://high.example.com",
            parameters={}, priority=1  # High priority
        )
        low_priority_job = JobRequest.create(
            job_type="single_crawl", url="https://low.example.com",
            parameters={}, priority=10  # Low priority
        )
        
        # Enqueue low priority first, then high priority
        await job_queue.enqueue_job(low_priority_job)
        await job_queue.enqueue_job(high_priority_job)
        
        # Dequeue should return high priority job first
        dequeued_job = await job_queue.dequeue_job()
        assert dequeued_job.job_id == high_priority_job.job_id
        
        # Next should be low priority job
        dequeued_job = await job_queue.dequeue_job()
        assert dequeued_job.job_id == low_priority_job.job_id
    
    @pytest.mark.asyncio
    async def test_cancel_job(self, job_queue, sample_job_request):
        """Test job cancellation"""
        job_id = await job_queue.enqueue_job(sample_job_request)
        
        # Cancel the job
        success = await job_queue.cancel_job(job_id)
        assert success is True
        
        # Verify job status is cancelled
        status = await job_queue.get_job_status(job_id)
        assert status.status == JobStatus.CANCELLED
        
        # Verify job is not in queue anymore
        stats = await job_queue.get_queue_stats()
        assert stats["queued_jobs"] == 0
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_jobs(self, job_queue):
        """Test cleanup of expired jobs"""
        # Create a job and simulate it being stuck in processing
        job_request = JobRequest.create(
            job_type="smart_crawl", url="https://stuck.example.com", parameters={}
        )
        job_id = await job_queue.enqueue_job(job_request)
        
        # Move to processing
        await job_queue.dequeue_job()
        
        # Mock old enqueue time to simulate timeout
        redis_client = job_queue._get_redis_client()
        job_key = job_queue.job_data_pattern.format(job_id)
        await redis_client.hset(job_key, "enqueued_at", str(time.time() - 3700))  # > 1 hour ago
        await redis_client.aclose()
        
        # Run cleanup
        cleaned_count = await job_queue.cleanup_expired_jobs()
        assert cleaned_count == 1
        
        # Verify job is marked as failed
        status = await job_queue.get_job_status(job_id)
        assert status.status == JobStatus.FAILED
        assert "timed out" in status.error_message
    
    @pytest.mark.asyncio
    async def test_concurrent_queue_operations(self, job_queue):
        """Test concurrent queue operations"""
        # Create multiple jobs
        jobs = [
            JobRequest.create(job_type="single_crawl", url=f"https://example{i}.com", parameters={})
            for i in range(10)
        ]
        
        # Enqueue all jobs concurrently
        enqueue_tasks = [job_queue.enqueue_job(job) for job in jobs]
        await asyncio.gather(*enqueue_tasks)
        
        # Verify all jobs are queued
        stats = await job_queue.get_queue_stats()
        assert stats["queued_jobs"] == 10
        
        # Dequeue all jobs concurrently
        dequeue_tasks = [job_queue.dequeue_job() for _ in range(10)]
        dequeued_jobs = await asyncio.gather(*dequeue_tasks)
        
        # Verify all jobs were dequeued successfully
        assert len(dequeued_jobs) == 10
        assert all(job is not None for job in dequeued_jobs)
        
        # Verify queue is empty and all are processing
        stats = await job_queue.get_queue_stats()
        assert stats["queued_jobs"] == 0
        assert stats["processing_jobs"] == 10


class TestCrawlJobWorker:
    """Test the background job worker"""
    
    @pytest.fixture
    async def mock_supabase(self):
        """Mock Supabase client"""
        mock_client = AsyncMock()
        mock_client.table.return_value.insert.return_value.execute.return_value = Mock(data=[{"id": 1}])
        return mock_client
    
    @pytest.fixture
    async def worker(self, mock_supabase):
        """Create worker instance for testing"""
        worker = CrawlJobWorker("redis://localhost:6379", "test-worker", concurrency=1)
        worker._supabase = mock_supabase
        
        # Mock the crawler initialization to avoid dependencies
        with patch('src.job_worker.AsyncWebCrawler'), \
             patch('src.job_worker.create_client', return_value=mock_supabase):
            await worker.initialize()
        
        yield worker
        
        await worker.shutdown()
    
    @pytest.mark.asyncio
    async def test_worker_initialization(self, worker):
        """Test worker initialization"""
        assert worker.worker_id == "test-worker"
        assert worker.concurrency == 1
        assert worker.job_queue is not None
        assert worker._supabase is not None
    
    @pytest.mark.asyncio
    async def test_process_single_crawl_job(self, worker):
        """Test processing of single crawl job"""
        # Mock the crawler result
        mock_result = Mock()
        mock_result.success = True
        mock_result.markdown = "# Test Content\n\nThis is test content for crawling."
        mock_result.error_message = None
        
        # Mock process_crawled_content to return success
        with patch.object(worker, '_process_crawled_content', return_value=(5, "example.com")):
            job_request = JobRequest.create(
                job_type="single_crawl",
                url="https://example.com",
                parameters={"chunk_size": 1000}
            )
            
            result = await worker._process_single_crawl(job_request)
            
            assert result["success"] is True
            assert result["url"] == "https://example.com"
            assert result["pages_crawled"] == 1
            assert result["chunks_stored"] == 5
            assert result["source"] == "example.com"
    
    @pytest.mark.asyncio
    async def test_process_smart_crawl_job(self, worker):
        """Test processing of smart crawl job"""
        # Mock URLs extraction for smart crawl
        with patch.object(worker, '_extract_sitemap_urls', return_value=["https://example.com/page1", "https://example.com/page2"]), \
             patch.object(worker, '_crawl_single_url_with_semaphore', return_value=(3, "example.com")):
            
            job_request = JobRequest.create(
                job_type="smart_crawl",
                url="https://example.com/sitemap.xml",
                parameters={"max_depth": 2, "max_concurrent": 5, "chunk_size": 1000}
            )
            
            result = await worker._process_smart_crawl(job_request)
            
            assert result["success"] is True
            assert result["url"] == "https://example.com/sitemap.xml"
            assert result["total_pages_crawled"] == 2
            assert result["total_chunks_stored"] == 6  # 2 pages * 3 chunks each
    
    @pytest.mark.asyncio
    async def test_worker_error_handling(self, worker):
        """Test worker error handling"""
        # Create a job that will fail
        job_request = JobRequest.create(
            job_type="invalid_type",
            url="https://invalid.com",
            parameters={}
        )
        
        # Mock job queue operations
        worker.job_queue.update_job_status = AsyncMock()
        
        await worker._process_job(job_request)
        
        # Verify error was handled and job marked as failed
        worker.job_queue.update_job_status.assert_any_call(
            job_request.job_id, JobStatus.FAILED, error_message=pytest.StringMatching("Unknown job type")
        )


class TestRedisQueuePerformance:
    """Performance tests for Redis queue system"""
    
    @pytest.fixture
    async def job_queue(self):
        """Create job queue for performance testing"""
        queue = RedisJobQueue("redis://localhost:6379", "perf-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("perf-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        yield queue
        await queue.disconnect()
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_queue_throughput(self, job_queue):
        """Test queue throughput with many jobs"""
        num_jobs = 100
        start_time = time.time()
        
        # Create and enqueue jobs
        jobs = [
            JobRequest.create(
                job_type="single_crawl", 
                url=f"https://test{i}.com", 
                parameters={}
            ) for i in range(num_jobs)
        ]
        
        # Enqueue all jobs
        enqueue_tasks = [job_queue.enqueue_job(job) for job in jobs]
        await asyncio.gather(*enqueue_tasks)
        
        enqueue_time = time.time() - start_time
        
        # Dequeue all jobs
        start_time = time.time()
        dequeue_tasks = [job_queue.dequeue_job() for _ in range(num_jobs)]
        dequeued_jobs = await asyncio.gather(*dequeue_tasks)
        
        dequeue_time = time.time() - start_time
        
        # Performance assertions
        assert len(dequeued_jobs) == num_jobs
        assert all(job is not None for job in dequeued_jobs)
        
        # Performance metrics (should complete within reasonable time)
        assert enqueue_time < 5.0  # Should enqueue 100 jobs in < 5 seconds
        assert dequeue_time < 5.0  # Should dequeue 100 jobs in < 5 seconds
        
        print(f"Enqueue throughput: {num_jobs/enqueue_time:.2f} jobs/second")
        print(f"Dequeue throughput: {num_jobs/dequeue_time:.2f} jobs/second")
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_memory_usage(self, job_queue):
        """Test memory usage with large job payloads"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create jobs with large payloads
        large_payload = "x" * 10000  # 10KB payload
        jobs = [
            JobRequest.create(
                job_type="smart_crawl",
                url=f"https://large{i}.com",
                parameters={"large_data": large_payload}
            ) for i in range(50)
        ]
        
        # Enqueue jobs
        for job in jobs:
            await job_queue.enqueue_job(job)
        
        # Process jobs
        for _ in range(50):
            job = await job_queue.dequeue_job()
            await job_queue.update_job_status(job.job_id, JobStatus.COMPLETED)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (< 100MB for this test)
        assert memory_increase < 100
        print(f"Memory increase: {memory_increase:.2f} MB")


class TestRedisQueueIntegration:
    """Integration tests for Redis queue with MCP tools"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_crawl_tool_integration(self):
        """Test integration with async crawl tools"""
        # This would test the actual MCP tools with Redis queue
        # Mock the environment and dependencies
        from unittest.mock import patch, AsyncMock
        
        with patch.dict('os.environ', {
            'REDIS_URL': 'redis://localhost:6379',
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key',
            'OPENAI_API_KEY': 'test-key'
        }):
            # Mock the smart_crawl_url_async function
            with patch('src.ay_rag_mcp.smart_crawl_url_async') as mock_smart_crawl:
                mock_smart_crawl.return_value = {
                    "success": True,
                    "job_id": "test-job-123",
                    "status": "queued",
                    "message": "Crawl job queued successfully"
                }
                
                # Test would call the actual MCP tool
                result = await mock_smart_crawl({
                    "url": "https://example.com",
                    "max_depth": 2,
                    "chunk_size": 1000
                })
                
                assert result["success"] is True
                assert "job_id" in result
                assert result["status"] == "queued"
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_job_status_polling(self):
        """Test job status polling functionality"""
        queue = RedisJobQueue("redis://localhost:6379", "integration-test")
        await queue.connect()
        
        try:
            # Create and enqueue a job
            job_request = JobRequest.create(
                job_type="smart_crawl",
                url="https://integration.test.com",
                parameters={}
            )
            job_id = await queue.enqueue_job(job_request)
            
            # Simulate job processing
            await queue.dequeue_job()  # Move to processing
            await queue.update_job_status(job_id, JobStatus.PROCESSING)
            
            # Poll for status
            status = await queue.get_job_status(job_id)
            assert status.status == JobStatus.PROCESSING
            
            # Complete the job
            result_data = {"success": True, "pages_crawled": 3}
            await queue.update_job_status(job_id, JobStatus.COMPLETED, result_data=result_data)
            
            # Final status check
            final_status = await queue.get_job_status(job_id)
            assert final_status.status == JobStatus.COMPLETED
            assert final_status.result_data == result_data
            
        finally:
            await queue.disconnect()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])