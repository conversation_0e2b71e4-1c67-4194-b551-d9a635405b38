"""
Test suite for hybrid scoring system.
"""
import pytest
import math
from src.hybrid_scoring import (
    HybridScorer, 
    ScoringConfig,
    get_default_scorer,
    get_conservative_scorer,
    get_keyword_focused_scorer
)

class TestScoringConfig:
    """Test scoring configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = ScoringConfig()
        
        assert config.vector_weight == 0.5
        assert config.keyword_weight == 0.3
        assert config.rerank_weight == 0.2
        assert config.dual_match_boost == 1.2
        assert config.use_sigmoid_normalization is True
        assert config.rerank_score_threshold == -2.0

class TestHybridScorer:
    """Test hybrid scoring functionality."""
    
    @pytest.fixture
    def scorer(self):
        """Create default scorer for testing."""
        return HybridScorer()
    
    @pytest.fixture
    def custom_scorer(self):
        """Create scorer with custom config."""
        config = ScoringConfig(
            vector_weight=0.6,
            keyword_weight=0.4,
            rerank_weight=0.0,
            dual_match_boost=1.5
        )
        return HybridScorer(config)
    
    def test_normalize_rerank_score_sigmoid(self, scorer):
        """Test sigmoid normalization of rerank scores."""
        # Test normal range
        assert abs(scorer.normalize_rerank_score(0.0) - 0.5) < 0.01
        assert scorer.normalize_rerank_score(2.0) > 0.8
        assert scorer.normalize_rerank_score(-2.0) < 0.2
        
        # Test extreme values
        assert scorer.normalize_rerank_score(10.0) > 0.99
        assert scorer.normalize_rerank_score(-10.0) < 0.01
    
    def test_normalize_rerank_score_linear(self):
        """Test linear normalization of rerank scores."""
        config = ScoringConfig(use_sigmoid_normalization=False)
        scorer = HybridScorer(config)
        
        assert scorer.normalize_rerank_score(0.0) == 0.5
        assert scorer.normalize_rerank_score(2.0) == 1.0
        assert scorer.normalize_rerank_score(-2.0) == 0.0
        assert scorer.normalize_rerank_score(-5.0) == 0.0  # Clipped to 0
        assert scorer.normalize_rerank_score(5.0) == 1.0   # Clipped to 1
    
    def test_combine_scores_basic(self, scorer):
        """Test basic score combination."""
        # Vector only
        score = scorer.combine_scores(0.8, False, None, False)
        expected = 0.8 * (0.5 / 0.8) + 0.0 * (0.3 / 0.8)  # Redistributed weights
        assert abs(score - expected) < 0.01
        
        # Vector + keyword
        score = scorer.combine_scores(0.8, True, None, False)
        expected = 0.8 * (0.5 / 0.8) + 1.0 * (0.3 / 0.8)  # Redistributed weights
        assert abs(score - expected) < 0.01
    
    def test_combine_scores_with_reranking(self, scorer):
        """Test score combination with reranking."""
        score = scorer.combine_scores(0.8, True, 1.0, False)
        
        # Should use all three weights
        vector_component = 0.5 * 0.8
        keyword_component = 0.3 * 1.0
        rerank_component = 0.2 * scorer.normalize_rerank_score(1.0)
        expected = vector_component + keyword_component + rerank_component
        
        assert abs(score - expected) < 0.01
    
    def test_combine_scores_dual_match_boost(self, scorer):
        """Test dual match boost application."""
        # Without boost
        score1 = scorer.combine_scores(0.8, True, 1.0, False)
        
        # With boost
        score2 = scorer.combine_scores(0.8, True, 1.0, True)
        
        # Score2 should be higher due to dual match boost
        assert score2 > score1
    
    def test_combine_scores_threshold_filtering(self, scorer):
        """Test rerank score threshold filtering."""
        # Score above threshold
        score1 = scorer.combine_scores(0.8, True, -1.0, False)
        
        # Score below threshold
        score2 = scorer.combine_scores(0.8, True, -3.0, False)
        
        # Both should be valid, but different
        assert 0 <= score1 <= 1
        assert 0 <= score2 <= 1
    
    def test_merge_hybrid_results_empty(self, scorer):
        """Test merging with empty results."""
        result = scorer.merge_hybrid_results([], [], 5)
        assert result == []
        
        result = scorer.merge_hybrid_results([{'id': 1}], [], 5)
        assert len(result) == 1
    
    def test_merge_hybrid_results_dual_matches(self, scorer):
        """Test prioritization of dual matches."""
        vector_results = [
            {'id': 1, 'similarity': 0.9, 'content': 'text1'},
            {'id': 2, 'similarity': 0.7, 'content': 'text2'},
            {'id': 3, 'similarity': 0.8, 'content': 'text3'}
        ]
        
        keyword_results = [
            {'id': 1, 'content': 'text1'},
            {'id': 4, 'content': 'text4'}
        ]
        
        result = scorer.merge_hybrid_results(vector_results, keyword_results, 5)
        
        # First result should be the dual match (id=1)
        assert result[0]['id'] == 1
        assert result[0]['appears_in_both'] is True
        assert result[0]['match_type'] == 'hybrid'
        assert 'final_score' in result[0]
    
    def test_merge_hybrid_results_vector_fallback(self, scorer):
        """Test vector result fallback."""
        vector_results = [
            {'id': 1, 'similarity': 0.9, 'content': 'text1'},
            {'id': 2, 'similarity': 0.7, 'content': 'text2'}
        ]
        
        keyword_results = [
            {'id': 3, 'content': 'text3'}  # No overlap
        ]
        
        result = scorer.merge_hybrid_results(vector_results, keyword_results, 5)
        
        # Should have vector-only matches
        vector_only = [r for r in result if r['match_type'] == 'vector']
        assert len(vector_only) >= 2
    
    def test_merge_hybrid_results_keyword_fallback(self, scorer):
        """Test keyword result fallback."""
        vector_results = [
            {'id': 1, 'similarity': 0.9, 'content': 'text1'}
        ]
        
        keyword_results = [
            {'id': 2, 'content': 'text2'},
            {'id': 3, 'content': 'text3'},
            {'id': 4, 'content': 'text4'}
        ]
        
        result = scorer.merge_hybrid_results(vector_results, keyword_results, 5)
        
        # Should have keyword-only matches
        keyword_only = [r for r in result if r['match_type'] == 'keyword']
        assert len(keyword_only) >= 2
        
        # Check keyword-only result format
        for kw_result in keyword_only:
            assert 'final_score' in kw_result
            assert kw_result['similarity'] == 0.5  # Default similarity
    
    def test_apply_reranking_boost(self, scorer):
        """Test reranking boost application."""
        results = [
            {'id': 1, 'similarity': 0.9, 'content': 'text1', 'match_type': 'vector'},
            {'id': 2, 'similarity': 0.7, 'content': 'text2', 'match_type': 'hybrid', 'appears_in_both': True}
        ]
        
        rerank_scores = [1.5, -0.5]
        
        enhanced = scorer.apply_reranking_boost(results, rerank_scores)
        
        # Check rerank scores added
        assert enhanced[0]['rerank_score'] == 1.5
        assert enhanced[1]['rerank_score'] == -0.5
        
        # Check final scores updated
        assert 'final_score' in enhanced[0]
        assert 'final_score' in enhanced[1]
        
        # Results should be sorted by final score
        assert enhanced[0]['final_score'] >= enhanced[1]['final_score']

class TestScorerVariants:
    """Test different scorer configurations."""
    
    def test_default_scorer(self):
        """Test default scorer creation."""
        scorer = get_default_scorer()
        assert isinstance(scorer, HybridScorer)
        assert scorer.config.vector_weight == 0.5
    
    def test_conservative_scorer(self):
        """Test conservative scorer emphasizes vector similarity."""
        scorer = get_conservative_scorer()
        assert scorer.config.vector_weight > 0.5
        assert scorer.config.dual_match_boost < 1.2
    
    def test_keyword_focused_scorer(self):
        """Test keyword-focused scorer for technical content."""
        scorer = get_keyword_focused_scorer()
        assert scorer.config.keyword_weight >= 0.4
        assert scorer.config.dual_match_boost > 1.2

class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_extreme_rerank_scores(self):
        """Test handling of extreme rerank scores."""
        scorer = HybridScorer()
        
        # Very large positive
        score = scorer.normalize_rerank_score(100.0)
        assert 0.99 <= score <= 1.0
        
        # Very large negative
        score = scorer.normalize_rerank_score(-100.0)
        assert 0.0 <= score <= 0.01
    
    def test_invalid_similarity_scores(self):
        """Test handling of invalid similarity scores."""
        scorer = HybridScorer()
        
        # Test boundary values
        score = scorer.combine_scores(-0.1, True, 1.0, False)
        assert 0.0 <= score <= 1.0
        
        score = scorer.combine_scores(1.5, True, 1.0, False)
        assert 0.0 <= score <= 1.0
    
    def test_missing_result_fields(self):
        """Test handling of results with missing fields."""
        scorer = HybridScorer()
        
        results = [
            {'id': 1},  # Missing similarity and content
            {'id': 2, 'content': 'text2'}  # Missing similarity
        ]
        
        rerank_scores = [0.5, -0.5]
        enhanced = scorer.apply_reranking_boost(results, rerank_scores)
        
        # Should handle missing fields gracefully
        assert len(enhanced) == 2
        assert all('final_score' in r for r in enhanced)
    
    def test_weight_normalization_warning(self):
        """Test that weights don't need to sum to 1.0 exactly."""
        config = ScoringConfig(
            vector_weight=0.6,
            keyword_weight=0.3,
            rerank_weight=0.05  # Sums to 0.95
        )
        scorer = HybridScorer(config)
        
        # Should still work
        score = scorer.combine_scores(0.8, True, 1.0, False)
        assert 0.0 <= score <= 1.0

@pytest.mark.integration
class TestIntegration:
    """Integration tests with realistic data."""
    
    def test_realistic_search_scenario(self):
        """Test with realistic search results."""
        scorer = get_default_scorer()
        
        # Simulate vector search results
        vector_results = [
            {'id': 'doc1', 'similarity': 0.85, 'content': 'Python function for data processing'},
            {'id': 'doc2', 'similarity': 0.72, 'content': 'Machine learning model training'},
            {'id': 'doc3', 'similarity': 0.68, 'content': 'Data processing pipeline setup'}
        ]
        
        # Simulate keyword search results  
        keyword_results = [
            {'id': 'doc1', 'content': 'Python function for data processing'},  # Overlap
            {'id': 'doc4', 'content': 'Python tutorial for beginners'},
            {'id': 'doc5', 'content': 'Function definition in Python'}
        ]
        
        # Merge results
        merged = scorer.merge_hybrid_results(vector_results, keyword_results, 5)
        
        # Verify realistic behavior
        assert len(merged) <= 5
        assert merged[0]['id'] == 'doc1'  # Should be top due to dual match
        assert merged[0]['appears_in_both'] is True
        
        # Verify score progression
        scores = [r['final_score'] for r in merged]
        assert scores == sorted(scores, reverse=True)
    
    def test_performance_with_large_results(self):
        """Test performance with large result sets."""
        scorer = get_default_scorer()
        
        # Generate large result sets
        vector_results = [
            {'id': f'v{i}', 'similarity': 0.9 - i*0.01, 'content': f'content {i}'}
            for i in range(100)
        ]
        
        keyword_results = [
            {'id': f'k{i}', 'content': f'keyword content {i}'}
            for i in range(50)
        ]
        
        # Should handle large datasets efficiently
        merged = scorer.merge_hybrid_results(vector_results, keyword_results, 20)
        
        assert len(merged) == 20
        assert all('final_score' in r for r in merged)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])