"""
Test suite for contextual embeddings functionality.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from src.contextual_embeddings import (
    ContextualConfig,
    ContextualEmbeddingProcessor,
    SimpleContextualProcessor,
    get_contextual_processor,
    process_chunk_with_context_async
)

class TestContextualConfig:
    """Test contextual embedding configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = ContextualConfig()
        
        assert config.max_context_length == 2000
        assert config.chunk_overlap == 200
        assert config.max_context_chunks == 5
        assert config.enable_document_summary is True
        assert "Document context:" in config.context_template

class TestContextualEmbeddingProcessor:
    """Test LLM-based contextual embedding processor."""
    
    @pytest.fixture
    def processor(self):
        """Create processor for testing."""
        return ContextualEmbeddingProcessor()
    
    @pytest.fixture
    def mock_llm_client(self):
        """Create mock LLM client."""
        client = AsyncMock()
        client.generate_async.return_value = {
            'content': 'Enhanced contextual summary'
        }
        return client
    
    def test_extract_document_context_found_chunk(self, processor):
        """Test context extraction when chunk is found in document."""
        document = "This is the beginning. The target chunk content here. This is the end."
        chunk = "The target chunk content here."
        
        context = processor.extract_document_context(document, chunk)
        
        assert "The target chunk content here." in context
        assert len(context) <= processor.config.max_context_length
    
    def test_extract_document_context_not_found(self, processor):
        """Test context extraction when chunk is not found."""
        document = "This is a long document with multiple sections."
        chunk = "Non-existent chunk content."
        chunk_position = 1
        
        context = processor.extract_document_context(document, chunk, chunk_position)
        
        assert len(context) <= processor.config.max_context_length
        assert len(context) > 0
    
    def test_clean_context_boundaries(self, processor):
        """Test context boundary cleaning."""
        context = "incomplete sentence start. This is a complete sentence. Another complete sentence. incomplete end"
        chunk = "test chunk"
        
        cleaned = processor._clean_context_boundaries(context, chunk)
        
        # Should remove incomplete sentences at boundaries
        assert not cleaned.startswith("incomplete")
        assert not cleaned.endswith("incomplete end")
        assert "This is a complete sentence." in cleaned
    
    @pytest.mark.asyncio
    async def test_generate_contextual_content_success(self, processor, mock_llm_client):
        """Test successful contextual content generation."""
        with patch.object(processor, '_get_llm_client', return_value=mock_llm_client):
            document = "Full document content with context."
            chunk = "Specific chunk to enhance."
            metadata = {"original": "metadata"}
            
            enhanced_content, enhanced_metadata = await processor.generate_contextual_content(
                document, chunk, metadata
            )
            
            assert enhanced_content == "Enhanced contextual summary"
            assert enhanced_metadata['has_contextual_embedding'] is True
            assert enhanced_metadata['enhancement_method'] == 'llm_contextual'
            assert enhanced_metadata['original'] == "metadata"
            
            # Verify LLM was called
            mock_llm_client.generate_async.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_contextual_content_failure(self, processor):
        """Test contextual content generation failure fallback."""
        with patch.object(processor, '_get_llm_client', side_effect=Exception("LLM error")):
            document = "Full document content."
            chunk = "Original chunk content."
            metadata = {"test": "data"}
            
            enhanced_content, enhanced_metadata = await processor.generate_contextual_content(
                document, chunk, metadata
            )
            
            # Should fallback to original content
            assert enhanced_content == chunk
            assert enhanced_metadata == metadata
    
    def test_create_context_prompt(self, processor):
        """Test context prompt creation."""
        context = "Document context information."
        chunk = "Specific chunk content."
        metadata = {"section": "introduction"}
        
        prompt = processor._create_context_prompt(context, chunk, metadata)
        
        assert "Document Context:" in prompt
        assert "Specific Chunk:" in prompt
        assert context in prompt
        assert chunk in prompt
    
    @pytest.mark.asyncio
    async def test_process_document_chunks_batch_processing(self, processor, mock_llm_client):
        """Test batch processing of multiple chunks."""
        with patch.object(processor, '_get_llm_client', return_value=mock_llm_client):
            document = "Full document with multiple sections."
            chunks = ["chunk1", "chunk2", "chunk3"]
            
            results = await processor.process_document_chunks(document, chunks)
            
            assert len(results) == 3
            assert all(isinstance(r, tuple) and len(r) == 2 for r in results)
            
            # Verify all chunks were processed
            assert mock_llm_client.generate_async.call_count == 3
    
    @pytest.mark.asyncio
    async def test_process_document_chunks_with_exceptions(self, processor):
        """Test batch processing with some failures."""
        mock_client = AsyncMock()
        # First call succeeds, second fails, third succeeds
        mock_client.generate_async.side_effect = [
            {'content': 'Success 1'},
            Exception("LLM error"),
            {'content': 'Success 3'}
        ]
        
        with patch.object(processor, '_get_llm_client', return_value=mock_client):
            document = "Test document."
            chunks = ["chunk1", "chunk2", "chunk3"]
            metadata_list = [{"id": 1}, {"id": 2}, {"id": 3}]
            
            results = await processor.process_document_chunks(document, chunks, metadata_list)
            
            assert len(results) == 3
            
            # First and third should be enhanced, second should be original
            assert results[0][0] == "Success 1"
            assert results[1][0] == "chunk2"  # Original due to failure
            assert results[2][0] == "Success 3"
    
    @pytest.mark.asyncio
    async def test_process_document_chunks_empty(self, processor):
        """Test processing empty chunk list."""
        results = await processor.process_document_chunks("document", [])
        assert results == []

class TestSimpleContextualProcessor:
    """Test heuristic-based contextual processor."""
    
    @pytest.fixture
    def processor(self):
        """Create simple processor for testing."""
        return SimpleContextualProcessor()
    
    def test_extract_surrounding_context(self, processor):
        """Test surrounding context extraction."""
        document = "Start of document. Target chunk content here. End of document."
        chunk = "Target chunk content here."
        
        context = processor.extract_surrounding_context(document, chunk, context_size=20)
        
        assert chunk in context
        assert len(context) <= len(document)
    
    def test_extract_surrounding_context_not_found(self, processor):
        """Test context extraction when chunk not found."""
        document = "Document content."
        chunk = "Missing chunk."
        
        context = processor.extract_surrounding_context(document, chunk)
        
        assert context == ""
    
    def test_enhance_chunk_with_headers(self, processor):
        """Test chunk enhancement with headers."""
        document = """# Main Title
        
        Some introduction text.
        
        ## Section 1
        
        This is section one content.
        Target chunk content here.
        
        ## Section 2
        
        This is section two."""
        
        chunk = "Target chunk content here."
        metadata = {"original": "data"}
        
        enhanced_content, enhanced_metadata = processor.enhance_chunk_with_headers(
            document, chunk, metadata
        )
        
        assert "[Section: Section 1]" in enhanced_content
        assert chunk in enhanced_content
        assert enhanced_metadata['has_contextual_embedding'] is True
        assert enhanced_metadata['relevant_header'] == "Section 1"
        assert enhanced_metadata['enhancement_method'] == 'heuristic_headers'
        assert enhanced_metadata['original'] == "data"
    
    def test_enhance_chunk_no_headers(self, processor):
        """Test chunk enhancement when no headers found."""
        document = "Simple document without headers. Target chunk here."
        chunk = "Target chunk here."
        
        enhanced_content, enhanced_metadata = processor.enhance_chunk_with_headers(
            document, chunk
        )
        
        assert enhanced_content == chunk  # No enhancement
        assert enhanced_metadata['relevant_header'] is None
    
    def test_extract_headers(self, processor):
        """Test markdown header extraction."""
        document = """# H1 Title
        ## H2 Section
        ### H3 Subsection
        #### H4 Detail
        Regular text
        ## Another H2"""
        
        headers = processor._extract_headers(document)
        
        assert len(headers) == 4
        assert headers[0] == (1, "H1 Title", "# H1 Title")
        assert headers[1] == (2, "H2 Section", "## H2 Section")
        assert headers[2] == (3, "H3 Subsection", "### H3 Subsection")
        assert headers[3] == (2, "Another H2", "## Another H2")
    
    def test_find_relevant_header(self, processor):
        """Test finding relevant header for chunk."""
        document = """# Title
        Introduction text.
        ## Section A
        Section A content.
        ## Section B
        Target chunk here.
        ## Section C
        Section C content."""
        
        headers = processor._extract_headers(document)
        chunk = "Target chunk here."
        
        relevant = processor._find_relevant_header(document, chunk, headers)
        
        assert relevant == "Section B"
    
    def test_find_relevant_header_not_found(self, processor):
        """Test header finding when chunk not in document."""
        document = "Document content with ## Header"
        headers = processor._extract_headers(document)
        chunk = "Missing chunk"
        
        relevant = processor._find_relevant_header(document, chunk, headers)
        
        assert relevant is None

class TestProcessorFactory:
    """Test processor factory functions."""
    
    @patch.dict('os.environ', {'USE_CONTEXTUAL_EMBEDDINGS': 'true'})
    def test_get_contextual_processor_llm_enabled(self):
        """Test getting LLM processor when enabled."""
        processor = get_contextual_processor(use_llm=True)
        
        # Should try to create LLM processor, fall back to simple on import error
        assert isinstance(processor, (ContextualEmbeddingProcessor, SimpleContextualProcessor))
    
    def test_get_contextual_processor_llm_disabled(self):
        """Test getting simple processor when LLM disabled."""
        processor = get_contextual_processor(use_llm=False)
        
        assert isinstance(processor, SimpleContextualProcessor)
    
    @patch.dict('os.environ', {'USE_CONTEXTUAL_EMBEDDINGS': 'false'})
    def test_get_contextual_processor_env_disabled(self):
        """Test getting simple processor when env var disabled."""
        processor = get_contextual_processor(use_llm=True)
        
        assert isinstance(processor, SimpleContextualProcessor)
    
    @pytest.mark.asyncio
    async def test_process_chunk_with_context_async_simple(self):
        """Test async wrapper with simple processor."""
        document = "## Header\nDocument content.\nTarget chunk here."
        chunk = "Target chunk here."
        metadata = {"test": "data"}
        
        enhanced_content, enhanced_metadata = await process_chunk_with_context_async(
            document, chunk, metadata, use_llm=False
        )
        
        assert "[Section: Header]" in enhanced_content
        assert enhanced_metadata['has_contextual_embedding'] is True
        assert enhanced_metadata['test'] == "data"

class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_empty_document(self):
        """Test processing with empty document."""
        processor = SimpleContextualProcessor()
        
        enhanced_content, enhanced_metadata = processor.enhance_chunk_with_headers(
            "", "chunk", {}
        )
        
        assert enhanced_content == "chunk"
        assert enhanced_metadata['relevant_header'] is None
    
    def test_very_long_document(self):
        """Test processing with very long document."""
        processor = ContextualEmbeddingProcessor()
        
        # Create very long document
        document = "Section content. " * 10000  # ~150k characters
        chunk = "Target chunk."
        
        context = processor.extract_document_context(document, chunk)
        
        # Should be truncated to max length
        assert len(context) <= processor.config.max_context_length
    
    def test_special_characters_in_headers(self):
        """Test header extraction with special characters."""
        processor = SimpleContextualProcessor()
        
        document = """# Title with "quotes" and & symbols
        ## Section with <tags> and [brackets]
        Content here."""
        
        headers = processor._extract_headers(document)
        
        assert len(headers) == 2
        assert 'quotes' in headers[0][1]
        assert 'tags' in headers[1][1]
    
    @pytest.mark.asyncio
    async def test_llm_timeout_handling(self):
        """Test handling of LLM timeouts."""
        processor = ContextualEmbeddingProcessor()
        
        mock_client = AsyncMock()
        mock_client.generate_async.side_effect = asyncio.TimeoutError("Timeout")
        
        with patch.object(processor, '_get_llm_client', return_value=mock_client):
            enhanced_content, enhanced_metadata = await processor.generate_contextual_content(
                "document", "chunk", {}
            )
            
            # Should fallback to original
            assert enhanced_content == "chunk"
            assert enhanced_metadata == {}

if __name__ == "__main__":
    pytest.main([__file__, "-v"])