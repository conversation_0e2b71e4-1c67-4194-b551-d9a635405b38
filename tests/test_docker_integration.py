#!/usr/bin/env python3
"""
Test Docker Crawl4AI integration with corrected API endpoints.
"""
import os
import sys
import asyncio
import json
from unittest.mock import MagicMock

# Add src directory to path for imports
sys.path.insert(0, '/home/<USER>/dev/tools/mcp_servers/mcp-crawl4ai-rag/src')

# Set test environment
os.environ["DOCKER_MODE_ENABLED"] = "true"
os.environ["CRAWL4AI_DOCKER_HOST"] = "http://localhost:11235"

print("🐳 Testing Corrected Docker Crawl4AI Integration")

async def test_docker_client():
    """Test the corrected Docker client."""
    print("\n🔗 Testing Docker Client...")
    
    try:
        from docker_crawl_client import DockerCrawlClient
        
        client = DockerCrawlClient()
        
        # Test health check
        is_healthy = await client.health_check()
        print(f"✅ Health check: {'Healthy' if is_healthy else 'Unhealthy'}")
        
        if is_healthy:
            # Test sync crawl
            results = await client.crawl_sync(["https://httpbin.org/html"])
            print(f"✅ Sync crawl: {results['success']}, {len(results['results'])} results")
            
            # Test stream crawl
            stream_results = []
            async for data in client.crawl_stream(["https://httpbin.org/html"]):
                stream_results.append(data)
            print(f"✅ Stream crawl: {len(stream_results)} stream items")
            
            # Test background task
            task_id = await client.crawl_background(
                "test-bg-123",
                ["https://httpbin.org/html"]
            )
            print(f"✅ Background task: {task_id}")
            
            # Check task status
            await asyncio.sleep(1)  # Let it start
            status = client.get_background_task_status(task_id)
            print(f"✅ Task status: {status['status']}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Docker client test failed: {e}")
        return False

async def test_docker_mcp_tools():
    """Test the corrected Docker MCP tools."""
    print("\n🛠️  Testing Docker MCP Tools...")
    
    try:
        from docker_mcp_tools import docker_smart_crawl, docker_get_crawl_status, docker_list_crawl_tasks
        
        # Create mock context
        mock_context = MagicMock()
        mock_supabase = MagicMock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Test smart crawl (sync mode)
        result = await docker_smart_crawl(
            mock_context,
            "https://httpbin.org/html",
            max_depth=1,
            use_background=False
        )
        
        result_data = json.loads(result)
        print(f"✅ Smart crawl (sync): {result_data['success']}")
        
        # Test smart crawl (background mode)
        bg_result = await docker_smart_crawl(
            mock_context,
            "https://httpbin.org/html",
            max_depth=1,
            use_background=True
        )
        
        bg_data = json.loads(bg_result)
        print(f"✅ Smart crawl (background): {bg_data['success']}")
        
        if bg_data["success"] and "task_id" in bg_data:
            # Test status check
            await asyncio.sleep(1)
            status_result = await docker_get_crawl_status(mock_context, bg_data["task_id"])
            status_data = json.loads(status_result)
            print(f"✅ Status check: {status_data['status']}")
        
        # Test list tasks
        list_result = await docker_list_crawl_tasks(mock_context)
        list_data = json.loads(list_result)
        print(f"✅ List tasks: {list_data['count']} tasks")
        
        return True
        
    except Exception as e:
        print(f"❌ Docker MCP tools test failed: {e}")
        return False

async def test_disabled_mode():
    """Test behavior when Docker mode is disabled."""
    print("\n🚫 Testing Disabled Mode...")
    
    try:
        # Temporarily disable Docker mode
        os.environ["DOCKER_MODE_ENABLED"] = "false"
        
        from docker_mcp_tools import docker_smart_crawl
        
        mock_context = MagicMock()
        result = await docker_smart_crawl(mock_context, "https://example.com")
        result_data = json.loads(result)
        
        # Should return error when disabled
        success = not result_data["success"] and "not enabled" in result_data["error"]
        print(f"✅ Disabled mode handled correctly: {success}")
        
        # Restore enabled mode
        os.environ["DOCKER_MODE_ENABLED"] = "true"
        
        return success
        
    except Exception as e:
        print(f"❌ Disabled mode test failed: {e}")
        return False

async def run_docker_tests():
    """Run all Docker integration tests."""
    print("🚀 Running Corrected Docker Integration Tests\n")
    
    tests = [
        ("Docker Client", test_docker_client),
        ("Docker MCP Tools", test_docker_mcp_tools),
        ("Disabled Mode", test_disabled_mode),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Docker Integration Test Results:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All Docker integration tests passed!")
        return True
    else:
        print(f"💥 {total - passed} test(s) failed")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_docker_tests())
    sys.exit(0 if success else 1)