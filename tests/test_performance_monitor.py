"""
Test suite for performance monitoring system
"""

import pytest
import time
import tempfile
import json
from src.performance_monitor import (
    PerformanceMonitor,
    FilteringMetrics,
    PerformanceSnapshot
)


class MockClassificationResult:
    """Mock classification result for testing"""
    def __init__(self, content_type='documentation', confidence=0.7, strategy='balanced'):
        self.content_type = content_type
        self.confidence_score = confidence
        self.strategy = strategy


class MockFilterResult:
    """Mock filter result for testing"""
    def __init__(self, confidence=0.8, adaptive_confidence=0.75):
        self.confidence_score = confidence
        self.adaptive_confidence = adaptive_confidence


class TestPerformanceMonitor:
    """Test performance monitoring functionality"""
    
    def setup_method(self):
        self.monitor = PerformanceMonitor(max_history_size=1000, snapshot_interval_minutes=1)
    
    def test_metrics_recording(self):
        """Test recording of filtering operation metrics"""
        initial_count = len(self.monitor.metrics_history)
        
        classification_result = MockClassificationResult('tutorial', 0.8, 'lenient')
        filter_result = MockFilterResult(0.9, 0.85)
        
        self.monitor.record_filtering_operation(
            classification_result=classification_result,
            filter_result=filter_result,
            processing_time_ms=150.5,
            content_length=500,
            url="https://tutorial.example.com/step1",
            was_filtered=False,
            review_recommended=False
        )
        
        # Check that metrics were recorded
        assert len(self.monitor.metrics_history) == initial_count + 1
        
        # Verify the recorded metrics
        recorded_metric = self.monitor.metrics_history[-1]
        assert recorded_metric.content_type == 'tutorial'
        assert recorded_metric.strategy == 'lenient'
        assert recorded_metric.classification_confidence == 0.8
        assert recorded_metric.filter_confidence == 0.9
        assert recorded_metric.adaptive_confidence == 0.85
        assert not recorded_metric.was_filtered
        assert not recorded_metric.review_recommended
        assert recorded_metric.processing_time_ms == 150.5
        assert recorded_metric.content_length == 500
        assert recorded_metric.url_domain == "tutorial.example.com"
    
    def test_aggregate_statistics_updating(self):
        """Test that aggregate statistics are properly updated"""
        initial_total = self.monitor.stats['total_operations']
        
        # Record several operations
        test_operations = [
            ('marketing', 'strict', 100.0, 200, True, False),
            ('tutorial', 'lenient', 75.0, 300, False, False),
            ('documentation', 'balanced', 120.0, 400, False, True),
            ('marketing', 'strict', 200.0, 150, True, False)
        ]
        
        for content_type, strategy, time_ms, length, filtered, reviewed in test_operations:
            classification_result = MockClassificationResult(content_type, 0.7, strategy)
            filter_result = MockFilterResult(0.8, 0.75)
            
            self.monitor.record_filtering_operation(
                classification_result=classification_result,
                filter_result=filter_result,
                processing_time_ms=time_ms,
                content_length=length,
                url=f"https://{content_type}.example.com",
                was_filtered=filtered,
                review_recommended=reviewed
            )
        
        # Check aggregate statistics
        assert self.monitor.stats['total_operations'] == initial_total + len(test_operations)
        assert self.monitor.stats['content_type_counts']['marketing'] == 2
        assert self.monitor.stats['content_type_counts']['tutorial'] == 1
        assert self.monitor.stats['strategy_counts']['strict'] == 2
        assert self.monitor.stats['strategy_counts']['lenient'] == 1
        assert self.monitor.stats['filter_outcomes']['filtered'] == 2
        assert self.monitor.stats['filter_outcomes']['passed'] == 2
        assert self.monitor.stats['filter_outcomes']['reviewed'] == 1
    
    def test_real_time_metrics(self):
        """Test real-time metrics calculation"""
        # Record some recent operations
        current_time = time.time()
        
        # Add operations within the last 5 minutes
        for i in range(3):
            classification_result = MockClassificationResult('documentation', 0.6 + i*0.1, 'balanced')
            filter_result = MockFilterResult(0.7 + i*0.1, 0.8)
            
            self.monitor.record_filtering_operation(
                classification_result=classification_result,
                filter_result=filter_result,
                processing_time_ms=100.0 + i*10,
                content_length=300,
                url="https://docs.example.com",
                was_filtered=i == 2,  # Filter the last one
                review_recommended=False
            )
        
        real_time_metrics = self.monitor.get_real_time_metrics()
        
        assert real_time_metrics['status'] == 'active'
        assert real_time_metrics['time_window_minutes'] == 5
        assert real_time_metrics['metrics']['total_processed'] == 3
        assert real_time_metrics['metrics']['filter_rate'] == 1/3  # One filtered out of three
        assert real_time_metrics['metrics']['avg_processing_time_ms'] == 110.0  # (100+110+120)/3
        assert real_time_metrics['metrics']['operations_per_minute'] == 3/5  # 3 operations in 5 minutes
    
    def test_performance_summary(self):
        """Test performance summary generation"""
        # Record operations with different characteristics
        test_data = [
            ('marketing', 'strict', 50.0, True, False),
            ('tutorial', 'lenient', 75.0, False, False),
            ('documentation', 'balanced', 100.0, False, True),
            ('marketing', 'strict', 150.0, True, False),
            ('tutorial', 'lenient', 80.0, False, False)
        ]
        
        for content_type, strategy, time_ms, filtered, reviewed in test_data:
            classification_result = MockClassificationResult(content_type, 0.7, strategy)
            filter_result = MockFilterResult(0.8, 0.75)
            
            self.monitor.record_filtering_operation(
                classification_result=classification_result,
                filter_result=filter_result,
                processing_time_ms=time_ms,
                content_length=400,
                url=f"https://{content_type}.example.com",
                was_filtered=filtered,
                review_recommended=reviewed
            )
        
        summary = self.monitor.get_performance_summary(hours=24)
        
        assert summary['status'] == 'success'
        assert summary['time_period_hours'] == 24
        assert summary['summary']['total_processed'] == len(test_data)
        assert summary['summary']['total_filtered'] == 2  # Two marketing pages filtered
        assert summary['summary']['total_reviewed'] == 1  # One documentation reviewed
        assert summary['summary']['filter_rate'] == 2/5
        assert summary['summary']['review_rate'] == 1/5
        
        # Check content type analysis
        content_analysis = summary['content_type_analysis']
        assert content_analysis['marketing']['count'] == 2
        assert content_analysis['marketing']['filtered'] == 2
        assert content_analysis['marketing']['filter_rate'] == 1.0  # All marketing filtered
        assert content_analysis['tutorial']['count'] == 2
        assert content_analysis['tutorial']['filtered'] == 0
        assert content_analysis['tutorial']['effectiveness'] == 1.0  # None filtered
        
        # Check strategy analysis
        strategy_analysis = summary['strategy_analysis']
        assert strategy_analysis['strict']['count'] == 2
        assert strategy_analysis['strict']['filtered'] == 2
        assert strategy_analysis['lenient']['count'] == 2
        assert strategy_analysis['lenient']['filtered'] == 0
    
    def test_performance_alerts(self):
        """Test performance alerting system"""
        # Record operations with high processing times to trigger alerts
        for i in range(3):
            classification_result = MockClassificationResult('documentation', 0.2, 'balanced')  # Low confidence
            filter_result = MockFilterResult(0.8, 0.75)
            
            self.monitor.record_filtering_operation(
                classification_result=classification_result,
                filter_result=filter_result,
                processing_time_ms=1500.0,  # High processing time
                content_length=400,
                url="https://docs.example.com",
                was_filtered=False,
                review_recommended=True  # High review rate
            )
        
        alerts = self.monitor.get_performance_alerts()
        
        # Should have alerts for high processing time, low confidence, and high review rate
        alert_types = [alert['type'] for alert in alerts]
        assert 'high_processing_time' in alert_types
        assert 'low_classification_confidence' in alert_types
        assert 'high_review_rate' in alert_types
        
        # Check alert details
        processing_time_alert = next(alert for alert in alerts if alert['type'] == 'high_processing_time')
        assert processing_time_alert['severity'] == 'warning'
        assert processing_time_alert['current_value'] == 1500.0
        assert processing_time_alert['threshold'] == 1000.0
    
    def test_domain_analysis(self):
        """Test domain-based performance analysis"""
        # Record operations for different domains
        domains_data = [
            ('docs.example.com', [0.8, 0.9, 0.7], [100, 110, 90]),
            ('tutorial.example.com', [0.6, 0.7, 0.8], [80, 85, 75]),
            ('api.example.com', [0.9, 0.8, 0.9], [120, 115, 125])
        ]
        
        for domain, confidences, times in domains_data:
            for conf, time_ms in zip(confidences, times):
                classification_result = MockClassificationResult('documentation', conf, 'balanced')
                filter_result = MockFilterResult(0.8, 0.75)
                
                self.monitor.record_filtering_operation(
                    classification_result=classification_result,
                    filter_result=filter_result,
                    processing_time_ms=time_ms,
                    content_length=400,
                    url=f"https://{domain}/page",
                    was_filtered=False,
                    review_recommended=False
                )
        
        domain_analysis = self.monitor.get_domain_analysis(min_samples=3)
        
        # Should have analysis for all three domains
        assert len(domain_analysis) == 3
        
        # Check specific domain metrics
        docs_metrics = domain_analysis['docs.example.com']
        assert docs_metrics['sample_count'] == 3
        assert docs_metrics['avg_classification_confidence'] == 0.8  # (0.8+0.9+0.7)/3
        assert docs_metrics['avg_processing_time_ms'] == 100.0  # (100+110+90)/3
        
        api_metrics = domain_analysis['api.example.com']
        assert api_metrics['avg_classification_confidence'] == (0.9+0.8+0.9)/3
        assert api_metrics['avg_processing_time_ms'] == 120.0  # (120+115+125)/3
    
    def test_metrics_export(self):
        """Test metrics export functionality"""
        # Record some test data
        classification_result = MockClassificationResult('tutorial', 0.8, 'lenient')
        filter_result = MockFilterResult(0.9, 0.85)
        
        self.monitor.record_filtering_operation(
            classification_result=classification_result,
            filter_result=filter_result,
            processing_time_ms=100.0,
            content_length=500,
            url="https://tutorial.example.com",
            was_filtered=False,
            review_recommended=False
        )
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            success = self.monitor.export_metrics(temp_file.name, format='json')
            assert success
            
            # Read back and verify
            with open(temp_file.name, 'r') as f:
                exported_data = json.load(f)
            
            assert 'export_timestamp' in exported_data
            assert 'metrics_count' in exported_data
            assert 'metrics' in exported_data
            assert exported_data['metrics_count'] >= 1
            assert len(exported_data['metrics']) >= 1
            
            # Verify metric structure
            first_metric = exported_data['metrics'][0]
            assert 'content_type' in first_metric
            assert 'processing_time_ms' in first_metric
            assert 'url_domain' in first_metric
    
    def test_metrics_reset(self):
        """Test metrics reset functionality"""
        # Record some data
        classification_result = MockClassificationResult('documentation', 0.7, 'balanced')
        filter_result = MockFilterResult(0.8, 0.75)
        
        self.monitor.record_filtering_operation(
            classification_result=classification_result,
            filter_result=filter_result,
            processing_time_ms=100.0,
            content_length=400,
            url="https://docs.example.com",
            was_filtered=False,
            review_recommended=False
        )
        
        # Verify data exists
        assert len(self.monitor.metrics_history) > 0
        assert self.monitor.stats['total_operations'] > 0
        
        # Reset metrics
        self.monitor.reset_metrics()
        
        # Verify reset
        assert len(self.monitor.metrics_history) == 0
        assert len(self.monitor.performance_snapshots) == 0
        assert self.monitor.stats['total_operations'] == 0
        assert len(self.monitor.stats['content_type_counts']) == 0
    
    def test_snapshot_creation(self):
        """Test automatic performance snapshot creation"""
        # Set a very short snapshot interval for testing
        self.monitor.snapshot_interval = 1  # 1 second
        
        # Record some operations
        for i in range(3):
            classification_result = MockClassificationResult('documentation', 0.7, 'balanced')
            filter_result = MockFilterResult(0.8, 0.75)
            
            self.monitor.record_filtering_operation(
                classification_result=classification_result,
                filter_result=filter_result,
                processing_time_ms=100.0 + i*10,
                content_length=400,
                url="https://docs.example.com",
                was_filtered=i == 2,  # Filter the last one
                review_recommended=False
            )
        
        # Wait for snapshot interval to pass
        time.sleep(1.1)
        
        # Record another operation to trigger snapshot creation
        self.monitor.record_filtering_operation(
            classification_result=MockClassificationResult('tutorial', 0.8, 'lenient'),
            filter_result=MockFilterResult(0.9, 0.85),
            processing_time_ms=90.0,
            content_length=300,
            url="https://tutorial.example.com",
            was_filtered=False,
            review_recommended=False
        )
        
        # Should have at least one snapshot
        assert len(self.monitor.performance_snapshots) >= 1
        
        # Check snapshot content
        latest_snapshot = self.monitor.performance_snapshots[-1]
        assert isinstance(latest_snapshot, PerformanceSnapshot)
        assert latest_snapshot.total_processed >= 1
        assert latest_snapshot.total_filtered >= 0  # May be 0 if no content was actually filtered
        # Should have content types from the snapshot period  
        assert len(latest_snapshot.content_type_distribution) > 0
        assert len(latest_snapshot.strategy_distribution) > 0
    
    def test_no_data_scenarios(self):
        """Test behavior when no data is available"""
        # Test real-time metrics with no data
        real_time_metrics = self.monitor.get_real_time_metrics()
        assert real_time_metrics['status'] == 'no_data'
        
        # Test performance summary with no data
        summary = self.monitor.get_performance_summary(hours=24)
        assert summary['status'] == 'no_data'
        
        # Test alerts with no data
        alerts = self.monitor.get_performance_alerts()
        assert len(alerts) == 0
        
        # Test domain analysis with no data
        domain_analysis = self.monitor.get_domain_analysis()
        assert len(domain_analysis) == 0


class TestFilteringMetrics:
    """Test FilteringMetrics dataclass"""
    
    def test_metrics_creation(self):
        """Test creation of FilteringMetrics"""
        metrics = FilteringMetrics(
            timestamp=time.time(),
            content_type='tutorial',
            strategy='lenient',
            classification_confidence=0.8,
            filter_confidence=0.9,
            adaptive_confidence=0.85,
            was_filtered=False,
            review_recommended=False,
            processing_time_ms=150.0,
            content_length=500,
            url_domain='tutorial.example.com'
        )
        
        assert metrics.content_type == 'tutorial'
        assert metrics.strategy == 'lenient'
        assert metrics.classification_confidence == 0.8
        assert metrics.filter_confidence == 0.9
        assert metrics.adaptive_confidence == 0.85
        assert not metrics.was_filtered
        assert not metrics.review_recommended
        assert metrics.processing_time_ms == 150.0
        assert metrics.content_length == 500
        assert metrics.url_domain == 'tutorial.example.com'


class TestPerformanceSnapshot:
    """Test PerformanceSnapshot dataclass"""
    
    def test_snapshot_creation(self):
        """Test creation of PerformanceSnapshot"""
        snapshot = PerformanceSnapshot(
            start_time=time.time() - 3600,
            end_time=time.time(),
            total_processed=100,
            total_filtered=20,
            total_reviewed=5,
            avg_processing_time_ms=125.5,
            avg_classification_confidence=0.75,
            avg_filter_confidence=0.82,
            content_type_distribution={'documentation': 60, 'tutorial': 40},
            strategy_distribution={'balanced': 60, 'lenient': 40},
            filter_effectiveness={'balanced': 0.8, 'lenient': 0.9}
        )
        
        assert snapshot.total_processed == 100
        assert snapshot.total_filtered == 20
        assert snapshot.total_reviewed == 5
        assert snapshot.avg_processing_time_ms == 125.5
        assert snapshot.avg_classification_confidence == 0.75
        assert snapshot.avg_filter_confidence == 0.82
        assert snapshot.content_type_distribution['documentation'] == 60
        assert snapshot.strategy_distribution['balanced'] == 60
        assert snapshot.filter_effectiveness['balanced'] == 0.8


if __name__ == "__main__":
    pytest.main([__file__, "-v"])