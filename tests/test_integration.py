"""
Integration tests for hybrid RAG system.
"""
import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from src.rag_config import RAGConfiguration, get_rag_config, reload_config
from src.hybrid_scoring import get_default_scorer

class TestHybridRAGIntegration:
    """Integration tests for the complete hybrid RAG system."""
    
    @pytest.fixture
    def mock_supabase_client(self):
        """Create mock Supabase client."""
        client = Mock()
        
        # Mock vector search results
        client.rpc.return_value.execute.return_value.data = [
            {
                'id': 1,
                'url': 'https://example.com/doc1',
                'chunk_number': 0,
                'content': 'Python function for data processing',
                'metadata': {'section': 'functions'},
                'source_id': 'example.com',
                'similarity': 0.85
            },
            {
                'id': 2,
                'url': 'https://example.com/doc2', 
                'chunk_number': 0,
                'content': 'Machine learning model training',
                'metadata': {'section': 'ml'},
                'source_id': 'example.com',
                'similarity': 0.72
            }
        ]
        
        # Mock keyword search results
        client.from_.return_value.select.return_value.ilike.return_value.limit.return_value.execute.return_value.data = [
            {
                'id': 1,
                'url': 'https://example.com/doc1',
                'chunk_number': 0,
                'content': 'Python function for data processing',
                'metadata': {'section': 'functions'},
                'source_id': 'example.com'
            },
            {
                'id': 3,
                'url': 'https://example.com/doc3',
                'chunk_number': 0,
                'content': 'Python tutorial for beginners',
                'metadata': {'section': 'tutorials'},
                'source_id': 'example.com'
            }
        ]
        
        return client
    
    @pytest.fixture
    def mock_reranking_model(self):
        """Create mock reranking model."""
        model = Mock()
        model.predict.return_value = [1.2, -0.3, 0.8]  # Rerank scores
        return model
    
    @pytest.fixture
    def mock_context(self, mock_supabase_client, mock_reranking_model):
        """Create mock MCP context."""
        context = Mock()
        context.request_context.lifespan_context.supabase_client = mock_supabase_client
        context.request_context.lifespan_context.reranking_model = mock_reranking_model
        return context
    
    def test_hybrid_scoring_integration(self, mock_supabase_client):
        """Test integration between hybrid search and scoring."""
        scorer = get_default_scorer()
        
        # Simulate vector search results
        vector_results = [
            {'id': 1, 'similarity': 0.85, 'content': 'Python function'},
            {'id': 2, 'similarity': 0.72, 'content': 'ML training'}
        ]
        
        # Simulate keyword search results
        keyword_results = [
            {'id': 1, 'content': 'Python function'},  # Overlap with vector
            {'id': 3, 'content': 'Python tutorial'}
        ]
        
        # Merge using hybrid scorer
        merged = scorer.merge_hybrid_results(vector_results, keyword_results, 5)
        
        # Verify results structure
        assert len(merged) >= 2
        assert merged[0]['id'] == 1  # Should be top due to dual match
        assert merged[0]['appears_in_both'] is True
        assert merged[0]['match_type'] == 'hybrid'
        assert 'final_score' in merged[0]
        
        # Verify scoring progression
        scores = [r['final_score'] for r in merged]
        assert scores == sorted(scores, reverse=True)
    
    @patch.dict('os.environ', {
        'USE_HYBRID_SEARCH': 'true',
        'USE_RERANKING': 'true',
        'VECTOR_WEIGHT': '0.6',
        'KEYWORD_WEIGHT': '0.3',
        'RERANK_WEIGHT': '0.1'
    })
    def test_config_integration_with_scoring(self):
        """Test configuration integration with scoring system."""
        # Reload config to pick up environment variables
        config = reload_config()
        
        assert config.use_hybrid_search is True
        assert config.use_reranking is True
        assert config.vector_weight == 0.6
        assert config.keyword_weight == 0.3
        assert config.rerank_weight == 0.1
        
        # Test scorer uses config values
        from src.hybrid_scoring import ScoringConfig, HybridScorer
        scoring_config = ScoringConfig(
            vector_weight=config.vector_weight,
            keyword_weight=config.keyword_weight,
            rerank_weight=config.rerank_weight
        )
        scorer = HybridScorer(scoring_config)
        
        # Test score combination
        score = scorer.combine_scores(0.8, True, 1.0, False)
        expected = 0.6 * 0.8 + 0.3 * 1.0 + 0.1 * scorer.normalize_rerank_score(1.0)
        assert abs(score - expected) < 0.01
    
    @pytest.mark.asyncio
    async def test_perform_rag_query_integration(self, mock_context):
        """Test perform_rag_query with hybrid scoring."""
        # Import here to avoid circular imports
        from src.ay_rag_mcp import perform_rag_query
        
        with patch('src.ay_rag_mcp.search_documents') as mock_search, \
             patch('src.ay_rag_mcp.get_rag_config') as mock_get_config, \
             patch('os.getenv') as mock_getenv:
            
            # Configure environment
            mock_getenv.side_effect = lambda key, default='false': {
                'USE_HYBRID_SEARCH': 'true',
                'USE_RERANKING': 'true'
            }.get(key, default)
            
            # Configure mock search
            mock_search.return_value = [
                {
                    'id': 1,
                    'url': 'https://example.com/doc1',
                    'content': 'Python function for data processing',
                    'similarity': 0.85
                }
            ]
            
            # Configure mock config
            mock_config = RAGConfiguration(use_hybrid_search=True, use_reranking=True)
            mock_get_config.return_value = mock_config
            
            # Execute query
            result = await perform_rag_query(mock_context, "Python function", None, 5)
            result_data = json.loads(result)
            
            # Verify response structure
            assert result_data['success'] is True
            assert result_data['search_mode'] == 'hybrid'
            assert result_data['reranking_applied'] is True
            assert len(result_data['results']) >= 1
            
            # Verify enhanced result format
            first_result = result_data['results'][0]
            assert 'final_score' in first_result
            assert 'match_type' in first_result
    
    def test_contextual_embeddings_config_integration(self):
        """Test contextual embeddings configuration integration."""
        with patch.dict('os.environ', {'USE_CONTEXTUAL_EMBEDDINGS': 'true'}):
            config = reload_config()
            assert config.use_contextual_embeddings is True
            
            # Test processor selection
            from src.contextual_embeddings import get_contextual_processor
            processor = get_contextual_processor(use_llm=True)
            
            # Should attempt to create LLM processor (may fall back to simple)
            assert processor is not None
    
    @pytest.mark.asyncio 
    async def test_end_to_end_query_flow(self, mock_context):
        """Test complete end-to-end query processing flow."""
        # This tests the full pipeline: query -> hybrid search -> scoring -> reranking -> response
        
        with patch.multiple(
            'src.ay_rag_mcp',
            search_documents=Mock(return_value=[
                {'id': 1, 'similarity': 0.9, 'content': 'Python data processing'},
                {'id': 2, 'similarity': 0.7, 'content': 'Machine learning basics'}
            ]),
            get_rag_config=Mock(return_value=RAGConfiguration(
                use_hybrid_search=True,
                use_reranking=True,
                vector_weight=0.5,
                keyword_weight=0.3,
                rerank_weight=0.2
            ))
        ), patch('os.getenv') as mock_getenv:
            
            # Configure environment
            mock_getenv.side_effect = lambda key, default='false': {
                'USE_HYBRID_SEARCH': 'true',
                'USE_RERANKING': 'true'
            }.get(key, default)
            
            # Mock keyword search in Supabase client
            mock_context.request_context.lifespan_context.supabase_client.from_.return_value.select.return_value.ilike.return_value.limit.return_value.execute.return_value.data = [
                {'id': 1, 'content': 'Python data processing'},  # Overlap
                {'id': 3, 'content': 'Python tutorial'}
            ]
            
            from src.ay_rag_mcp import perform_rag_query
            
            # Execute full query
            result = await perform_rag_query(mock_context, "Python data", None, 5)
            result_data = json.loads(result)
            
            # Verify complete pipeline execution
            assert result_data['success'] is True
            assert result_data['search_mode'] == 'hybrid'
            assert result_data['reranking_applied'] is True
            
            # Verify enhanced scoring information
            if result_data['results']:
                first_result = result_data['results'][0]
                assert 'similarity' in first_result
                assert 'rerank_score' in first_result
                assert 'final_score' in first_result
                assert 'match_type' in first_result

class TestConfigurationValidation:
    """Test configuration validation in integrated scenarios."""
    
    def test_invalid_config_warnings(self):
        """Test that invalid configurations produce warnings."""
        with patch.dict('os.environ', {
            'VECTOR_WEIGHT': '0.8',
            'KEYWORD_WEIGHT': '0.4',
            'RERANK_WEIGHT': '0.3',  # Sum > 1.0
            'MAX_CONCURRENT_SEARCHES': '20'  # High value
        }):
            config = reload_config()
            warnings = config.validate_configuration()
            
            assert len(warnings) > 0
            assert any('weights sum' in w for w in warnings)
            assert any('concurrent searches' in w for w in warnings)
    
    def test_dependency_validation(self):
        """Test dependency validation between features."""
        with patch.dict('os.environ', {
            'USE_RERANKING': 'true',
            'USE_HYBRID_SEARCH': 'false'  # Missing dependency
        }):
            config = reload_config()
            warnings = config.validate_configuration()
            
            assert any('reranking' in w and 'hybrid search' in w for w in warnings)

class TestPerformanceIntegration:
    """Test performance aspects of integrated system."""
    
    def test_large_result_set_handling(self):
        """Test system behavior with large result sets."""
        scorer = get_default_scorer()
        
        # Generate large result sets
        vector_results = [
            {'id': f'v{i}', 'similarity': 0.9 - i*0.001, 'content': f'vector content {i}'}
            for i in range(200)
        ]
        
        keyword_results = [
            {'id': f'k{i}', 'content': f'keyword content {i}'}
            for i in range(150)
        ]
        
        # Test merging performance
        merged = scorer.merge_hybrid_results(vector_results, keyword_results, 50)
        
        assert len(merged) == 50
        assert all('final_score' in r for r in merged)
        
        # Verify results are properly sorted
        scores = [r['final_score'] for r in merged]
        assert scores == sorted(scores, reverse=True)
    
    def test_concurrent_processing_simulation(self):
        """Test concurrent processing capabilities."""
        # This would test the concurrent processing features
        # For now, just verify the configuration supports it
        
        with patch.dict('os.environ', {
            'MAX_CONCURRENT_SEARCHES': '5',
            'ENABLE_PARALLEL_PROCESSING': 'true'
        }):
            config = reload_config()
            
            assert config.max_concurrent_searches == 5
            assert config.enable_parallel_processing is True

class TestErrorHandling:
    """Test error handling in integrated scenarios."""
    
    @pytest.mark.asyncio
    async def test_reranking_model_failure(self, mock_context):
        """Test graceful handling of reranking model failures."""
        # Make reranking model throw an exception
        mock_context.request_context.lifespan_context.reranking_model.predict.side_effect = Exception("Model error")
        
        with patch.multiple(
            'src.ay_rag_mcp',
            search_documents=Mock(return_value=[
                {'id': 1, 'similarity': 0.8, 'content': 'test content'}
            ]),
            get_rag_config=Mock(return_value=RAGConfiguration(use_reranking=True))
        ), patch('os.getenv', return_value='true'):
            
            from src.ay_rag_mcp import perform_rag_query
            
            # Should handle reranking failure gracefully
            result = await perform_rag_query(mock_context, "test query", None, 5)
            result_data = json.loads(result)
            
            # Should still return results even if reranking fails
            assert result_data['success'] is True
    
    def test_missing_environment_variables(self):
        """Test handling of missing environment variables."""
        with patch.dict('os.environ', {}, clear=True):
            config = RAGConfiguration.from_environment()
            
            # Should use defaults when env vars missing
            assert config.use_hybrid_search is True  # default
            assert config.vector_weight == 0.5  # default
            assert config.max_concurrent_searches == 3  # default

if __name__ == "__main__":
    pytest.main([__file__, "-v"])