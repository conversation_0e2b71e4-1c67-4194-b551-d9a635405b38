"""
Unit tests for circuit breaker functionality

Comprehensive tests for the circuit breaker pattern implementation
"""

import pytest
import time
from unittest.mock import Mock, patch, AsyncMock

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from circuit_breaker import (
    SimpleCircuitBreaker,
    CircuitState,
    get_circuit_breaker,
    CircuitBreaker  # Alias
)


@pytest.mark.unit
class TestSimpleCircuitBreaker:
    """Test the SimpleCircuitBreaker implementation"""
    
    def test_initialization(self):
        """Test circuit breaker initialization"""
        cb = SimpleCircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60,
            success_threshold=3
        )
        
        assert cb.failure_threshold == 5
        assert cb.recovery_timeout == 60
        assert cb.success_threshold == 3
        assert cb.failure_count == 0
        assert cb.success_count == 0
        assert cb.state == CircuitState.CLOSED
        assert cb.last_failure_time is None
    
    def test_initial_state_allows_execution(self):
        """Test that initial state allows execution"""
        cb = SimpleCircuitBreaker()
        assert cb.can_execute() is True
    
    def test_failure_threshold_opens_circuit(self):
        """Test that reaching failure threshold opens circuit"""
        cb = SimpleCircuitBreaker(failure_threshold=3)
        
        # Record failures up to threshold - 1
        for i in range(2):
            cb.record_failure()
            assert cb.state == CircuitState.CLOSED
            assert cb.can_execute() is True
        
        # Final failure should open circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        assert cb.can_execute() is False
    
    def test_success_resets_failure_count_in_closed_state(self):
        """Test that success resets failure count when circuit is closed"""
        cb = SimpleCircuitBreaker(failure_threshold=3)
        
        # Record some failures
        cb.record_failure()
        cb.record_failure()
        assert cb.failure_count == 2
        
        # Success should reset failure count
        cb.record_success()
        assert cb.failure_count == 0
        assert cb.state == CircuitState.CLOSED
    
    def test_half_open_state_transition(self):
        """Test transition to half-open state after timeout"""
        cb = SimpleCircuitBreaker(failure_threshold=1, recovery_timeout=0.1)
        
        # Open circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        assert cb.can_execute() is False
        
        # Wait for recovery timeout
        time.sleep(0.2)
        
        # Should transition to half-open and allow one try
        assert cb.can_execute() is True
        # State should be half-open after the check
        # (Implementation detail may vary)
    
    def test_half_open_success_closes_circuit(self):
        """Test that successes in half-open state eventually close circuit"""
        cb = SimpleCircuitBreaker(
            failure_threshold=1,
            recovery_timeout=0.1,
            success_threshold=2
        )
        
        # Open circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        
        # Wait and transition to half-open
        time.sleep(0.2)
        cb.can_execute()  # Trigger state check
        
        # Record required successes
        cb.record_success()
        if cb.state == CircuitState.HALF_OPEN:
            cb.record_success()  # Should close circuit
            assert cb.state == CircuitState.CLOSED
    
    def test_half_open_failure_reopens_circuit(self):
        """Test that failure in half-open state reopens circuit"""
        cb = SimpleCircuitBreaker(failure_threshold=1, recovery_timeout=0.1)
        
        # Open circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        
        # Wait and transition to half-open
        time.sleep(0.2)
        cb.can_execute()
        
        # Failure should reopen circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        assert cb.can_execute() is False
    
    @pytest.mark.asyncio
    async def test_call_wrapper_functionality(self):
        """Test the call wrapper functionality"""
        cb = SimpleCircuitBreaker(failure_threshold=2)
        call_count = 0
        
        async def test_function(should_fail=False):
            nonlocal call_count
            call_count += 1
            if should_fail:
                raise Exception("Test failure")
            return "success"
        
        # Successful calls
        result = await cb.call(test_function)
        assert result == "success"
        assert call_count == 1
        assert cb.state == CircuitState.CLOSED
        
        # Failing calls
        try:
            await cb.call(test_function, should_fail=True)
        except Exception:
            pass
        
        assert cb.failure_count == 1
        assert cb.state == CircuitState.CLOSED  # Still closed, under threshold
        
        # Another failure should open circuit
        try:
            await cb.call(test_function, should_fail=True)
        except Exception:
            pass
        
        assert cb.state == CircuitState.OPEN
        
        # Further calls should be blocked
        with pytest.raises(Exception, match="Circuit breaker is OPEN"):
            await cb.call(test_function)
    
    @pytest.mark.asyncio
    async def test_async_call_wrapper(self):
        """Test async call wrapper functionality"""
        cb = SimpleCircuitBreaker(failure_threshold=1)
        
        async def async_function(should_fail=False):
            if should_fail:
                raise Exception("Async test failure")
            return "async success"
        
        # Test successful async call
        result = await cb.call(async_function)
        assert result == "async success"
        assert cb.state == CircuitState.CLOSED
        
        # Test failing async call
        with pytest.raises(Exception):
            await cb.call(async_function, should_fail=True)
        
        assert cb.state == CircuitState.OPEN


@pytest.mark.unit
class TestCircuitState:
    """Test CircuitState enum"""
    
    def test_circuit_state_values(self):
        """Test circuit state enum values"""
        assert CircuitState.CLOSED.value == "closed"
        assert CircuitState.OPEN.value == "open"
        assert CircuitState.HALF_OPEN.value == "half_open"
    
    def test_circuit_state_comparison(self):
        """Test circuit state comparisons"""
        assert CircuitState.CLOSED == CircuitState.CLOSED
        assert CircuitState.CLOSED != CircuitState.OPEN
        assert CircuitState.OPEN != CircuitState.HALF_OPEN


@pytest.mark.unit
class TestCircuitBreakerFactory:
    """Test circuit breaker factory functions"""
    
    def test_get_circuit_breaker_creates_instance(self):
        """Test that get_circuit_breaker creates circuit breaker instance"""
        cb = get_circuit_breaker("test_service")
        
        assert isinstance(cb, SimpleCircuitBreaker)
        assert cb.failure_threshold == 5  # Default value
    
    def test_get_circuit_breaker_reuses_instance(self):
        """Test that get_circuit_breaker reuses existing instances"""
        cb1 = get_circuit_breaker("same_service")
        cb2 = get_circuit_breaker("same_service")
        
        assert cb1 is cb2  # Should be the same instance
    
    def test_get_circuit_breaker_with_custom_params(self):
        """Test circuit breaker creation with custom parameters"""
        cb = get_circuit_breaker(
            "custom_service",
            failure_threshold=10,
            recovery_timeout=120
        )
        
        assert cb.failure_threshold == 10
        assert cb.recovery_timeout == 120
    
    def test_different_services_get_different_breakers(self):
        """Test that different services get different circuit breakers"""
        cb1 = get_circuit_breaker("service_a")
        cb2 = get_circuit_breaker("service_b")
        
        assert cb1 is not cb2
        assert isinstance(cb1, SimpleCircuitBreaker)
        assert isinstance(cb2, SimpleCircuitBreaker)


@pytest.mark.unit
class TestCircuitBreakerAlias:
    """Test CircuitBreaker alias functionality"""
    
    def test_circuit_breaker_alias_works(self):
        """Test that CircuitBreaker alias works like SimpleCircuitBreaker"""
        cb = CircuitBreaker(failure_threshold=3)
        
        assert isinstance(cb, SimpleCircuitBreaker)
        assert cb.failure_threshold == 3
    
    def test_alias_maintains_functionality(self):
        """Test that alias maintains all functionality"""
        cb = CircuitBreaker()
        
        # Should have all the same methods
        assert hasattr(cb, 'can_execute')
        assert hasattr(cb, 'record_success')
        assert hasattr(cb, 'record_failure')
        assert hasattr(cb, 'call')
        
        # Should behave the same way
        assert cb.can_execute() is True
        assert cb.state == CircuitState.CLOSED


@pytest.mark.unit
class TestCircuitBreakerEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_zero_failure_threshold(self):
        """Test circuit breaker with zero failure threshold"""
        cb = SimpleCircuitBreaker(failure_threshold=0)
        
        # Should immediately open on first failure
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        assert cb.can_execute() is False
    
    def test_negative_values_handled(self):
        """Test circuit breaker handles negative configuration values"""
        # Should not crash with negative values
        cb = SimpleCircuitBreaker(
            failure_threshold=-1,
            recovery_timeout=-10
        )
        
        assert isinstance(cb, SimpleCircuitBreaker)
        # Behavior with negative values is implementation-specific
        # but should not crash
    
    def test_very_short_recovery_timeout(self):
        """Test very short recovery timeout"""
        cb = SimpleCircuitBreaker(failure_threshold=1, recovery_timeout=0.001)
        
        # Open circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        
        # Very short timeout should allow quick recovery
        time.sleep(0.01)
        assert cb.can_execute() is True
    
    def test_concurrent_access_safety(self):
        """Test circuit breaker thread safety (basic test)"""
        cb = SimpleCircuitBreaker()
        
        # Basic test - should not crash with concurrent access
        # (Full thread safety testing would require more complex setup)
        for _ in range(10):
            cb.can_execute()
            cb.record_success()


@pytest.mark.unit
class TestCircuitBreakerMetrics:
    """Test circuit breaker metrics and monitoring"""
    
    def test_failure_count_tracking(self):
        """Test that failure count is tracked correctly"""
        cb = SimpleCircuitBreaker(failure_threshold=5)
        
        assert cb.failure_count == 0
        
        cb.record_failure()
        assert cb.failure_count == 1
        
        cb.record_failure()
        assert cb.failure_count == 2
        
        # Success should reset count
        cb.record_success()
        assert cb.failure_count == 0
    
    def test_success_count_in_half_open(self):
        """Test success count tracking in half-open state"""
        cb = SimpleCircuitBreaker(
            failure_threshold=1,
            recovery_timeout=0.1,
            success_threshold=3
        )
        
        # Open circuit
        cb.record_failure()
        assert cb.state == CircuitState.OPEN
        
        # Wait and manually trigger half-open state (simulate state transition)
        time.sleep(0.2)
        if cb._should_attempt_reset():
            cb.state = CircuitState.HALF_OPEN
        
        # Track successes in half-open state
        initial_success_count = cb.success_count
        cb.record_success()
        # In half-open state, success count should increase
        if cb.state == CircuitState.HALF_OPEN:
            assert cb.success_count == initial_success_count + 1
    
    def test_last_failure_time_tracking(self):
        """Test that last failure time is tracked"""
        cb = SimpleCircuitBreaker()
        
        assert cb.last_failure_time is None
        
        before_failure = time.time()
        cb.record_failure()
        after_failure = time.time()
        
        assert cb.last_failure_time is not None
        assert before_failure <= cb.last_failure_time <= after_failure


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "unit"])