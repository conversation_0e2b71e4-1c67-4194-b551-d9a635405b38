"""
Unit tests for enhanced extraction functionality

Comprehensive tests for the enhanced code extraction and relevance search system
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

try:
    from enhanced_extraction import (
        EnhancedCodeExtractor,
        RelevanceSearchEnhancer,
        CodeBlock,
        extract_enhanced_code_examples,
        enhance_search_query,
        calculate_relevance_score
    )
    ENHANCED_EXTRACTION_AVAILABLE = True
except ImportError:
    ENHANCED_EXTRACTION_AVAILABLE = False


@pytest.mark.skipif(not ENHANCED_EXTRACTION_AVAILABLE, reason="Enhanced extraction module not available")
@pytest.mark.unit
class TestEnhancedCodeExtractor:
    """Test the EnhancedCodeExtractor class"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.extractor = EnhancedCodeExtractor()
    
    def test_initialization(self):
        """Test extractor initialization"""
        assert self.extractor is not None
        assert hasattr(self.extractor, 'language_patterns')
    
    def test_python_function_detection(self):
        """Test Python function extraction"""
        content = '''
        ```python
        def async_crawl_website(url: str, max_depth: int = 3):
            """Asynchronously crawl website with depth control"""
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url)
                return result.markdown
        
        class RAGSystem:
            def __init__(self, embeddings_model: str):
                self.model = embeddings_model
        ```
        '''
        
        blocks = extract_enhanced_code_examples(content, "test.md", 50)
        
        assert len(blocks) >= 1
        python_blocks = [b for b in blocks if b.language == 'python']
        assert len(python_blocks) >= 1
        
        # Check function extraction - collect all function names
        all_functions = []
        for block in python_blocks:
            all_functions.extend(block.function_names or [])
        
        assert 'async_crawl_website' in all_functions
    
    def test_javascript_function_detection(self):
        """Test JavaScript function extraction"""
        content = '''
        ```javascript
        async function performRAGQuery(query, options = {}) {
            const { maxResults = 10, threshold = 0.7 } = options;
            
            try {
                const embedding = await createEmbedding(query);
                const results = await vectorSearch(embedding, maxResults);
                return results.filter(r => r.similarity > threshold);
            } catch (error) {
                console.error('RAG query failed:', error);
                return [];
            }
        }
        ```
        '''
        
        blocks = extract_enhanced_code_examples(content, "test.md", 50)
        
        assert len(blocks) >= 1
        js_blocks = [b for b in blocks if b.language in ['javascript', 'typescript']]
        assert len(js_blocks) >= 1
        
        # Check function extraction
        for block in js_blocks:
            if block.function_names:
                assert 'performRAGQuery' in block.function_names
    
    def test_language_confidence_scoring(self):
        """Test language detection confidence"""
        test_cases = [
            ("```python\ndef test(): pass\n```", "python", 0.8),
            ("```javascript\nfunction test() {}\n```", "javascript", 0.8),
            ("```typescript\nconst test: string = 'hello';\n```", "typescript", 0.8),
            ("```\nsome generic code\n```", "", 0.3),  # Low confidence for generic
        ]
        
        for content, expected_lang, min_confidence in test_cases:
            blocks = extract_enhanced_code_examples(content, "test.md", 10)
            
            if blocks:
                if expected_lang:
                    assert any(b.language == expected_lang for b in blocks)
                    confident_blocks = [b for b in blocks if b.confidence >= min_confidence]
                    assert len(confident_blocks) >= 1
    
    def test_complexity_scoring(self):
        """Test code complexity scoring"""
        simple_code = '''
        ```python
        def hello():
            print("hello")
        ```
        '''
        
        complex_code = '''
        ```python
        async def complex_function(data: Dict[str, Any]) -> Optional[List[Dict]]:
            """Complex function with multiple operations"""
            try:
                results = []
                for item in data.get('items', []):
                    if item.get('active', False):
                        processed = await process_item(item)
                        if processed:
                            results.append({
                                'id': processed.id,
                                'data': processed.serialize(),
                                'meta': {'processed': True}
                            })
                return results if results else None
            except Exception as e:
                logger.error(f"Processing failed: {e}")
                return None
        ```
        '''
        
        simple_blocks = extract_enhanced_code_examples(simple_code, "simple.md", 10)
        complex_blocks = extract_enhanced_code_examples(complex_code, "complex.md", 10)
        
        if simple_blocks and complex_blocks:
            simple_score = simple_blocks[0].complexity_score
            complex_score = complex_blocks[0].complexity_score
            assert complex_score > simple_score
    
    def test_min_length_filtering(self):
        """Test minimum length filtering"""
        content = '''
        ```python
        # Short code
        x = 1
        ```
        
        ```python
        # Longer code that should be included
        def process_data(data: List[str]) -> Dict[str, int]:
            """Process data and return counts"""
            counts = {}
            for item in data:
                if item.strip():
                    counts[item] = counts.get(item, 0) + 1
            return counts
        ```
        '''
        
        # Test with high minimum length
        blocks = extract_enhanced_code_examples(content, "test.md", 200)
        
        # Should filter out short code blocks
        for block in blocks:
            assert len(block.content) >= 200
    
    def test_function_name_extraction(self):
        """Test function name extraction across languages"""
        test_cases = [
            ("```python\ndef test_func():\n    pass\n```", ["test_func"]),
            ("```python\nclass TestClass:\n    def method(self):\n        pass\n```", ["method"]),
            ("```javascript\nfunction testFunc() {}\n```", ["testFunc"]),
            ("```javascript\nconst testFunc = () => {}\n```", ["testFunc"]),
            ("```typescript\nfunction testFunc(): void {}\n```", ["testFunc"]),
        ]
        
        for content, expected_functions in test_cases:
            blocks = extract_enhanced_code_examples(content, "test.md", 10)
            
            if blocks and blocks[0].function_names:
                for expected_func in expected_functions:
                    assert expected_func in blocks[0].function_names


@pytest.mark.skipif(not ENHANCED_EXTRACTION_AVAILABLE, reason="Enhanced extraction module not available")
@pytest.mark.unit
class TestRelevanceSearchEnhancer:
    """Test the RelevanceSearchEnhancer class"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.enhancer = RelevanceSearchEnhancer()
    
    def test_initialization(self):
        """Test enhancer initialization"""
        assert self.enhancer is not None
    
    def test_query_type_classification(self):
        """Test query type classification"""
        test_cases = [
            ("how to implement async functions in python", "implementation_focused"),
            ("react useEffect hook examples", "code_focused"),
            ("what is machine learning", "concept_focused"),
            ("database connection pooling", "code_focused"),  # Contains 'connection' which is code-related
            ("python async database", "code_focused"),
        ]
        
        for query, expected_type in test_cases:
            enhanced = enhance_search_query(query, "test context")
            
            # Should classify query type
            assert 'query_type' in enhanced
            if expected_type:
                assert enhanced['query_type'] == expected_type
    
    def test_query_enhancement(self):
        """Test query enhancement functionality"""
        original_query = "python async functions"
        
        enhanced = enhance_search_query(original_query, "code search")
        
        # Should have required fields
        assert 'enhanced_query' in enhanced
        assert 'original_query' in enhanced
        assert 'boost_terms' in enhanced
        assert 'query_type' in enhanced
        
        # Should have original query
        assert enhanced['original_query'] == original_query
        assert isinstance(enhanced['boost_terms'], list)
    
    def test_context_integration(self):
        """Test context integration in query enhancement"""
        query = "database connections"
        
        web_context = enhance_search_query(query, "web search")
        code_context = enhance_search_query(query, "code search")
        
        # Context should influence query type or boost terms (implementation may vary)
        assert web_context['query_type'] == code_context['query_type'] or web_context['boost_terms'] != code_context['boost_terms']


@pytest.mark.skipif(not ENHANCED_EXTRACTION_AVAILABLE, reason="Enhanced extraction module not available")
@pytest.mark.unit
class TestRelevanceScoring:
    """Test relevance scoring functionality"""
    
    def test_basic_relevance_scoring(self):
        """Test basic relevance calculation"""
        query = "python async database connection"
        
        high_relevance_doc = "Python asyncio with SQLAlchemy for database connections"
        low_relevance_doc = "JavaScript frontend validation techniques"
        
        high_score = calculate_relevance_score(query, high_relevance_doc, {})
        low_score = calculate_relevance_score(query, low_relevance_doc, {})
        
        assert high_score > low_score
        assert 0 <= high_score <= 1
        assert 0 <= low_score <= 1
    
    def test_metadata_influence(self):
        """Test metadata influence on relevance scoring"""
        query = "python functions"
        content = "Python function definitions and usage"
        
        code_metadata = {'content_type': 'code', 'language': 'python'}
        doc_metadata = {'content_type': 'documentation'}
        
        code_score = calculate_relevance_score(query, content, code_metadata)
        doc_score = calculate_relevance_score(query, content, doc_metadata)
        
        # Code content should score higher for code-related queries
        assert code_score >= doc_score
    
    def test_keyword_matching(self):
        """Test keyword-based relevance scoring"""
        query = "database connection pool"
        
        exact_match = "Database connection pooling in Python"
        partial_match = "Database connections and queries"
        no_match = "Frontend user interface design"
        
        exact_score = calculate_relevance_score(query, exact_match, {})
        partial_score = calculate_relevance_score(query, partial_match, {})
        no_match_score = calculate_relevance_score(query, no_match, {})
        
        assert exact_score > partial_score > no_match_score


@pytest.mark.skipif(not ENHANCED_EXTRACTION_AVAILABLE, reason="Enhanced extraction module not available")
@pytest.mark.unit
class TestCodeBlock:
    """Test CodeBlock data structure"""
    
    def test_code_block_creation(self):
        """Test CodeBlock instantiation"""
        block = CodeBlock(
            content="def test(): pass",
            language="python",
            confidence=0.9,
            context="Test function",
            function_names=["test"],
            complexity_score=0.2
        )
        
        assert block.content == "def test(): pass"
        assert block.language == "python"
        assert block.confidence == 0.9
        assert "test" in block.function_names
        assert block.complexity_score == 0.2
    
    def test_code_block_attributes(self):
        """Test CodeBlock required attributes"""
        block = CodeBlock(
            content="console.log('test')",
            language="javascript",
            confidence=0.8,
            context="test context"
        )
        
        assert hasattr(block, 'content')
        assert hasattr(block, 'language')
        assert hasattr(block, 'confidence')
        assert hasattr(block, 'context')
        assert hasattr(block, 'function_names')
        assert hasattr(block, 'complexity_score')


@pytest.mark.skipif(not ENHANCED_EXTRACTION_AVAILABLE, reason="Enhanced extraction module not available")
@pytest.mark.unit
class TestIntegrationWorkflows:
    """Test integration between components"""
    
    def test_end_to_end_extraction_workflow(self):
        """Test complete extraction workflow"""
        markdown_content = '''
        # Database Connections Example
        
        Here's how to handle async database connections:
        
        ```python
        async def get_database_connection():
            """Get async database connection"""
            pool = await asyncpg.create_pool(
                host='localhost',
                database='mydb',
                user='user',
                password='pass'
            )
            return pool
        
        async def query_database(pool, query: str):
            """Execute database query"""
            async with pool.acquire() as conn:
                return await conn.fetch(query)
        ```
        
        And here's the JavaScript equivalent:
        
        ```javascript
        const { Client } = require('pg');
        
        async function connectDatabase() {
            const client = new Client({
                host: 'localhost',
                database: 'mydb',
                user: 'user',
                password: 'pass'
            });
            await client.connect();
            return client;
        }
        ```
        '''
        
        # Extract code blocks
        blocks = extract_enhanced_code_examples(markdown_content, "db_example.md", 50)
        
        assert len(blocks) >= 2  # Should extract both Python and JavaScript
        
        # Verify language detection
        languages = {block.language for block in blocks}
        assert 'python' in languages
        assert 'javascript' in languages
        
        # Verify function extraction
        all_functions = []
        for block in blocks:
            if block.function_names:
                all_functions.extend(block.function_names)
        
        assert len(all_functions) >= 2
    
    def test_search_enhancement_with_code_context(self):
        """Test search enhancement for code-focused queries"""
        query = "async database connections"
        
        enhanced = enhance_search_query(query, "code repository search")
        
        # Should enhance for code context
        assert enhanced['query_type'] in ['code_focused', 'implementation_focused']
        assert 'boost_terms' in enhanced
        
        # Should include relevant technical terms
        enhanced_text = enhanced['enhanced_query'].lower()
        assert any(term in enhanced_text for term in ['async', 'database', 'connection'])


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "unit"])