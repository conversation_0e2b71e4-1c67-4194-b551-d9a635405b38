"""
Unit tests for utility functions

Comprehensive tests for core utility functions used across the MCP server
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import json
from typing import List, Dict, Any

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

try:
    from utils import (
        chunk_content,
        extract_metadata_from_content,
        create_embeddings,
        batch_process_embeddings,
        calculate_similarity,
        get_supabase_client,
        safe_json_loads,
        retry_with_backoff,
        validate_embedding_dimension,
        normalize_url,
        extract_domain,
        format_file_size,
        calculate_content_hash
    )
    UTILS_AVAILABLE = True
except ImportError as e:
    UTILS_AVAILABLE = False
    print(f"Utils import error: {e}")


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestContentProcessing:
    """Test content processing functions"""
    
    def test_chunk_content_basic(self):
        """Test basic content chunking"""
        content = "This is a test content that should be chunked into smaller pieces for processing."
        chunks = chunk_content(content, max_chunk_size=20)
        
        assert isinstance(chunks, list)
        assert len(chunks) > 1
        for chunk in chunks:
            assert len(chunk) <= 25  # Allow some overlap
    
    def test_chunk_content_preserves_code_blocks(self):
        """Test that code blocks are preserved during chunking"""
        content = '''
        Here is some text.
        
        ```python
        def example_function():
            return "This should stay together"
        ```
        
        More text here.
        '''
        
        chunks = chunk_content(content, max_chunk_size=50)
        
        # Code block should be preserved in one chunk
        code_chunk = None
        for chunk in chunks:
            if "def example_function" in chunk:
                code_chunk = chunk
                break
        
        assert code_chunk is not None
        assert "```python" in code_chunk
        assert "```" in code_chunk  # Should have closing backticks
    
    def test_metadata_extraction(self):
        """Test metadata extraction from content"""
        content = '''
        # Document Title
        
        This is a markdown document with various elements.
        
        ## Section 1
        
        Some content here.
        
        ```python
        def test():
            pass
        ```
        '''
        
        metadata = extract_metadata_from_content(content, "test.md")
        
        assert isinstance(metadata, dict)
        assert 'title' in metadata or 'headers' in metadata
        assert 'word_count' in metadata
        assert 'code_blocks' in metadata
        assert metadata['word_count'] > 0
        assert metadata['code_blocks'] >= 1
    
    def test_content_hash_calculation(self):
        """Test content hash calculation for deduplication"""
        content1 = "This is test content"
        content2 = "This is test content"  # Same content
        content3 = "This is different content"
        
        hash1 = calculate_content_hash(content1)
        hash2 = calculate_content_hash(content2)
        hash3 = calculate_content_hash(content3)
        
        assert hash1 == hash2  # Same content should have same hash
        assert hash1 != hash3  # Different content should have different hash
        assert isinstance(hash1, str)
        assert len(hash1) > 0


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestEmbeddingFunctions:
    """Test embedding-related functions"""
    
    @pytest.mark.asyncio
    async def test_create_embeddings_mock(self):
        """Test embedding creation with mocked API"""
        texts = ["This is test text", "Another piece of text"]
        
        with patch('utils.get_default_llm_client') as mock_client:
            mock_client.return_value.embeddings.create.return_value.data = [
                Mock(embedding=[0.1, 0.2, 0.3]),
                Mock(embedding=[0.4, 0.5, 0.6])
            ]
            
            embeddings = await create_embeddings(texts)
            
            assert isinstance(embeddings, list)
            assert len(embeddings) == 2
            assert all(isinstance(emb, list) for emb in embeddings)
    
    @pytest.mark.asyncio
    async def test_batch_process_embeddings(self):
        """Test batch processing of embeddings"""
        texts = [f"Text chunk {i}" for i in range(10)]
        
        with patch('utils.create_embeddings') as mock_create:
            mock_create.return_value = [[0.1] * 1536] * len(texts)
            
            results = await batch_process_embeddings(texts, batch_size=3)
            
            assert len(results) == len(texts)
            assert all(isinstance(emb, list) for emb in results)
    
    def test_embedding_dimension_validation(self):
        """Test embedding dimension validation"""
        valid_embedding = [0.1] * 1536  # OpenAI standard
        invalid_embedding = [0.1] * 512  # Wrong dimension
        
        assert validate_embedding_dimension(valid_embedding) is True
        assert validate_embedding_dimension(invalid_embedding) is False
    
    def test_similarity_calculation(self):
        """Test similarity calculation between embeddings"""
        embedding1 = [1.0, 0.0, 0.0]
        embedding2 = [1.0, 0.0, 0.0]  # Same as embedding1
        embedding3 = [0.0, 1.0, 0.0]  # Different from embedding1
        
        similarity_same = calculate_similarity(embedding1, embedding2)
        similarity_different = calculate_similarity(embedding1, embedding3)
        
        assert similarity_same > similarity_different
        assert 0 <= similarity_same <= 1
        assert 0 <= similarity_different <= 1


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestDatabaseFunctions:
    """Test database utility functions"""
    
    def test_supabase_client_creation(self):
        """Test Supabase client creation"""
        with patch.dict('os.environ', {
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key'
        }):
            client = get_supabase_client()
            assert client is not None
    
    def test_supabase_client_missing_env(self):
        """Test Supabase client creation with missing environment variables"""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(Exception):
                get_supabase_client()


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestJSONHandling:
    """Test JSON processing functions"""
    
    def test_safe_json_loads_valid(self):
        """Test safe JSON loading with valid JSON"""
        valid_json = '{"key": "value", "number": 123}'
        result = safe_json_loads(valid_json)
        
        assert isinstance(result, dict)
        assert result["key"] == "value"
        assert result["number"] == 123
    
    def test_safe_json_loads_invalid(self):
        """Test safe JSON loading with invalid JSON"""
        invalid_json = '{"key": "value", "incomplete":'
        result = safe_json_loads(invalid_json, default={})
        
        assert result == {}
    
    def test_safe_json_loads_with_default(self):
        """Test safe JSON loading with custom default"""
        invalid_json = 'not json at all'
        default_value = {"error": True}
        result = safe_json_loads(invalid_json, default=default_value)
        
        assert result == default_value


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestRetryMechanism:
    """Test retry mechanism and error handling"""
    
    @pytest.mark.asyncio
    async def test_retry_with_backoff_success(self):
        """Test retry mechanism with eventual success"""
        call_count = 0
        
        async def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = await retry_with_backoff(flaky_function, max_retries=3)
        
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_retry_with_backoff_failure(self):
        """Test retry mechanism with persistent failure"""
        async def always_fails():
            raise Exception("Persistent failure")
        
        with pytest.raises(Exception):
            await retry_with_backoff(always_fails, max_retries=2)


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestURLUtilities:
    """Test URL processing utilities"""
    
    def test_url_normalization(self):
        """Test URL normalization"""
        test_cases = [
            ("HTTP://EXAMPLE.COM/Path", "http://example.com/Path"),
            ("https://example.com//double//slash", "https://example.com/double/slash"),
            ("https://example.com/path?", "https://example.com/path"),
        ]
        
        for input_url, expected in test_cases:
            result = normalize_url(input_url)
            assert result == expected
    
    def test_domain_extraction(self):
        """Test domain extraction from URLs"""
        test_cases = [
            ("https://example.com/path", "example.com"),
            ("http://api.service.com:8080/endpoint", "api.service.com"),
            ("https://subdomain.example.org/", "subdomain.example.org"),
        ]
        
        for url, expected_domain in test_cases:
            result = extract_domain(url)
            assert result == expected_domain
    
    def test_invalid_url_handling(self):
        """Test handling of invalid URLs"""
        invalid_urls = [
            "not-a-url",
            "http://",
            "",
            None,
        ]
        
        for invalid_url in invalid_urls:
            domain = extract_domain(invalid_url)
            assert domain is None or domain == ""


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestFileUtilities:
    """Test file processing utilities"""
    
    def test_file_size_formatting(self):
        """Test human-readable file size formatting"""
        test_cases = [
            (1024, "1.0 KB"),
            (1048576, "1.0 MB"), 
            (1073741824, "1.0 GB"),
            (500, "500 B"),
            (0, "0 B"),
        ]
        
        for size_bytes, expected in test_cases:
            result = format_file_size(size_bytes)
            assert result == expected


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestAsyncUtilities:
    """Test async utility functions"""
    
    @pytest.mark.asyncio
    async def test_async_batch_processing(self):
        """Test async batch processing utilities"""
        async def process_item(item):
            await asyncio.sleep(0.01)  # Simulate async work
            return item * 2
        
        items = list(range(10))
        
        # Test manual batch processing since function may not exist
        results = []
        batch_size = 3
        for i in range(0, len(items), batch_size):
            batch = items[i:i+batch_size]
            batch_results = []
            for item in batch:
                result = await process_item(item)
                batch_results.append(result)
            results.extend(batch_results)
        
        assert len(results) == len(items)
        assert results == [item * 2 for item in items]


@pytest.mark.skipif(not UTILS_AVAILABLE, reason="Utils module not available")
@pytest.mark.unit
class TestErrorHandlingUtilities:
    """Test error handling utility functions"""
    
    def test_error_context_preservation(self):
        """Test that error context is preserved"""
        try:
            raise ValueError("Original error message")
        except Exception as e:
            # Test that error details are captured
            assert str(e) == "Original error message"
            assert type(e).__name__ == "ValueError"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "unit"])