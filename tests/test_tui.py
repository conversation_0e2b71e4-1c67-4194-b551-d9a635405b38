"""
Tests for TUI application components.

This module contains comprehensive tests for the TUI application including:
- Screen functionality
- MCP integration 
- User interactions
- Error handling
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from textual.app import App
from textual.widgets import <PERSON><PERSON>, Text<PERSON>rea

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tui.app import AYKnowledgeBaseTUI
from tui.screens.home import HomeScreen
from tui.screens.chat import ChatScreen, MessageWidget
from tui.screens.crawl_url import CrawlURLScreen
from tui.screens.sources import SourcesScreen


class TestTUIApp:
    """Test the main TUI application."""
    
    def test_app_initialization(self):
        """Test that the app initializes correctly."""
        app = AYKnowledgeBaseTUI()
        assert app.title == "AY Knowledge Base v0.1"
        assert app.sub_title == "Intelligent RAG-powered Knowledge Management"
        assert hasattr(app, 'chat_history')
        assert hasattr(app, 'sources_cache')
        assert hasattr(app, 'settings')
    
    def test_app_settings_initialization(self):
        """Test that app settings are initialized from environment."""
        with patch.dict('os.environ', {
            'QUERY_ENHANCEMENT_MODEL': 'test-model',
            'ADMIN_PASSWORD': 'test-password'
        }):
            app = AYKnowledgeBaseTUI()
            assert app.settings['openrouter_model'] == 'test-model'
            assert app.settings['admin_password'] == 'test-password'
    
    def test_connection_status_methods(self):
        """Test connection status display methods."""
        app = AYKnowledgeBaseTUI()
        
        # Test all connected
        app.mcp_connected = True
        app.db_connected = True
        status = app.get_connection_status()
        assert "Connected" in status
        assert "green" in status
        
        # Test MCP only
        app.mcp_connected = True
        app.db_connected = False
        status = app.get_connection_status()
        assert "MCP Only" in status
        assert "yellow" in status
        
        # Test disconnected
        app.mcp_connected = False
        app.db_connected = False
        status = app.get_connection_status()
        assert "Disconnected" in status
        assert "red" in status


class TestHomeScreen:
    """Test the home screen functionality."""
    
    def test_home_screen_compose(self):
        """Test that home screen composes correctly."""
        app = AYKnowledgeBaseTUI()
        screen = HomeScreen()
        screen.app = app
        
        # Test that compose returns the expected widgets
        widgets = list(screen.compose())
        assert len(widgets) > 0
    
    @pytest.mark.asyncio
    async def test_home_screen_navigation(self):
        """Test navigation methods exist and are callable."""
        app = AYKnowledgeBaseTUI()
        
        # Test navigation methods exist
        assert hasattr(app, 'show_crawl_url')
        assert hasattr(app, 'show_crawl_search')
        assert hasattr(app, 'show_chat')
        assert hasattr(app, 'show_sources')
        assert hasattr(app, 'show_settings')
        assert hasattr(app, 'show_help')


class TestChatScreen:
    """Test the chat screen functionality."""
    
    def test_message_widget_creation(self):
        """Test MessageWidget creation with different message types."""
        # Test user message
        user_msg = {
            "role": "user",
            "content": "Test message",
            "timestamp": datetime.now()
        }
        widget = MessageWidget(user_msg)
        assert widget.role == "user"
        assert widget.content == "Test message"
        
        # Test assistant message
        assistant_msg = {
            "role": "assistant",
            "content": "AI response",
            "timestamp": datetime.now()
        }
        widget = MessageWidget(assistant_msg)
        assert widget.role == "assistant"
        assert widget.content == "AI response"
        
        # Test system message
        system_msg = {
            "role": "system",
            "content": "System notification",
            "timestamp": datetime.now()
        }
        widget = MessageWidget(system_msg)
        assert widget.role == "system"
        assert widget.content == "System notification"
    
    def test_chat_screen_compose(self):
        """Test that chat screen composes correctly."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen.app = app
        
        widgets = list(screen.compose())
        assert len(widgets) > 0
    
    def test_command_handling(self):
        """Test chat command handling."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen.app = app
        
        # Mock add_message method
        screen.add_message = Mock()
        
        # Test /new command
        result = screen.handle_command("/new")
        assert result is True
        
        # Test /sources command
        with patch('tui.screens.chat.get_available_sources') as mock_sources:
            mock_sources.return_value = {
                "success": True,
                "sources": [{"domain": "test.com", "document_count": 5}]
            }
            result = screen.handle_command("/sources")
            assert result is True
        
        # Test unknown command
        result = screen.handle_command("/unknown")
        assert result is False


class TestMCPIntegration:
    """Test MCP integration issues and fixes."""
    
    def test_mcp_function_import_issue(self):
        """Test that direct MCP function imports fail correctly."""
        # This test documents the current broken state
        with pytest.raises(TypeError, match="missing 1 required positional argument"):
            from ay_rag_mcp import get_available_sources
            get_available_sources()  # Missing ctx parameter
    
    @patch('tui.screens.chat.get_available_sources')
    def test_mcp_function_mocking(self, mock_get_sources):
        """Test that MCP functions can be mocked for testing."""
        mock_get_sources.return_value = {
            "success": True,
            "sources": [
                {"domain": "example.com", "document_count": 10},
                {"domain": "test.org", "document_count": 5}
            ]
        }
        
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen.app = app
        screen.add_message = Mock()
        
        screen.show_sources_info()
        
        # Verify the message was added
        screen.add_message.assert_called_once()
        call_args = screen.add_message.call_args[0][0]
        assert call_args["role"] == "assistant"
        assert "Available Sources (2)" in call_args["content"]
        assert "example.com" in call_args["content"]


class TestTUIAsyncHandling:
    """Test async operation handling in TUI."""
    
    @pytest.mark.asyncio
    async def test_async_to_thread_usage(self):
        """Test that async operations are handled correctly."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen.app = app
        
        # Mock the necessary components
        screen.query_one = Mock()
        loading_mock = Mock()
        loading_mock.visible = False
        screen.query_one.return_value = loading_mock
        screen.add_message = Mock()
        screen.set_focus = Mock()
        
        # Mock the MCP function
        with patch('tui.screens.chat.perform_rag_query') as mock_rag:
            mock_rag.return_value = {
                "success": True,
                "response": "Test response",
                "sources": [{"title": "Test", "url": "http://test.com"}]
            }
            
            # This should work if properly implemented
            with patch('asyncio.to_thread', new_callable=AsyncMock) as mock_to_thread:
                mock_to_thread.return_value = mock_rag.return_value
                
                await screen.process_query("test query")
                
                # Verify async handling
                mock_to_thread.assert_called_once()
                screen.add_message.assert_called()


class TestTUIErrorHandling:
    """Test error handling in TUI components."""
    
    def test_sources_error_handling(self):
        """Test error handling when getting sources fails."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen.app = app
        screen.add_message = Mock()
        
        # Mock get_available_sources to raise exception
        with patch('tui.screens.chat.get_available_sources', side_effect=Exception("Connection failed")):
            screen.show_sources_info()
            
            screen.add_message.assert_called_once()
            call_args = screen.add_message.call_args[0][0]
            assert call_args["role"] == "assistant"
            assert "Error fetching sources" in call_args["content"]
            assert "Connection failed" in call_args["content"]
    
    @pytest.mark.asyncio
    async def test_query_error_handling(self):
        """Test error handling during query processing."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen.app = app
        
        screen.query_one = Mock()
        loading_mock = Mock()
        loading_mock.visible = False
        screen.query_one.return_value = loading_mock
        screen.add_message = Mock()
        screen.set_focus = Mock()
        
        # Mock asyncio.to_thread to raise exception
        with patch('asyncio.to_thread', side_effect=Exception("Query failed")):
            await screen.process_query("test query")
            
            # Verify error message was added
            screen.add_message.assert_called()
            call_args = screen.add_message.call_args[0][0]
            assert call_args["role"] == "assistant"
            assert "Error processing query" in call_args["content"]
            assert "Query failed" in call_args["content"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])