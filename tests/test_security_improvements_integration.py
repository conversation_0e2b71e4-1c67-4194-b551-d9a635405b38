"""
Integration tests for security improvements

This module tests the complete integration of error handling, content validation,
and content management features to ensure they work together properly.
"""
import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from src.error_handler import create_safe_error_response, ErrorCategory
from src.content_validator import validate_url_content, ContentRisk, RiskReason


class TestSecurityIntegration:
    """Test integration of all security improvements"""
    
    def test_error_handler_import_in_mcp_tools(self):
        """Test that error handler is properly imported in MCP tools"""
        # This test ensures the imports work correctly
        from src.error_handler import create_safe_error_response
        
        # Simulate an error that might occur in MCP tools
        error = Exception("Database connection failed")
        response = create_safe_error_response(error, "mcp_tool_operation")
        
        assert response["success"] is False
        assert response["error_type"] == ErrorCategory.DATABASE.value
        assert "Database connection error" in response["error"]
    
    def test_content_validation_integration(self):
        """Test content validation integration"""
        from src.content_validator import ContentSafetyValidator
        
        validator = ContentSafetyValidator()
        
        # Test suspicious pizza form content
        pizza_content = """
        <h1>Order Pizza</h1>
        <form>
            <input type="text" name="address">
            <input type="text" name="credit_card" placeholder="Card Number">
            <input type="text" name="cvv">
            <button>Order Now</button>
        </form>
        """
        
        result = validator.validate_content(pizza_content, "https://pizza.com/order")
        
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert any("payment" in detail.lower() or "credit card" in detail.lower() 
                  for detail in result.details)
    
    @patch('src.ay_rag_mcp.get_supabase_client')
    def test_content_management_error_handling(self, mock_supabase):
        """Test that content management tools use safe error handling"""
        # This would test the actual MCP tools, but we'll simulate the behavior
        
        # Simulate a database error
        mock_client = Mock()
        mock_client.table.side_effect = Exception("Connection to database failed")
        mock_supabase.return_value = mock_client
        
        # Test that errors are handled safely
        error = Exception("Connection to database failed")
        response = create_safe_error_response(error, "list_content")
        
        assert response["success"] is False
        assert response["error_type"] == ErrorCategory.DATABASE.value
        assert "Connection to database failed" not in response["error"]
        assert "database" in response["error"].lower()
    
    def test_validation_with_different_risk_levels(self):
        """Test validation produces appropriate risk levels"""
        from src.content_validator import ContentSafetyValidator
        
        validator = ContentSafetyValidator()
        
        test_cases = [
            # Safe content
            ("<h1>Welcome</h1><p>This is safe content.</p>", ContentRisk.SAFE),
            
            # Suspicious content (form but not dangerous)
            ("<form><input type='text' name='name'><input type='email'></form>", ContentRisk.SAFE),
            
            # Suspicious content (password form)
            ("<form><input type='password' name='pass'></form>", ContentRisk.SUSPICIOUS),
            
            # High-risk content (multiple factors)
            ("""
            <h1>URGENT: Verify Your Account</h1>
            <form>
                <input type="password">
                <input name="credit_card">
                <input name="ssn">
            </form>
            <script>stealData();</script>
            """, ContentRisk.BLOCKED)
        ]
        
        for content, expected_risk in test_cases:
            result = validator.validate_content(content, "https://test.com")
            assert result.risk_level == expected_risk, f"Failed for content: {content[:50]}..."
    
    def test_error_categorization_accuracy(self):
        """Test that error categorization works correctly"""
        from src.error_handler import categorize_error, ErrorCategory
        
        test_cases = [
            (Exception("Network timeout"), ErrorCategory.NETWORK),
            (Exception("OpenAI API key invalid"), ErrorCategory.API),
            (Exception("Supabase connection failed"), ErrorCategory.DATABASE),
            (ValueError("Invalid input"), ErrorCategory.VALIDATION),
            (Exception("Operation timed out"), ErrorCategory.TIMEOUT),
            (Exception("Server initialization not complete"), ErrorCategory.INITIALIZATION),
            (Exception("Random error"), ErrorCategory.INTERNAL)
        ]
        
        for error, expected_category in test_cases:
            category = categorize_error(error)
            assert category == expected_category, f"Failed for error: {str(error)}"
    
    def test_safe_error_response_structure(self):
        """Test that safe error responses have consistent structure"""
        from src.error_handler import create_safe_error_response
        
        errors = [
            Exception("Network error"),
            ValueError("Invalid input"),
            Exception("Database failed"),
            Exception("API error")
        ]
        
        for error in errors:
            response = create_safe_error_response(error, "test_operation")
            
            # Check required fields
            assert "success" in response
            assert "error" in response
            assert "error_type" in response
            assert "suggestions" in response
            
            # Check values
            assert response["success"] is False
            assert isinstance(response["error"], str)
            assert isinstance(response["error_type"], str)
            assert isinstance(response["suggestions"], list)
            
            # Check that error message is user-friendly
            assert len(response["error"]) > 10  # Not just empty
            assert "Exception" not in response["error"]  # No raw exception names
            assert "Traceback" not in response["error"]  # No tracebacks
    
    def test_content_validation_performance(self):
        """Test that content validation performs reasonably fast"""
        from src.content_validator import ContentSafetyValidator
        import time
        
        validator = ContentSafetyValidator()
        
        # Test with moderately large content
        large_content = """
        <h1>Large Document</h1>
        """ + "<p>This is a paragraph of text. " * 100 + "</p>"
        
        start_time = time.time()
        result = validator.validate_content(large_content, "https://example.com")
        end_time = time.time()
        
        # Should complete in reasonable time
        assert end_time - start_time < 1.0  # Less than 1 second
        assert result.risk_level == ContentRisk.SAFE
    
    def test_content_management_safety_features(self):
        """Test safety features in content management"""
        from src.error_handler import validation_error_response
        
        # Test that bulk operations require confirmation
        # This simulates the validation logic in delete_content
        
        # Single URL deletion - should work without confirmation
        deletion_params = ["http://example.com", None, None, None]  # url, source, content_id, risk_level
        param_count = sum(1 for param in deletion_params if param is not None)
        bulk_operations = [deletion_params[1], deletion_params[3]]  # source, risk_level
        confirm = False
        
        assert param_count == 1  # Only URL provided
        assert not any(bulk_operations)  # No bulk operations
        # Should proceed without requiring confirmation
        
        # Bulk deletion by source - should require confirmation
        deletion_params = [None, "example.com", None, None]
        param_count = sum(1 for param in deletion_params if param is not None)
        bulk_operations = [deletion_params[1], deletion_params[3]]
        confirm = False
        
        assert param_count == 1
        assert any(bulk_operations)  # Bulk operation detected
        assert not confirm  # No confirmation provided
        
        # Should return validation error
        response = validation_error_response("Bulk deletion requires confirm=True parameter for safety")
        assert response["success"] is False
        assert "confirm" in response["error"]


class TestRealWorldScenarios:
    """Test realistic scenarios that might occur in production"""
    
    def test_pizza_ordering_scenario(self):
        """Test the specific pizza ordering scenario mentioned"""
        from src.content_validator import validate_url_content
        
        pizza_form_html = """
        <!DOCTYPE html>
        <html>
        <head><title>Tony's Pizza - Order Online</title></head>
        <body>
            <h1>Order Your Favorite Pizza</h1>
            <form action="/order" method="post">
                <h3>Pizza Details</h3>
                <select name="size">
                    <option value="small">Small ($12.99)</option>
                    <option value="large">Large ($18.99)</option>
                </select>
                
                <h3>Delivery Information</h3>
                <input type="text" name="customer_name" placeholder="Your Name" required>
                <input type="text" name="delivery_address" placeholder="Delivery Address" required>
                <input type="tel" name="phone_number" placeholder="Phone Number" required>
                
                <h3>Payment Information</h3>
                <input type="text" name="credit_card" placeholder="Credit Card Number" required>
                <input type="text" name="expiry" placeholder="MM/YY" required>
                <input type="text" name="cvv" placeholder="CVV" required>
                
                <button type="submit">Place Order ($18.99)</button>
            </form>
        </body>
        </html>
        """
        
        result = validate_url_content(
            pizza_form_html, 
            "https://tonys-pizza.com/order", 
            "Tony's Pizza - Order Online"
        )
        
        # Should be flagged as suspicious due to payment form
        assert result.risk_level in [ContentRisk.SUSPICIOUS, ContentRisk.BLOCKED]
        assert RiskReason.PAYMENT_FORM in result.reasons
        assert any("credit card" in detail.lower() for detail in result.details)
        assert not result.safe_to_store or result.risk_level == ContentRisk.SUSPICIOUS
    
    def test_legitimate_documentation_site(self):
        """Test that legitimate documentation is not flagged"""
        from src.content_validator import validate_url_content
        
        docs_content = """
        <h1>API Documentation</h1>
        <h2>Authentication</h2>
        <p>To authenticate with our API, include your API key in the header:</p>
        <pre><code>
        curl -H "Authorization: Bearer YOUR_API_KEY" \\
             https://api.example.com/users
        </code></pre>
        
        <h2>User Management</h2>
        <h3>GET /users</h3>
        <p>Retrieve a list of users.</p>
        
        <h3>POST /users</h3>
        <p>Create a new user.</p>
        
        <h3>DELETE /users/{id}</h3>
        <p>Delete a user by ID.</p>
        
        <a href="/api/reference">API Reference</a>
        <a href="/examples">Code Examples</a>
        """
        
        result = validate_url_content(
            docs_content,
            "https://docs.example.com/api",
            "API Documentation"
        )
        
        # Should be safe
        assert result.risk_level == ContentRisk.SAFE
        assert len(result.reasons) == 0
        assert result.safe_to_store is True
    
    def test_phishing_attempt_detection(self):
        """Test detection of a realistic phishing attempt"""
        from src.content_validator import validate_url_content
        
        phishing_content = """
        <h1>SECURITY ALERT: Immediate Action Required</h1>
        <p style="color: red; font-weight: bold;">
            Your account has been temporarily suspended due to suspicious activity detected.
        </p>
        <p>
            To restore access to your account, you must verify your identity immediately.
            Failure to do so within 24 hours will result in permanent account closure.
        </p>
        <form action="https://secure-verify.fake-bank.com/verify" method="post">
            <h3>Account Verification</h3>
            <input type="text" name="username" placeholder="Username" required>
            <input type="password" name="password" placeholder="Password" required>
            <input type="text" name="ssn" placeholder="Social Security Number" required>
            <input type="text" name="account_number" placeholder="Account Number" required>
            
            <button type="submit">Verify Account Now</button>
        </form>
        <p>
            <strong>This verification must be completed immediately.</strong>
            If you do not verify your account, you will lose access permanently.
        </p>
        """
        
        result = validate_url_content(
            phishing_content,
            "https://fake-bank-security.com/urgent-verification",
            "URGENT: Account Verification Required"
        )
        
        # Should be blocked due to multiple high-risk factors
        assert result.risk_level == ContentRisk.BLOCKED
        assert result.safe_to_store is False
        
        # Should detect multiple risk factors
        expected_reasons = [
            RiskReason.ACCOUNT_VERIFICATION,
            RiskReason.URGENT_ACTION,
            RiskReason.PASSWORD_FORM,
            RiskReason.PERSONAL_DATA_FORM
        ]
        
        detected_reasons = set(result.reasons)
        assert len(detected_reasons.intersection(expected_reasons)) >= 2  # At least 2 factors


if __name__ == "__main__":
    pytest.main([__file__])