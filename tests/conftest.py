"""
Shared test fixtures and configuration for TUI testing.
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import Mock, patch
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


@pytest.fixture
def mock_env_vars():
    """Fixture to provide mock environment variables."""
    env_vars = {
        'SUPABASE_URL': 'https://test.supabase.co',
        'SUPABASE_SERVICE_KEY': 'test-key',
        'OPENAI_API_KEY': 'test-openai-key',
        'QUERY_ENHANCEMENT_MODEL': 'gpt-4o-mini',
        'ADMIN_PASSWORD': 'test-admin-123'
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars


@pytest.fixture
def mock_tui_app():
    """Fixture to provide a mocked TUI app."""
    from tui.app import AYKnowledgeBaseTUI
    
    app = AYKnowledgeBaseTUI()
    app.mcp_connected = True
    app.db_connected = True
    app.chat_history = []
    app.sources_cache = None
    
    return app


@pytest.fixture
def sample_message():
    """Fixture to provide a sample chat message."""
    return {
        "role": "user",
        "content": "Test message content",
        "timestamp": datetime.now()
    }


@pytest.fixture
def sample_sources():
    """Fixture to provide sample source data."""
    return [
        {
            "id": "example.com",
            "name": "Example Site",
            "url": "https://example.com",
            "domain": "example.com",
            "document_count": 25,
            "last_crawled": "2024-01-20T10:30:00Z",
            "status": "active",
            "avg_document_size": 2500,
            "crawl_success_rate": 0.95,
            "code_examples_count": 5,
            "summary": "Example website for testing",
            "total_word_count": 50000
        },
        {
            "id": "test.org",
            "name": "Test Organization",
            "url": "https://test.org",
            "domain": "test.org",
            "document_count": 10,
            "last_crawled": "2024-01-19T15:45:00Z",
            "status": "active",
            "avg_document_size": 1800,
            "crawl_success_rate": 0.88,
            "code_examples_count": 2,
            "summary": "Test organization documentation",
            "total_word_count": 18000
        }
    ]


@pytest.fixture
def mock_mcp_functions():
    """Fixture to mock all MCP functions used by TUI."""
    mocks = {}
    
    # Mock get_available_sources
    get_sources_mock = Mock(return_value={
        "success": True,
        "sources": [
            {"domain": "example.com", "document_count": 25, "last_crawled": "2024-01-20"},
            {"domain": "test.org", "document_count": 10, "last_crawled": "2024-01-19"}
        ]
    })
    
    # Mock perform_rag_query
    rag_query_mock = Mock(return_value={
        "success": True,
        "response": "This is a test response from the RAG system.",
        "sources": [
            {"title": "Test Document", "url": "https://example.com/doc1"},
            {"title": "Another Document", "url": "https://test.org/doc2"}
        ]
    })
    
    # Mock crawl functions
    crawl_single_page_mock = Mock(return_value={
        "success": True,
        "message": "Successfully crawled page",
        "documents_added": 1
    })
    
    smart_crawl_url_mock = Mock(return_value={
        "success": True,
        "message": "Successfully crawled URL",
        "documents_added": 5,
        "pages_crawled": 5
    })
    
    mocks['get_available_sources'] = get_sources_mock
    mocks['perform_rag_query'] = rag_query_mock
    mocks['crawl_single_page'] = crawl_single_page_mock
    mocks['smart_crawl_url'] = smart_crawl_url_mock
    
    # Apply patches
    patches = []
    patches.append(patch('tui.screens.chat.get_available_sources', get_sources_mock))
    patches.append(patch('tui.screens.chat.perform_rag_query', rag_query_mock))
    patches.append(patch('tui.screens.crawl_url.crawl_single_page', crawl_single_page_mock))
    patches.append(patch('tui.screens.crawl_url.smart_crawl_url', smart_crawl_url_mock))
    patches.append(patch('tui.screens.sources.get_available_sources', get_sources_mock))
    
    # Start all patches
    for p in patches:
        p.start()
    
    yield mocks
    
    # Stop all patches
    for p in patches:
        p.stop()


@pytest.fixture
def mock_textual_widgets():
    """Fixture to mock Textual widgets for testing."""
    widget_mocks = {}
    
    # Mock query_one method
    def create_mock_query_one(widget_type):
        mock_widget = Mock()
        mock_widget.value = ""
        mock_widget.visible = False
        mock_widget.children = []
        return mock_widget
    
    widget_mocks['query_one'] = create_mock_query_one
    
    return widget_mocks


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def async_mock():
    """Fixture to provide AsyncMock for async testing."""
    from unittest.mock import AsyncMock
    return AsyncMock