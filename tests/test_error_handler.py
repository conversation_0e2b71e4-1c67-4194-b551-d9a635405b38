"""
Tests for the error handling module

This module tests the safe error handling functionality to ensure
that sensitive information is not exposed to users.
"""
import pytest
import logging
from unittest.mock import patch
from src.error_handler import (
    ErrorCategory,
    categorize_error,
    create_safe_error_response,
    sanitize_context,
    network_error_response,
    validation_error_response,
    initialization_error_response
)


class TestErrorCategorization:
    """Test error categorization functionality"""
    
    def test_network_errors(self):
        """Test network error categorization"""
        errors = [
            ConnectionError("Failed to connect to host"),
            Exception("Network timeout occurred"),
            Exception("Connection refused"),
            Exception("urllib error"),
            Exception("socket timeout")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.NETWORK
    
    def test_api_errors(self):
        """Test API error categorization"""
        errors = [
            Exception("OpenAI API key invalid"),
            Exception("401 Unauthorized"),
            Exception("Rate limit exceeded"),
            Exception("API quota exceeded")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.API
    
    def test_database_errors(self):
        """Test database error categorization"""
        errors = [
            Exception("Supabase connection failed"),
            Exception("PostgreSQL error"),
            Exception("Table does not exist"),
            Exception("Foreign key constraint")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.DATABASE
    
    def test_validation_errors(self):
        """Test validation error categorization"""
        errors = [
            ValueError("Invalid input"),
            TypeError("Expected string, got int"),
            KeyError("missing required field"),
            Exception("Bad request - validation failed")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.VALIDATION
    
    def test_timeout_errors(self):
        """Test timeout error categorization"""
        errors = [
            Exception("Operation timed out"),
            Exception("Request timeout"),
            Exception("asyncio.TimeoutError")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.TIMEOUT
    
    def test_initialization_errors(self):
        """Test initialization error categorization"""
        errors = [
            Exception("Server initialization not complete"),
            Exception("Component not ready"),
            Exception("Starting up")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.INITIALIZATION
    
    def test_internal_errors(self):
        """Test internal error categorization (default)"""
        errors = [
            Exception("Some random error"),
            Exception("Unexpected condition"),
            RuntimeError("Unknown issue")
        ]
        
        for error in errors:
            category = categorize_error(error)
            assert category == ErrorCategory.INTERNAL


class TestSafeErrorResponse:
    """Test safe error response generation"""
    
    @patch('src.error_handler.logger')
    def test_creates_safe_response(self, mock_logger):
        """Test that safe error responses are created"""
        error = Exception("Database connection failed with credentials: user@host:5432/db")
        
        response = create_safe_error_response(error, "test_context")
        
        # Check response structure
        assert "success" in response
        assert response["success"] is False
        assert "error" in response
        assert "error_type" in response
        assert "suggestions" in response
        
        # Check that sensitive info is not exposed
        assert "user@host" not in response["error"]
        assert "5432" not in response["error"]
        assert "credentials" not in response["error"]
        
        # Check that error is logged
        mock_logger.error.assert_called_once()
    
    def test_network_error_response(self):
        """Test network error convenience function"""
        response = network_error_response("crawl_operation")
        
        assert response["success"] is False
        assert response["error_type"] == ErrorCategory.NETWORK.value
        assert "suggestions" in response
        assert len(response["suggestions"]) > 0
    
    def test_validation_error_response(self):
        """Test validation error convenience function"""
        response = validation_error_response("Invalid URL format")
        
        assert response["success"] is False
        assert response["error"] == "Invalid URL format"
        assert response["error_type"] == ErrorCategory.VALIDATION.value
    
    def test_initialization_error_response(self):
        """Test initialization error convenience function"""
        response = initialization_error_response("server_startup")
        
        assert response["success"] is False
        assert response["error_type"] == ErrorCategory.INITIALIZATION.value
        assert "suggestions" in response


class TestContextSanitization:
    """Test context sanitization functionality"""
    
    def test_sanitizes_api_keys(self):
        """Test that API keys are sanitized"""
        contexts = [
            "Error with key sk-1234567890abcdef",
            "OpenAI key: sk-proj-xyz123",
            "Failed using sk-test-key"
        ]
        
        for context in contexts:
            sanitized = sanitize_context(context)
            assert "sk-" not in sanitized
            assert "[REDACTED]" in sanitized
    
    def test_sanitizes_passwords(self):
        """Test that passwords are sanitized"""
        contexts = [
            "Database error: password=secret123",
            "Failed auth with password: mypass",
            "Connection failed password=xyz"
        ]
        
        for context in contexts:
            sanitized = sanitize_context(context)
            assert "secret123" not in sanitized
            assert "mypass" not in sanitized
            assert "[REDACTED]" in sanitized
    
    def test_sanitizes_file_paths(self):
        """Test that sensitive file paths are removed"""
        contexts = [
            "/home/<USER>/secret/file.txt",
            "C:\\Users\\<USER>\\Documents\\keys.txt",
            "Error in /home/<USER>/config"
        ]
        
        for context in contexts:
            sanitized = sanitize_context(context)
            # Long paths or paths with sensitive info should be None
            assert sanitized is None or "/home/" not in sanitized
    
    def test_preserves_safe_context(self):
        """Test that safe context is preserved"""
        safe_contexts = [
            "crawl_operation",
            "validation_check",
            "network_request"
        ]
        
        for context in safe_contexts:
            sanitized = sanitize_context(context)
            assert sanitized == context
    
    def test_handles_empty_context(self):
        """Test handling of empty context"""
        assert sanitize_context("") is None
        assert sanitize_context(None) is None


class TestErrorResponseIntegration:
    """Test integration of error handling components"""
    
    def test_complete_error_flow(self):
        """Test complete error handling flow"""
        # Simulate a database connection error
        error = Exception("Connection to ****************************/mydb failed")
        
        response = create_safe_error_response(error, "database_operation", log_full_error=False)
        
        # Verify response structure
        assert response["success"] is False
        assert response["error_type"] == ErrorCategory.DATABASE.value
        
        # Verify sensitive data is not exposed
        error_text = response["error"]
        assert "user:pass" not in error_text
        assert "5432" not in error_text
        assert "postgres://" not in error_text
        
        # Verify helpful suggestions are provided
        assert "suggestions" in response
        assert len(response["suggestions"]) > 0
        assert any("database" in suggestion.lower() for suggestion in response["suggestions"])
    
    def test_error_without_suggestions(self):
        """Test error response without suggestions"""
        error = Exception("Some internal error")
        
        response = create_safe_error_response(error, "test", include_suggestions=False)
        
        assert "suggestions" not in response
        assert response["error_type"] == ErrorCategory.INTERNAL.value
    
    def test_multiple_error_types(self):
        """Test handling of complex errors with multiple indicators"""
        error = Exception("Network timeout while connecting to OpenAI API")
        
        # Should categorize as network error (first match)
        category = categorize_error(error)
        assert category == ErrorCategory.NETWORK
        
        response = create_safe_error_response(error, "api_call")
        assert response["error_type"] == ErrorCategory.NETWORK.value


if __name__ == "__main__":
    pytest.main([__file__])