"""
Comprehensive MCP Integration Test Suite

Tests for Model Context Protocol server functionality including:
- Tool execution
- Resource access  
- Error handling
- Authentication
- Performance validation
"""

import pytest
import asyncio
import json
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

try:
    # Import MCP server components
    from ay_rag_mcp import (
        crawl_single_page,
        smart_crawl_url, 
        perform_rag_query,
        get_available_sources,
        search_code_examples
    )
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False


@pytest.mark.skipif(not MCP_AVAILABLE, reason="MCP server components not available")
@pytest.mark.integration
class TestMCPToolExecution:
    """Test MCP tool execution and functionality"""
    
    @pytest.mark.asyncio
    async def test_crawl_single_page_tool(self):
        """Test crawl_single_page MCP tool"""
        # Mock the FastMCP context and dependencies
        mock_context = Mock()
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_context.request_context.lifespan_context.supabase_client = Mock()
        
        # Mock successful crawling response
        mock_result = Mock()
        mock_result.markdown = "# Test Page\n\nThis is test content with code:\n\n```python\ndef test():\n    pass\n```"
        mock_result.metadata.title = "Test Page"
        mock_result.links = {"internal": [], "external": ["https://example.com"]}
        
        mock_context.request_context.lifespan_context.crawler.arun.return_value = mock_result
        
        with patch('ay_rag_mcp.get_supabase_client') as mock_get_client, \
             patch('ay_rag_mcp.create_embeddings') as mock_embeddings:
            
            mock_get_client.return_value = mock_context.request_context.lifespan_context.supabase_client
            mock_embeddings.return_value = [[0.1] * 1536] * 5  # Mock embeddings
            
            # Test valid URL
            result = await crawl_single_page(mock_context, "https://example.com/test")
            
            assert isinstance(result, dict)
            assert "success" in result
            assert "message" in result
            assert result["success"] is True
    
    @pytest.mark.asyncio
    async def test_smart_crawl_url_tool(self):
        """Test smart_crawl_url MCP tool with different URL types"""
        mock_context = Mock()
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_context.request_context.lifespan_context.supabase_client = Mock()
        
        test_cases = [
            ("https://example.com/page", "regular"),
            ("https://example.com/sitemap.xml", "sitemap"),
            ("https://example.com/urls.txt", "text_file"),
        ]
        
        for url, expected_type in test_cases:
            # Mock response based on URL type
            if "sitemap.xml" in url:
                mock_response = Mock()
                mock_response.text = '''<?xml version="1.0" encoding="UTF-8"?>
                <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
                  <url><loc>https://example.com/page1</loc></url>
                  <url><loc>https://example.com/page2</loc></url>
                </urlset>'''
            elif "urls.txt" in url:
                mock_response = Mock()
                mock_response.text = "https://example.com/page1\nhttps://example.com/page2"
            else:
                mock_response = Mock()
                mock_response.markdown = f"# {expected_type.title()} Page\n\nContent here"
                mock_response.metadata.title = f"{expected_type.title()} Page"
            
            with patch('ay_rag_mcp.aiohttp.ClientSession') as mock_session:
                mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
                
                result = await smart_crawl_url(mock_context, url)
                
                assert isinstance(result, dict)
                assert "success" in result
    
    @pytest.mark.asyncio
    async def test_perform_rag_query_tool(self):
        """Test perform_rag_query MCP tool"""
        mock_context = Mock()
        mock_supabase = Mock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Mock Supabase query response
        mock_response = Mock()
        mock_response.data = [
            {
                'content': 'This is test content about Python async functions',
                'url': 'https://example.com/python-async',
                'title': 'Python Async Tutorial',
                'metadata': {'topic': 'programming'},
                'similarity': 0.85
            },
            {
                'content': 'More content about async programming patterns',
                'url': 'https://example.com/async-patterns', 
                'title': 'Async Patterns',
                'metadata': {'topic': 'programming'},
                'similarity': 0.78
            }
        ]
        
        mock_supabase.rpc.return_value = mock_response
        
        with patch('ay_rag_mcp.enhance_search_query') as mock_enhance, \
             patch('ay_rag_mcp.calculate_relevance_score') as mock_relevance:
            
            mock_enhance.return_value = {
                'enhanced_query': 'python async functions programming',
                'primary_query': 'python async functions',
                'variations': ['async python', 'python asyncio'],
                'query_type': 'code_focused'
            }
            mock_relevance.side_effect = [0.85, 0.78]
            
            result = await perform_rag_query(
                mock_context, 
                "python async functions",
                source=None,
                match_count=5
            )
            
            assert isinstance(result, dict)
            assert "results" in result
            assert len(result["results"]) == 2
            assert result["results"][0]["similarity"] >= result["results"][1]["similarity"]
    
    @pytest.mark.asyncio 
    async def test_get_available_sources_tool(self):
        """Test get_available_sources MCP tool"""
        mock_context = Mock()
        mock_supabase = Mock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Mock sources data
        mock_response = Mock()
        mock_response.data = [
            {
                'source_id': 'example.com',
                'name': 'Example Website',
                'url': 'https://example.com',
                'document_count': 25,
                'last_crawl': '2024-01-15T10:30:00Z',
                'status': 'active'
            },
            {
                'source_id': 'docs.python.org',
                'name': 'Python Documentation', 
                'url': 'https://docs.python.org',
                'document_count': 150,
                'last_crawl': '2024-01-14T15:45:00Z',
                'status': 'active'
            }
        ]
        
        mock_supabase.table.return_value.select.return_value.execute.return_value = mock_response
        
        result = await get_available_sources(mock_context)
        
        assert isinstance(result, dict)
        assert "sources" in result
        assert len(result["sources"]) == 2
        assert result["sources"][0]["document_count"] == 25


@pytest.mark.skipif(not MCP_AVAILABLE, reason="MCP server components not available")
@pytest.mark.integration
class TestMCPErrorHandling:
    """Test MCP error handling and edge cases"""
    
    @pytest.mark.asyncio
    async def test_crawl_invalid_url(self):
        """Test crawling with invalid URL"""
        mock_context = Mock()
        
        with pytest.raises(Exception):
            await crawl_single_page(mock_context, "not-a-valid-url")
    
    @pytest.mark.asyncio
    async def test_crawl_timeout_handling(self):
        """Test crawl timeout handling"""
        mock_context = Mock()
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_context.request_context.lifespan_context.crawler.arun.side_effect = asyncio.TimeoutError("Request timeout")
        
        result = await crawl_single_page(mock_context, "https://timeout.example.com")
        
        assert isinstance(result, dict)
        assert result["success"] is False
        assert "timeout" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_database_connection_failure(self):
        """Test database connection failure handling"""
        mock_context = Mock()
        mock_context.request_context.lifespan_context.supabase_client = None
        
        with pytest.raises(Exception):
            await perform_rag_query(mock_context, "test query")
    
    @pytest.mark.asyncio
    async def test_empty_query_handling(self):
        """Test handling of empty or invalid queries"""
        mock_context = Mock()
        mock_supabase = Mock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Test empty query
        result = await perform_rag_query(mock_context, "", match_count=5)
        
        assert isinstance(result, dict)
        assert "error" in result or len(result.get("results", [])) == 0


@pytest.mark.skipif(not MCP_AVAILABLE, reason="MCP server components not available")
@pytest.mark.integration 
class TestMCPPerformance:
    """Test MCP performance and scalability"""
    
    @pytest.mark.asyncio
    async def test_concurrent_crawl_operations(self):
        """Test concurrent crawling operations"""
        mock_context = Mock()
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_context.request_context.lifespan_context.supabase_client = Mock()
        
        # Mock crawling response
        mock_result = Mock()
        mock_result.markdown = "# Test Page\n\nTest content"
        mock_result.metadata.title = "Test Page"
        mock_result.links = {"internal": [], "external": []}
        
        mock_context.request_context.lifespan_context.crawler.arun.return_value = mock_result
        
        with patch('ay_rag_mcp.create_embeddings') as mock_embeddings:
            mock_embeddings.return_value = [[0.1] * 1536] * 2
            
            # Run concurrent crawl operations
            urls = [f"https://example.com/page{i}" for i in range(5)]
            
            start_time = time.time()
            tasks = [crawl_single_page(mock_context, url) for url in urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Verify results
            successful_results = [r for r in results if isinstance(r, dict) and r.get("success")]
            
            # Performance assertions
            assert len(successful_results) >= 3  # At least 60% success rate
            assert end_time - start_time < 10  # Should complete within 10 seconds
    
    @pytest.mark.asyncio
    async def test_large_query_performance(self):
        """Test performance with large query results"""
        mock_context = Mock()
        mock_supabase = Mock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Mock large result set
        large_results = []
        for i in range(100):
            large_results.append({
                'content': f'Large content chunk {i}' * 100,  # ~2KB per chunk
                'url': f'https://example.com/page{i}',
                'title': f'Page {i}',
                'metadata': {'index': i},
                'similarity': 0.9 - (i * 0.001)  # Descending similarity
            })
        
        mock_response = Mock()
        mock_response.data = large_results
        mock_supabase.rpc.return_value = mock_response
        
        with patch('ay_rag_mcp.enhance_search_query') as mock_enhance:
            mock_enhance.return_value = {
                'enhanced_query': 'large query test',
                'primary_query': 'large query',
                'variations': ['big query'],
                'query_type': 'general'
            }
            
            start_time = time.time()
            result = await perform_rag_query(
                mock_context, 
                "large query test",
                match_count=50
            )
            end_time = time.time()
            
            # Performance and correctness assertions
            assert isinstance(result, dict)
            assert "results" in result
            assert len(result["results"]) <= 50  # Should respect match_count
            assert end_time - start_time < 5  # Should complete within 5 seconds
    
    @pytest.mark.asyncio
    async def test_memory_usage_with_large_content(self):
        """Test memory usage with large content processing"""
        mock_context = Mock() 
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_context.request_context.lifespan_context.supabase_client = Mock()
        
        # Create large mock content (~1MB)
        large_content = "# Large Document\n\n" + ("Large content block. " * 10000)
        
        mock_result = Mock()
        mock_result.markdown = large_content
        mock_result.metadata.title = "Large Document"
        mock_result.links = {"internal": [], "external": []}
        
        mock_context.request_context.lifespan_context.crawler.arun.return_value = mock_result
        
        with patch('ay_rag_mcp.create_embeddings') as mock_embeddings:
            # Mock embeddings for chunks (should be ~20-30 chunks for 1MB content)
            mock_embeddings.return_value = [[0.1] * 1536] * 25
            
            result = await crawl_single_page(mock_context, "https://example.com/large")
            
            assert isinstance(result, dict)
            # Should handle large content without memory issues


@pytest.mark.skipif(not MCP_AVAILABLE, reason="MCP server components not available")
@pytest.mark.integration
class TestMCPDataValidation:
    """Test MCP data validation and sanitization"""
    
    @pytest.mark.asyncio
    async def test_url_validation(self):
        """Test URL validation for security"""
        mock_context = Mock()
        
        # Test various URL formats
        test_cases = [
            ("https://example.com", True),
            ("http://example.com/path", True),
            ("javascript:alert('xss')", False),
            ("file:///etc/passwd", False),
            ("http://localhost/admin", False),  # Internal URL
            ("", False),
        ]
        
        for url, should_succeed in test_cases:
            if should_succeed:
                # Should not raise exception for valid URLs
                # (Actual crawling might fail, but URL should be accepted)
                pass
            else:
                # Should reject dangerous URLs
                with pytest.raises(Exception):
                    await crawl_single_page(mock_context, url)
    
    @pytest.mark.asyncio
    async def test_content_sanitization(self):
        """Test content sanitization for security"""
        mock_context = Mock()
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_context.request_context.lifespan_context.supabase_client = Mock()
        
        # Test malicious content
        malicious_content = '''
        # Test Page
        
        <script>alert('xss')</script>
        
        ```sql
        DROP TABLE users;
        ```
        
        Some legitimate content here.
        '''
        
        mock_result = Mock()
        mock_result.markdown = malicious_content
        mock_result.metadata.title = "<script>alert('title')</script>"
        mock_result.links = {"internal": [], "external": []}
        
        mock_context.request_context.lifespan_context.crawler.arun.return_value = mock_result
        
        with patch('ay_rag_mcp.create_embeddings') as mock_embeddings, \
             patch('ay_rag_mcp.sanitize_input') as mock_sanitize:
            
            mock_embeddings.return_value = [[0.1] * 1536] * 3
            mock_sanitize.side_effect = lambda x, **kwargs: x.replace('<script>', '').replace('</script>', '')
            
            result = await crawl_single_page(mock_context, "https://example.com/malicious")
            
            # Should succeed but content should be sanitized
            assert isinstance(result, dict)
            mock_sanitize.assert_called()  # Should sanitize content


@pytest.mark.skipif(not MCP_AVAILABLE, reason="MCP server components not available") 
@pytest.mark.integration
class TestMCPWorkflows:
    """Test end-to-end MCP workflows"""
    
    @pytest.mark.asyncio
    async def test_crawl_and_search_workflow(self):
        """Test complete crawl -> search workflow"""
        mock_context = Mock()
        mock_context.request_context.lifespan_context.crawler = AsyncMock()
        mock_supabase = Mock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Step 1: Crawl content
        mock_crawl_result = Mock()
        mock_crawl_result.markdown = '''
        # Python Async Tutorial
        
        Learn about Python async programming:
        
        ```python
        async def fetch_data():
            async with aiohttp.ClientSession() as session:
                async with session.get('https://api.example.com') as response:
                    return await response.json()
        ```
        '''
        mock_crawl_result.metadata.title = "Python Async Tutorial"
        mock_crawl_result.links = {"internal": [], "external": []}
        
        mock_context.request_context.lifespan_context.crawler.arun.return_value = mock_crawl_result
        
        with patch('ay_rag_mcp.create_embeddings') as mock_embeddings:
            mock_embeddings.return_value = [[0.1] * 1536] * 3
            
            # Crawl the page
            crawl_result = await crawl_single_page(mock_context, "https://example.com/python-async")
            assert crawl_result["success"] is True
        
        # Step 2: Search for the content
        mock_search_response = Mock()
        mock_search_response.data = [{
            'content': 'Python async programming with aiohttp',
            'url': 'https://example.com/python-async',
            'title': 'Python Async Tutorial',
            'metadata': {'language': 'python'},
            'similarity': 0.92
        }]
        
        mock_supabase.rpc.return_value = mock_search_response
        
        with patch('ay_rag_mcp.enhance_search_query') as mock_enhance, \
             patch('ay_rag_mcp.calculate_relevance_score') as mock_relevance:
            
            mock_enhance.return_value = {
                'enhanced_query': 'python async programming aiohttp',
                'primary_query': 'python async',
                'variations': ['python asyncio', 'async python'],
                'query_type': 'code_focused'
            }
            mock_relevance.return_value = 0.92
            
            # Search for the crawled content
            search_result = await perform_rag_query(mock_context, "python async programming")
            
            assert isinstance(search_result, dict)
            assert "results" in search_result
            assert len(search_result["results"]) == 1
            assert search_result["results"][0]["title"] == "Python Async Tutorial"
    
    @pytest.mark.asyncio
    async def test_source_management_workflow(self):
        """Test source management workflow"""
        mock_context = Mock()
        mock_supabase = Mock()
        mock_context.request_context.lifespan_context.supabase_client = mock_supabase
        
        # Mock initial empty sources
        empty_response = Mock()
        empty_response.data = []
        mock_supabase.table.return_value.select.return_value.execute.return_value = empty_response
        
        # Get sources (should be empty initially)
        sources_result = await get_available_sources(mock_context)
        assert len(sources_result["sources"]) == 0
        
        # After crawling, sources should be populated
        populated_response = Mock()
        populated_response.data = [{
            'source_id': 'example.com',
            'name': 'Example Site',
            'url': 'https://example.com',
            'document_count': 1,
            'last_crawl': '2024-01-15T10:30:00Z',
            'status': 'active'
        }]
        
        mock_supabase.table.return_value.select.return_value.execute.return_value = populated_response
        
        # Get sources after crawling
        updated_sources = await get_available_sources(mock_context)
        assert len(updated_sources["sources"]) == 1
        assert updated_sources["sources"][0]["source_id"] == "example.com"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "integration"])