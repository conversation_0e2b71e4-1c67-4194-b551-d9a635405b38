"""
Integration tests for TUI with MCP client.

These tests verify the integration between TUI components and the MCP client,
including proper error handling, async operations, and data flow.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import json

# Add src to path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from tui.mcp_client import MCPClient, get_mcp_client
from tui.app import AYKnowledgeBaseTUI
from tui.screens.chat import ChatScreen


class TestMCPClient:
    """Test the MCP client functionality."""
    
    @pytest.fixture
    def mock_session(self):
        """Mock aiohttp session."""
        session = AsyncMock()
        
        # Mock health check response
        health_response = AsyncMock()
        health_response.status = 200
        session.get.return_value.__aenter__.return_value = health_response
        
        # Mock tool call response  
        tool_response = AsyncMock()
        tool_response.status = 200
        tool_response.json.return_value = {
            "success": True,
            "sources": [{"domain": "test.com", "document_count": 5}]
        }
        session.post.return_value.__aenter__.return_value = tool_response
        
        return session
    
    @pytest.mark.asyncio
    async def test_mcp_client_connection(self, mock_session):
        """Test MCP client connection and disconnection."""
        with patch('aiohttp.ClientSession', return_value=mock_session):
            client = MCPClient("http://localhost:8051")
            
            # Test connection
            await client.connect()
            assert client.is_connected
            
            # Test disconnection
            await client.disconnect()
            assert not client.is_connected
    
    @pytest.mark.asyncio
    async def test_mcp_client_tool_call(self, mock_session):
        """Test MCP client tool calling."""
        with patch('aiohttp.ClientSession', return_value=mock_session):
            client = MCPClient("http://localhost:8051")
            await client.connect()
            
            # Test tool call
            result = await client._call_tool("get_available_sources")
            
            assert result["success"] is True
            assert "sources" in result
            mock_session.post.assert_called_once()
    
    def test_mcp_client_sync_methods(self, mock_session):
        """Test synchronous wrapper methods."""
        with patch('aiohttp.ClientSession', return_value=mock_session):
            client = MCPClient("http://localhost:8051")
            
            # Test sync get_available_sources
            result = client.get_available_sources()
            assert isinstance(result, dict)
            
            # Test sync perform_rag_query
            mock_session.post.return_value.__aenter__.return_value.json.return_value = {
                "success": True,
                "response": "Test response",
                "sources": []
            }
            
            result = client.perform_rag_query("test query")
            assert isinstance(result, dict)
            assert "response" in result
    
    @pytest.mark.asyncio
    async def test_mcp_client_error_handling(self):
        """Test MCP client error handling."""
        # Test connection failure
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.get.side_effect = Exception("Connection failed")
            mock_session_class.return_value = mock_session
            
            client = MCPClient("http://localhost:8051")
            
            with pytest.raises(Exception):
                await client.connect()
            
            assert not client.is_connected
    
    def test_global_client_singleton(self):
        """Test global client singleton pattern."""
        client1 = get_mcp_client()
        client2 = get_mcp_client()
        
        assert client1 is client2


class TestTUIIntegrationWithMCP:
    """Test TUI integration with MCP client."""
    
    @pytest.fixture
    def mock_mcp_responses(self):
        """Mock MCP responses for testing."""
        return {
            "get_available_sources": {
                "success": True,
                "sources": [
                    {"domain": "example.com", "document_count": 10},
                    {"domain": "test.org", "document_count": 5}
                ]
            },
            "perform_rag_query": {
                "success": True,
                "response": "This is a test response from the knowledge base.",
                "sources": [
                    {"title": "Test Document", "url": "https://example.com/doc1"}
                ]
            },
            "crawl_single_page": {
                "success": True,
                "message": "Successfully crawled page",
                "documents_added": 1
            },
            "smart_crawl_url": {
                "success": True,
                "message": "Successfully crawled URL",
                "documents_added": 5,
                "pages_crawled": 5
            }
        }
    
    def test_tui_app_with_mcp_connection(self, mock_mcp_responses):
        """Test TUI app initialization with MCP connection."""
        with patch('tui.mcp_client.MCPClient.test_connection', return_value=True):
            app = AYKnowledgeBaseTUI()
            app.check_connections()
            
            assert app.mcp_connected is True
            assert app.db_connected is True
            
            status = app.get_connection_status()
            assert "Connected" in status
    
    def test_tui_app_with_mcp_failure(self):
        """Test TUI app with MCP connection failure."""
        with patch('tui.mcp_client.MCPClient.test_connection', return_value=False):
            app = AYKnowledgeBaseTUI()
            app.check_connections()
            
            assert app.mcp_connected is False
            
            status = app.get_connection_status()
            assert "Disconnected" in status
    
    def test_chat_screen_sources_command(self, mock_mcp_responses):
        """Test chat screen /sources command with MCP integration."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen._app = app  # Use private attribute to avoid setter issue
        
        # Mock the add_message method
        screen.add_message = Mock()
        
        # Mock MCP client response
        with patch('tui.mcp_client.get_available_sources', 
                   return_value=mock_mcp_responses["get_available_sources"]):
            
            screen.show_sources_info()
            
            # Verify message was added with correct content
            screen.add_message.assert_called_once()
            call_args = screen.add_message.call_args[0][0]
            
            assert call_args["role"] == "assistant"
            assert "Available Sources (2)" in call_args["content"]
            assert "example.com" in call_args["content"]
            assert "test.org" in call_args["content"]
    
    @pytest.mark.asyncio
    async def test_chat_screen_rag_query(self, mock_mcp_responses):
        """Test chat screen RAG query processing."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen._app = app
        
        # Mock UI components
        loading_mock = Mock()
        loading_mock.visible = False
        screen.query_one = Mock(return_value=loading_mock)
        screen.add_message = Mock()
        screen.set_focus = Mock()
        
        # Mock MCP client response
        with patch('tui.mcp_client.perform_rag_query', 
                   return_value=mock_mcp_responses["perform_rag_query"]):
            
            await screen.process_query("test query")
            
            # Verify loading state was managed
            assert loading_mock.visible == False  # Should be reset
            
            # Verify message was added
            screen.add_message.assert_called()
            call_args = screen.add_message.call_args[0][0]
            
            assert call_args["role"] == "assistant"
            assert "test response" in call_args["content"].lower()
    
    @pytest.mark.asyncio
    async def test_chat_screen_rag_query_error(self):
        """Test chat screen RAG query error handling."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen._app = app
        
        # Mock UI components
        loading_mock = Mock()
        loading_mock.visible = False
        screen.query_one = Mock(return_value=loading_mock)
        screen.add_message = Mock()
        screen.set_focus = Mock()
        
        # Mock MCP client error
        with patch('tui.mcp_client.perform_rag_query', 
                   side_effect=Exception("Connection failed")):
            
            await screen.process_query("test query")
            
            # Verify error message was added
            screen.add_message.assert_called()
            call_args = screen.add_message.call_args[0][0]
            
            assert call_args["role"] == "assistant"
            assert "Error processing query" in call_args["content"]
            assert "Connection failed" in call_args["content"]
    
    def test_backward_compatibility_functions(self, mock_mcp_responses):
        """Test backward compatibility wrapper functions."""
        with patch('tui.mcp_client.MCPClient') as mock_client_class:
            mock_client = Mock()
            mock_client.get_available_sources.return_value = mock_mcp_responses["get_available_sources"]
            mock_client.perform_rag_query.return_value = mock_mcp_responses["perform_rag_query"]
            mock_client_class.return_value = mock_client
            
            # Test backward compatibility imports
            from tui.mcp_client import (
                get_available_sources,
                perform_rag_query,
                crawl_single_page,
                smart_crawl_url
            )
            
            # Test that functions work as expected
            sources_result = get_available_sources()
            assert sources_result["success"] is True
            
            rag_result = perform_rag_query("test query")
            assert rag_result["success"] is True
    
    def test_mcp_client_json_parsing(self):
        """Test MCP client JSON response parsing."""
        client = MCPClient()
        
        # Test parsing string JSON response
        with patch.object(client, '_call_tool') as mock_call:
            mock_call.return_value = '{"success": true, "sources": []}'
            
            # This would fail in async context, but test the concept
            # In real implementation, this is handled in the async methods
    
    @pytest.mark.asyncio
    async def test_mcp_client_auto_connect(self, mock_mcp_responses):
        """Test MCP client auto-connection on first use."""
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            
            # Mock health check
            health_response = AsyncMock()
            health_response.status = 200
            mock_session.get.return_value.__aenter__.return_value = health_response
            
            # Mock tool response
            tool_response = AsyncMock()
            tool_response.status = 200
            tool_response.json.return_value = mock_mcp_responses["get_available_sources"]
            mock_session.post.return_value.__aenter__.return_value = tool_response
            
            mock_session_class.return_value = mock_session
            
            client = MCPClient("http://localhost:8051")
            
            # Client should auto-connect when calling async method
            result = await client._get_available_sources_async()
            
            assert result["success"] is True
            assert client.is_connected


class TestTUIErrorRecovery:
    """Test TUI error recovery and resilience."""
    
    def test_graceful_degradation_no_mcp(self):
        """Test graceful degradation when MCP is unavailable."""
        with patch('tui.mcp_client.MCPClient.test_connection', return_value=False):
            app = AYKnowledgeBaseTUI()
            app.check_connections()
            
            assert app.mcp_connected is False
            status = app.get_connection_status()
            assert "Disconnected" in status
    
    def test_error_message_formatting(self):
        """Test error message formatting in TUI."""
        app = AYKnowledgeBaseTUI()
        screen = ChatScreen()
        screen._app = app
        screen.add_message = Mock()
        
        # Test network error handling
        with patch('tui.mcp_client.get_available_sources', 
                   side_effect=Exception("Network timeout")):
            
            screen.show_sources_info()
            
            screen.add_message.assert_called_once()
            call_args = screen.add_message.call_args[0][0]
            
            assert call_args["role"] == "assistant"
            assert "Error fetching sources" in call_args["content"]
            assert "Network timeout" in call_args["content"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])