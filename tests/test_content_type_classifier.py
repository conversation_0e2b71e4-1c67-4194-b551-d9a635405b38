"""
Comprehensive test suite for content type classification system
"""

import pytest
from src.content_type_classifier import (
    ContentTypeClassifier,
    AdaptiveFilterManager,
    ContentType,
    FilterStrategy,
    ContentClassification,
    FilterConfiguration
)


class TestContentTypeClassifier:
    """Test content type classification functionality"""
    
    def setup_method(self):
        self.classifier = ContentTypeClassifier()
    
    def test_marketing_page_detection(self):
        """Test detection of marketing/sales pages"""
        marketing_content = """
        # Welcome to Our Amazing Product!
        
        ## Why Choose Us?
        - Industry leader in innovation
        - Trusted by thousands of customers
        - Award-winning customer service
        
        ### Pricing Plans
        - Basic: $9.99/month
        - Pro: $19.99/month  
        - Enterprise: Contact sales
        
        [Get Started Now!](signup.html)
        [Contact Sales](contact.html)
        
        Our mission is to provide the best solutions for your business needs.
        Join thousands of satisfied customers who trust our platform.
        """
        
        classification = self.classifier.classify_content(
            marketing_content, 
            url="https://example.com/pricing"
        )
        
        assert classification.content_type == ContentType.MARKETING
        assert classification.confidence_score > 0.5
        assert classification.strategy == FilterStrategy.STRICT
        assert any('pricing' in indicator for indicator in classification.indicators)
    
    def test_documentation_page_detection(self):
        """Test detection of documentation pages"""
        docs_content = """
        # API Reference
        
        ## Getting Started
        
        This guide will help you get started with our API.
        
        ### Installation
        
        ```bash
        npm install our-api-client
        ```
        
        ### Usage Example
        
        ```javascript
        const client = new ApiClient({
            apiKey: 'your-api-key'
        });
        
        const result = await client.getData();
        console.log(result);
        ```
        
        ## API Methods
        
        ### getData()
        
        Returns user data from the API.
        
        **Parameters:**
        - `userId` (string): The user ID
        - `options` (object): Optional configuration
        
        **Returns:**
        - Promise resolving to user data object
        """
        
        classification = self.classifier.classify_content(
            docs_content,
            url="https://docs.example.com/api"
        )
        
        assert classification.content_type == ContentType.DOCUMENTATION
        assert classification.confidence_score > 0.4
        assert classification.strategy == FilterStrategy.BALANCED
        assert any('code_blocks' in indicator for indicator in classification.indicators)
    
    def test_tutorial_page_detection(self):
        """Test detection of tutorial pages"""
        tutorial_content = """
        # Step-by-Step Tutorial: Building a Todo App
        
        In this tutorial, you will learn how to build a complete todo application.
        By the end of this walkthrough, you'll have a working app with full CRUD functionality.
        
        ## Step 1: Setting Up the Project
        
        Let's start by creating a new project directory.
        
        ```bash
        mkdir todo-app
        cd todo-app
        npm init -y
        ```
        
        ## Step 2: Installing Dependencies
        
        We'll need React and some additional packages.
        
        ```bash
        npm install react react-dom
        npm install --save-dev webpack webpack-cli
        ```
        
        ## Step 3: Creating the App Component
        
        Now let's build our main App component:
        
        ```jsx
        import React, { useState } from 'react';
        
        function App() {
            const [todos, setTodos] = useState([]);
            
            const addTodo = (text) => {
                setTodos([...todos, { id: Date.now(), text, completed: false }]);
            };
            
            return (
                <div>
                    <h1>Todo App</h1>
                    {/* Component JSX here */}
                </div>
            );
        }
        
        export default App;
        ```
        
        Continue to the next step to add more functionality...
        """
        
        classification = self.classifier.classify_content(
            tutorial_content,
            url="https://learn.example.com/react-tutorial"
        )
        
        assert classification.content_type == ContentType.TUTORIAL
        assert classification.confidence_score > 0.5
        assert classification.strategy == FilterStrategy.LENIENT
        assert any('steps' in indicator for indicator in classification.indicators)
    
    def test_github_repo_detection(self):
        """Test detection of GitHub repository pages"""
        github_content = """
        # My Awesome Library
        
        A powerful JavaScript library for data visualization.
        
        ## Installation
        
        ```bash
        npm install my-awesome-library
        ```
        
        You can also clone this repository:
        
        ```bash
        git clone https://github.com/user/my-awesome-library.git
        cd my-awesome-library
        npm install
        ```
        
        ## Usage
        
        ```javascript
        import { Chart } from 'my-awesome-library';
        
        const chart = new Chart({
            element: '#chart',
            data: myData
        });
        ```
        
        ## Contributing
        
        Pull requests are welcome! Please read our contributing guidelines.
        
        ## License
        
        MIT License - see LICENSE file for details.
        """
        
        classification = self.classifier.classify_content(
            github_content,
            url="https://github.com/user/my-awesome-library"
        )
        
        assert classification.content_type == ContentType.GITHUB_REPO
        assert classification.confidence_score > 0.4
        assert classification.strategy == FilterStrategy.CONSISTENT
    
    def test_blog_post_detection(self):
        """Test detection of blog posts"""
        blog_content = """
        # My Experience with React Hooks
        
        Published on March 15, 2024 by John Doe
        
        In this post, I want to share my thoughts on React Hooks and how they've 
        changed the way I write React components.
        
        ## What I Learned
        
        When I first started using hooks, I was skeptical. But after working with them
        for several months, I think they're a game-changer.
        
        ### useState is Powerful
        
        Here's a simple example of how I use useState:
        
        ```jsx
        function Counter() {
            const [count, setCount] = useState(0);
            
            return (
                <div>
                    <p>Count: {count}</p>
                    <button onClick={() => setCount(count + 1)}>
                        Increment
                    </button>
                </div>
            );
        }
        ```
        
        In my opinion, this is much cleaner than class components.
        
        ## Conclusion
        
        What I learned from this experience is that embracing new patterns
        can lead to better code. I highly recommend giving hooks a try.
        """
        
        classification = self.classifier.classify_content(
            blog_content,
            url="https://myblog.com/2024/03/react-hooks-experience"
        )
        
        assert classification.content_type == ContentType.BLOG
        assert classification.confidence_score > 0.3
        assert classification.strategy == FilterStrategy.BALANCED
    
    def test_api_reference_detection(self):
        """Test detection of API reference pages"""
        api_content = """
        # User API Endpoints
        
        ## GET /api/users
        
        Retrieve a list of users.
        
        ### Parameters
        
        - `page` (number): Page number for pagination
        - `limit` (number): Number of users per page
        - `filter` (string): Filter criteria
        
        ### Response
        
        ```json
        {
            "users": [
                {
                    "id": 1,
                    "name": "John Doe",
                    "email": "<EMAIL>"
                }
            ],
            "pagination": {
                "page": 1,
                "limit": 10,
                "total": 100
            }
        }
        ```
        
        ### Example Request
        
        ```bash
        curl -X GET "https://api.example.com/users?page=1&limit=10" \
             -H "Authorization: Bearer YOUR_TOKEN"
        ```
        
        ## POST /api/users
        
        Create a new user.
        
        ### Request Body
        
        ```json
        {
            "name": "Jane Doe",
            "email": "<EMAIL>"
        }
        ```
        """
        
        classification = self.classifier.classify_content(
            api_content,
            url="https://api-docs.example.com/users"
        )
        
        assert classification.content_type == ContentType.API_REFERENCE
        assert classification.confidence_score > 0.4
        assert classification.strategy == FilterStrategy.LENIENT
    
    def test_unknown_content_classification(self):
        """Test classification of ambiguous content"""
        ambiguous_content = """
        # Some Random Content
        
        This is just some random text that doesn't clearly fit into
        any specific category. It has some words and sentences but
        no clear indicators of what type of content it is.
        
        There might be some code here:
        
        ```
        var x = 1;
        console.log(x);
        ```
        
        But it's not clear what the purpose is.
        """
        
        classification = self.classifier.classify_content(ambiguous_content)
        
        # Should default to unknown with balanced strategy
        assert classification.strategy == FilterStrategy.BALANCED
        assert classification.confidence_score < 0.7  # Low confidence for ambiguous content
    
    def test_code_density_calculation(self):
        """Test code density calculation"""
        high_code_content = """
        ```python
        def hello():
            print("Hello")
        ```
        
        ```javascript
        function world() {
            console.log("World");
        }
        ```
        
        Some text here.
        """
        
        low_code_content = """
        This is a long paragraph with lots of text and very little code.
        We might have some `inline code` but mostly it's just regular text.
        There are many sentences and paragraphs explaining concepts without
        showing actual code examples. This represents content that has
        minimal code density.
        """
        
        high_density = self.classifier._calculate_code_density(high_code_content)
        low_density = self.classifier._calculate_code_density(low_code_content)
        
        assert high_density > low_density
        assert high_density > 0.2
        assert low_density < 0.1
    
    def test_filter_configuration_retrieval(self):
        """Test getting filter configuration for different content types"""
        marketing_classification = ContentClassification(
            content_type=ContentType.MARKETING,
            confidence_score=0.8,
            strategy=FilterStrategy.STRICT,
            indicators=['pricing', 'cta'],
            metrics={}
        )
        
        tutorial_classification = ContentClassification(
            content_type=ContentType.TUTORIAL,
            confidence_score=0.7,
            strategy=FilterStrategy.LENIENT,
            indicators=['steps', 'tutorial'],
            metrics={}
        )
        
        marketing_config = self.classifier.get_filter_config(marketing_classification)
        tutorial_config = self.classifier.get_filter_config(tutorial_classification)
        
        # Marketing should have stricter filtering
        assert marketing_config.confidence_threshold > tutorial_config.confidence_threshold
        assert marketing_config.min_code_indicators >= tutorial_config.min_code_indicators
        assert marketing_config.navigation_penalty > tutorial_config.navigation_penalty
    
    def test_strict_filtering_determination(self):
        """Test determination of when to apply strict filtering"""
        high_confidence_marketing = ContentClassification(
            content_type=ContentType.MARKETING,
            confidence_score=0.8,
            strategy=FilterStrategy.STRICT,
            indicators=[],
            metrics={}
        )
        
        low_confidence_marketing = ContentClassification(
            content_type=ContentType.MARKETING,
            confidence_score=0.4,
            strategy=FilterStrategy.STRICT,
            indicators=[],
            metrics={}
        )
        
        doc_classification = ContentClassification(
            content_type=ContentType.DOCUMENTATION,
            confidence_score=0.8,
            strategy=FilterStrategy.BALANCED,
            indicators=[],
            metrics={}
        )
        
        assert self.classifier.should_apply_strict_filtering(high_confidence_marketing)
        assert not self.classifier.should_apply_strict_filtering(low_confidence_marketing)
        assert not self.classifier.should_apply_strict_filtering(doc_classification)


class TestAdaptiveFilterManager:
    """Test adaptive filter management functionality"""
    
    def setup_method(self):
        self.manager = AdaptiveFilterManager()
    
    def test_adaptive_filter_config_generation(self):
        """Test generation of adaptive filter configuration"""
        content = """
        # API Documentation
        
        ```python
        import requests
        
        response = requests.get('https://api.example.com/data')
        print(response.json())
        ```
        """
        
        classification, config = self.manager.get_adaptive_filter_config(
            content, 
            url="https://docs.example.com/api"
        )
        
        assert isinstance(classification, ContentClassification)
        assert isinstance(config, FilterConfiguration)
        assert len(self.manager.performance_history) == 1
    
    def test_manual_review_recommendation(self):
        """Test manual review recommendation logic"""
        # Low confidence classification should recommend review
        low_confidence_classification = ContentClassification(
            content_type=ContentType.UNKNOWN,
            confidence_score=0.2,
            strategy=FilterStrategy.BALANCED,
            indicators=[],
            metrics={}
        )
        
        # High confidence classification should not recommend review
        high_confidence_classification = ContentClassification(
            content_type=ContentType.DOCUMENTATION,
            confidence_score=0.8,
            strategy=FilterStrategy.BALANCED,
            indicators=[],
            metrics={}
        )
        
        # Mock filter result
        class MockFilterResult:
            def __init__(self, is_valid, confidence):
                self.is_valid_code = is_valid
                self.confidence_score = confidence
        
        should_review_low = self.manager.should_review_manually(
            low_confidence_classification, 
            MockFilterResult(True, 0.5)
        )
        
        should_review_high = self.manager.should_review_manually(
            high_confidence_classification,
            MockFilterResult(True, 0.8)
        )
        
        assert should_review_low
        assert not should_review_high
    
    def test_performance_report_generation(self):
        """Test performance report generation"""
        # Add some mock performance history
        for i in range(5):
            classification = ContentClassification(
                content_type=ContentType.DOCUMENTATION,
                confidence_score=0.6 + (i * 0.1),
                strategy=FilterStrategy.BALANCED,
                indicators=['docs'],
                metrics={}
            )
            self.manager.performance_history.append(classification)
        
        report = self.manager.get_performance_report()
        
        assert 'total_classifications' in report
        assert report['total_classifications'] == 5
        assert 'content_type_distribution' in report
        assert 'strategy_distribution' in report
        assert 'confidence_stats' in report
        
        # Check confidence statistics
        assert report['confidence_stats']['average'] > 0.6
        assert report['confidence_stats']['minimum'] >= 0.6
        assert report['confidence_stats']['maximum'] <= 1.0


class TestContentTypeIntegration:
    """Integration tests for content type classification"""
    
    def setup_method(self):
        self.classifier = ContentTypeClassifier()
        self.manager = AdaptiveFilterManager(self.classifier)
    
    def test_end_to_end_classification_workflow(self):
        """Test complete workflow from content to filter configuration"""
        test_cases = [
            {
                'content': """
                # Buy Our Premium Product Now!
                Special offer: 50% off for new customers.
                [Sign Up Today!](/signup)
                """,
                'url': 'https://example.com/pricing',
                'expected_type': ContentType.MARKETING,
                'expected_strategy': FilterStrategy.STRICT
            },
            {
                'content': """
                # Step 1: Install the Library
                ```bash
                npm install example-lib
                ```
                Now let's create our first component...
                """,
                'url': 'https://tutorial.example.com/step-1',
                'expected_type': ContentType.TUTORIAL,
                'expected_strategy': FilterStrategy.LENIENT
            },
            {
                'content': """
                # User API Endpoints
                
                ## GET /api/users/{id}
                ## POST /api/users
                ## DELETE /api/users/{id}
                
                ### Parameters
                - id: User identifier
                
                ### Response
                ```json
                {"id": 1, "name": "John"}
                ```
                
                ### Example Request
                ```bash
                curl -X GET "https://api.example.com/users/1" \
                     -H "Authorization: Bearer YOUR_TOKEN"
                ```
                """,
                'url': 'https://api-docs.example.com/users',
                'expected_type': ContentType.API_REFERENCE,
                'expected_strategy': FilterStrategy.LENIENT
            }
        ]
        
        for case in test_cases:
            classification, config = self.manager.get_adaptive_filter_config(
                case['content'],
                case['url']
            )
            
            assert classification.content_type == case['expected_type']
            assert classification.strategy == case['expected_strategy']
            assert isinstance(config, FilterConfiguration)
            assert config.confidence_threshold > 0
            assert config.min_code_indicators > 0
    
    def test_performance_metrics_collection(self):
        """Test that performance metrics are collected correctly"""
        initial_count = len(self.manager.performance_history)
        
        # Process multiple different content types
        content_samples = [
            ("Marketing content with pricing info", "https://example.com/pricing"),
            ("Documentation with code examples", "https://docs.example.com/api"),
            ("Tutorial with step-by-step guide", "https://learn.example.com/tutorial"),
            ("GitHub repository readme", "https://github.com/user/repo"),
            ("Blog post about programming", "https://blog.example.com/post")
        ]
        
        for content, url in content_samples:
            self.manager.get_adaptive_filter_config(content, url)
        
        final_count = len(self.manager.performance_history)
        
        assert final_count == initial_count + len(content_samples)
        
        # Verify performance metrics
        performance_metrics = self.classifier.get_performance_metrics(
            self.manager.performance_history
        )
        
        assert 'total_classifications' in performance_metrics
        assert 'content_type_distribution' in performance_metrics
        assert 'confidence_stats' in performance_metrics
        assert performance_metrics['total_classifications'] >= len(content_samples)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])