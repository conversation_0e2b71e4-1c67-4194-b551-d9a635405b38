#!/usr/bin/env python3
"""
Simple test script to validate the implemented features.
Following KISS principle - basic functional tests only.
"""
import sys
import os
sys.path.append('src')

from github_client import SimpleGitHubClient, create_github_enhanced_content
from circuit_breaker import SimpleCircuitBreaker, get_circuit_breaker
from query_enhancer import SimpleQueryEnhancer


def test_github_client():
    """Test GitHub client functionality."""
    print("Testing GitHub Client...")
    
    client = SimpleGitHubClient()
    
    # Test URL detection
    assert client.is_github_url("https://github.com/user/repo"), "GitHub URL detection failed"
    assert not client.is_github_url("https://example.com"), "Non-GitHub URL incorrectly detected"
    
    # Test repo info extraction
    repo_info = client.extract_repo_info("https://github.com/user/repo")
    assert repo_info == ("user", "repo"), f"Repo info extraction failed: {repo_info}"
    
    print("✅ GitHub Client tests passed")


def test_circuit_breaker():
    """Test circuit breaker functionality."""
    print("Testing Circuit Breaker...")
    
    # Test basic circuit breaker creation
    cb = SimpleCircuitBreaker(failure_threshold=2, recovery_timeout=1)
    assert cb.state.value == "closed", "Circuit breaker should start closed"
    
    # Test global circuit breaker registry
    cb1 = get_circuit_breaker("test_service")
    cb2 = get_circuit_breaker("test_service")
    assert cb1 is cb2, "Circuit breaker registry should return same instance"
    
    print("✅ Circuit Breaker tests passed")


def test_query_enhancer():
    """Test query enhancement functionality."""
    print("Testing Query Enhancer...")
    
    enhancer = SimpleQueryEnhancer()
    
    # Test basic query expansion
    variations = enhancer.expand_query("rate limit error")
    assert len(variations) > 1, "Query expansion should generate variations"
    assert "rate limit error" in variations, "Original query should be included"
    
    # Test enhanced search query
    enhanced = enhancer.enhance_search_query("api authentication")
    assert "primary_query" in enhanced, "Enhanced query should have primary_query"
    assert "variations" in enhanced, "Enhanced query should have variations"
    
    print("✅ Query Enhancer tests passed")


def test_integration():
    """Test that components work together."""
    print("Testing Integration...")
    
    # Test GitHub content enhancement
    repo_info = {
        'name': 'test-repo',
        'description': 'A test repository',
        'language': 'Python',
        'stars': 100,
        'topics': ['test', 'demo'],
        'url': 'https://github.com/user/test-repo'
    }
    
    enhanced_content = create_github_enhanced_content(
        "https://github.com/user/test-repo",
        "# Test README\n\nThis is a test.",
        repo_info
    )
    
    assert "test-repo" in enhanced_content, "Enhanced content should include repo name"
    assert "Python" in enhanced_content, "Enhanced content should include language"
    
    print("✅ Integration tests passed")


def main():
    """Run all tests."""
    print("🧪 Running Implementation Validation Tests\n")
    
    try:
        test_github_client()
        test_circuit_breaker()
        test_query_enhancer()
        test_integration()
        
        print("\n🎉 All tests passed! Implementation is working correctly.")
        print("\n📋 Summary of implemented features:")
        print("   ✅ GitHub API integration for better repository crawling")
        print("   ✅ Circuit breaker pattern for Docker service resilience")
        print("   ✅ Enhanced query expansion for better search relevance")
        print("   ✅ Improved error handling with fallback strategies")
        print("   ✅ Enhanced health monitoring for Docker service")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())