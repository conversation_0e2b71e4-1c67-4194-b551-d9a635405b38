#!/usr/bin/env python3
"""
Dashboard server for AY Knowledge Base.
Provides HTTP API endpoints for the web dashboard.
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List
import aiohttp
from aiohttp import web, web_request
from aiohttp.web_response import Response
import aiohttp_cors

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from mcp_server import MCPCrawl4aiRAGServer
from utils.redis_queue import RedisQueue

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DashboardServer:
    """HTTP server for the web dashboard."""
    
    def __init__(self, port: int = 8080):
        self.port = port
        self.app = web.Application()
        self.mcp_server = MCPCrawl4aiRAGServer()
        self.redis_queue = RedisQueue()
        self.setup_routes()
        self.setup_cors()
    
    def setup_cors(self):
        """Setup CORS for frontend access."""
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    def setup_routes(self):
        """Setup HTTP routes."""
        # Static files
        self.app.router.add_get('/', self.serve_dashboard)
        self.app.router.add_static('/', path='.', name='static')
        
        # API endpoints
        self.app.router.add_post('/api/crawl/url', self.crawl_url)
        self.app.router.add_post('/api/crawl/search', self.crawl_search)
        self.app.router.add_get('/api/queue/status', self.get_queue_status)
        self.app.router.add_post('/api/queue/clear', self.clear_queue)
        self.app.router.add_post('/api/chat', self.chat_query)
        self.app.router.add_get('/api/sources', self.get_sources)
        self.app.router.add_get('/api/stats', self.get_stats)
    
    async def serve_dashboard(self, request: web_request) -> Response:
        """Serve the main dashboard HTML."""
        try:
            with open('dashboard.html', 'r') as f:
                content = f.read()
            return web.Response(text=content, content_type='text/html')
        except FileNotFoundError:
            return web.Response(text="Dashboard not found", status=404)
    
    async def crawl_url(self, request: web_request) -> Response:
        """Start URL crawling."""
        try:
            data = await request.json()
            url = data.get('url')
            max_depth = data.get('max_depth', 3)
            
            if not url:
                return web.json_response({'error': 'URL is required'}, status=400)
            
            # Add to Redis queue
            task_id = await self.redis_queue.add_task({
                'type': 'crawl_url',
                'url': url,
                'max_depth': max_depth,
                'status': 'pending'
            })
            
            logger.info(f"Added URL crawl task: {url}")
            
            return web.json_response({
                'success': True,
                'task_id': task_id,
                'message': f'Crawl started for {url}'
            })
            
        except Exception as e:
            logger.error(f"Error in crawl_url: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def crawl_search(self, request: web_request) -> Response:
        """Start search-based crawling."""
        try:
            data = await request.json()
            query = data.get('query')
            max_results = data.get('max_results', 10)
            
            if not query:
                return web.json_response({'error': 'Query is required'}, status=400)
            
            # Add to Redis queue
            task_id = await self.redis_queue.add_task({
                'type': 'crawl_search',
                'query': query,
                'max_results': max_results,
                'status': 'pending'
            })
            
            logger.info(f"Added search crawl task: {query}")
            
            return web.json_response({
                'success': True,
                'task_id': task_id,
                'message': f'Search crawl started for "{query}"'
            })
            
        except Exception as e:
            logger.error(f"Error in crawl_search: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def get_queue_status(self, request: web_request) -> Response:
        """Get current queue status."""
        try:
            tasks = await self.redis_queue.get_all_tasks()
            
            return web.json_response({
                'success': True,
                'tasks': tasks,
                'total': len(tasks)
            })
            
        except Exception as e:
            logger.error(f"Error getting queue status: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def clear_queue(self, request: web_request) -> Response:
        """Clear completed tasks from queue."""
        try:
            cleared = await self.redis_queue.clear_completed()
            
            return web.json_response({
                'success': True,
                'cleared': cleared,
                'message': f'Cleared {cleared} completed tasks'
            })
            
        except Exception as e:
            logger.error(f"Error clearing queue: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def chat_query(self, request: web_request) -> Response:
        """Handle chat queries."""
        try:
            data = await request.json()
            message = data.get('message')
            
            if not message:
                return web.json_response({'error': 'Message is required'}, status=400)
            
            # Use MCP server to perform RAG query
            # This would connect to your existing RAG functionality
            response = f"Based on your knowledge base, here's what I found about '{message}'..."
            
            return web.json_response({
                'success': True,
                'response': response,
                'sources': []  # Add relevant sources here
            })
            
        except Exception as e:
            logger.error(f"Error in chat_query: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def get_sources(self, request: web_request) -> Response:
        """Get available sources."""
        try:
            # This would connect to your existing source management
            sources = [
                {
                    'domain': 'example.com',
                    'documents': 15,
                    'last_crawl': '2 hours ago',
                    'status': 'active'
                },
                {
                    'domain': 'docs.python.org',
                    'documents': 42,
                    'last_crawl': '1 day ago',
                    'status': 'active'
                }
            ]
            
            return web.json_response({
                'success': True,
                'sources': sources,
                'total': len(sources)
            })
            
        except Exception as e:
            logger.error(f"Error getting sources: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def get_stats(self, request: web_request) -> Response:
        """Get system statistics."""
        try:
            # Get real stats from your systems
            stats = {
                'total_documents': 127,
                'total_sources': 8,
                'active_tasks': await self.redis_queue.get_active_count(),
                'redis_status': 'connected',
                'mcp_status': 'active'
            }
            
            return web.json_response({
                'success': True,
                'stats': stats
            })
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return web.json_response({'error': str(e)}, status=500)
    
    async def start(self):
        """Start the dashboard server."""
        logger.info(f"Starting AY Knowledge Base Dashboard on port {self.port}")
        logger.info(f"Dashboard URL: http://localhost:{self.port}")
        
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        
        return runner


async def main():
    """Main entry point."""
    server = DashboardServer(port=8080)
    runner = await server.start()
    
    try:
        print("🚀 AY Knowledge Base Dashboard Server Started!")
        print("📊 Dashboard: http://localhost:8080")
        print("🔌 API: http://localhost:8080/api/")
        print("Press Ctrl+C to stop...")
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down dashboard server...")
        await runner.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
