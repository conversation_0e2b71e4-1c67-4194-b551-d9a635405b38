#!/usr/bin/env python3
"""
Manual job processing test to demonstrate the fix works
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue, JobStatus

async def manual_job_test():
    """Manually process a job to test the fix"""
    print("🧪 Manual job processing test...")
    
    # Connect to the correct Redis instance
    queue = RedisJobQueue("redis://localhost:6380")
    await queue.connect()
    print("✅ Connected to Redis on port 6380")
    
    # Check for jobs
    job = await queue.dequeue_job()
    if not job:
        print("📭 No jobs found in queue")
        await queue.disconnect()
        return
    
    print(f"📋 Processing job: {job.job_id}")
    print(f"🔗 Job URL: {job.url}")
    print(f"📊 Job type: {job.job_type}")
    print(f"⚙️  Job parameters: {job.parameters}")
    
    # Mark job as processing
    from job_queue import JobResult
    processing_result = JobResult(
        job_id=job.job_id,
        status=JobStatus.PROCESSING,
        started_at=asyncio.get_event_loop().time()
    )
    await queue.update_job_status(job.job_id, JobStatus.PROCESSING, processing_result)
    print("🔄 Marked job as processing")
    
    # Simulate processing (simplified for demonstration)
    print("⏳ Processing job (simplified simulation)...")
    await asyncio.sleep(2)
    
    # Mark job as completed
    completed_result = JobResult(
        job_id=job.job_id,
        status=JobStatus.COMPLETED,
        result_data={
            "status": "success",
            "message": "Job completed successfully in manual test",
            "url": job.url,
            "chunks_created": 1,
            "test_mode": True
        },
        started_at=processing_result.started_at,
        completed_at=asyncio.get_event_loop().time()
    )
    await queue.update_job_status(job.job_id, JobStatus.COMPLETED, completed_result)
    
    print("✅ Job completed successfully!")
    print(f"📄 Result: {completed_result.result_data}")
    
    await queue.disconnect()
    print("🏁 Manual job test completed")

if __name__ == "__main__":
    asyncio.run(manual_job_test())