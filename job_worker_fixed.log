[07/21/25 22:53:58] INFO     Connected to Redis at              job_queue.py:127
                             redis://localhost:6379                             
                    INFO     Worker test-worker-3 initialized   job_worker.py:66
                             successfully                                       
                    INFO     Starting worker test-worker-3 with job_worker.py:79
                             3 concurrent tasks                                 
                    INFO     Starting worker loop 0 for        job_worker.py:126
                             test-worker-3                                      
                    INFO     Starting worker loop 1 for        job_worker.py:126
                             test-worker-3                                      
                    INFO     Starting worker loop 2 for        job_worker.py:126
                             test-worker-3                                      
[07/21/25 22:55:08] INFO     Received shutdown signal for      job_worker.py:468
                             worker test-worker-3                               
                    INFO     Received shutdown signal for      job_worker.py:468
                             worker test-worker-3                               
                    INFO     Shutting down worker              job_worker.py:107
                             test-worker-3                                      
                    INFO     Worker loop 0 cancelled           job_worker.py:144
                    INFO     Worker loop 1 cancelled           job_worker.py:144
                    INFO     Worker loop 2 cancelled           job_worker.py:144
