#!/usr/bin/env python3
"""
Redis Integration Test for AY RAG MCP Server
Tests the Redis queue functionality and generates performance report
"""

import asyncio
import json
import time
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from job_queue import RedisJobQueue, JobRequest, JobStatus
    REDIS_AVAILABLE = True
except ImportError as e:
    print(f"❌ Redis dependencies not available: {e}")
    REDIS_AVAILABLE = False


class RedisIntegrationTester:
    """Redis integration test suite"""
    
    def __init__(self):
        self.redis_url = "redis://localhost:6379"
        self.test_results = []
        
    async def test_redis_connectivity(self) -> dict:
        """Test basic Redis connectivity"""
        print("🔄 Testing Redis connectivity...")
        
        try:
            queue = RedisJobQueue(self.redis_url, "connectivity-test")
            await queue.connect()
            
            # Test basic operations
            redis_client = queue._get_redis_client()
            await redis_client.set("test_key", "test_value")
            value = await redis_client.get("test_key")
            await redis_client.delete("test_key")
            await redis_client.aclose()
            
            await queue.disconnect()
            
            result = {
                "test": "redis_connectivity",
                "status": "PASS",
                "message": "Redis connection successful",
                "details": {"value_retrieved": value == "test_value"}
            }
            print("✅ Redis connectivity test passed")
            
        except Exception as e:
            result = {
                "test": "redis_connectivity",
                "status": "FAIL", 
                "message": f"Redis connection failed: {e}",
                "details": {"error": str(e)}
            }
            print(f"❌ Redis connectivity test failed: {e}")
        
        self.test_results.append(result)
        return result
    
    async def test_job_queue_basic_operations(self) -> dict:
        """Test basic job queue operations"""
        print("🔄 Testing job queue basic operations...")
        
        try:
            queue = RedisJobQueue(self.redis_url, "test-basic-ops")
            await queue.connect()
            
            # Clean up any existing test data
            redis_client = queue._get_redis_client()
            keys_to_delete = await redis_client.keys("test-basic-ops:*")
            if keys_to_delete:
                await redis_client.delete(*keys_to_delete)
            await redis_client.aclose()
            
            # Test enqueue
            job_request = JobRequest.create(
                job_type="test_crawl",
                url="https://test.example.com",
                parameters={"test": True}
            )
            
            job_id = await queue.enqueue_job(job_request)
            
            # Verify queue stats
            stats = await queue.get_queue_stats()
            assert stats["queued_jobs"] == 1, f"Expected 1 queued job, got {stats['queued_jobs']}"
            
            # Test dequeue
            dequeued_job = await queue.dequeue_job()
            assert dequeued_job is not None, "Job dequeue returned None"
            assert dequeued_job.job_id == job_id, "Dequeued job ID mismatch"
            
            # Verify processing stats
            stats = await queue.get_queue_stats()
            assert stats["processing_jobs"] == 1, f"Expected 1 processing job, got {stats['processing_jobs']}"
            
            # Test status update
            await queue.update_job_status(job_id, JobStatus.COMPLETED, result_data={"success": True})
            
            # Verify final status
            final_status = await queue.get_job_status(job_id)
            assert final_status.status == JobStatus.COMPLETED, f"Expected COMPLETED, got {final_status.status}"
            
            await queue.disconnect()
            
            result = {
                "test": "job_queue_basic_operations",
                "status": "PASS",
                "message": "All basic operations successful",
                "details": {
                    "enqueue": True,
                    "dequeue": True,
                    "status_update": True,
                    "final_status": "COMPLETED"
                }
            }
            print("✅ Job queue basic operations test passed")
            
        except Exception as e:
            result = {
                "test": "job_queue_basic_operations",
                "status": "FAIL",
                "message": f"Basic operations failed: {e}",
                "details": {"error": str(e)}
            }
            print(f"❌ Job queue basic operations test failed: {e}")
        
        self.test_results.append(result)
        return result
    
    async def test_job_priority_ordering(self) -> dict:
        """Test job priority ordering"""
        print("🔄 Testing job priority ordering...")
        
        try:
            queue = RedisJobQueue(self.redis_url, "test-priority")
            await queue.connect()
            
            # Clean up
            redis_client = queue._get_redis_client()
            keys_to_delete = await redis_client.keys("test-priority:*")
            if keys_to_delete:
                await redis_client.delete(*keys_to_delete)
            await redis_client.aclose()
            
            # Create jobs with different priorities
            low_priority_job = JobRequest.create(
                job_type="test", url="https://low.com", parameters={}, priority=10
            )
            high_priority_job = JobRequest.create(
                job_type="test", url="https://high.com", parameters={}, priority=1
            )
            
            # Enqueue low priority first, then high priority
            await queue.enqueue_job(low_priority_job)
            await queue.enqueue_job(high_priority_job)
            
            # Dequeue should return high priority job first
            first_job = await queue.dequeue_job()
            second_job = await queue.dequeue_job()
            
            assert first_job.job_id == high_priority_job.job_id, "High priority job not dequeued first"
            assert second_job.job_id == low_priority_job.job_id, "Low priority job not dequeued second"
            
            await queue.disconnect()
            
            result = {
                "test": "job_priority_ordering",
                "status": "PASS",
                "message": "Priority ordering works correctly",
                "details": {
                    "high_priority_first": True,
                    "low_priority_second": True
                }
            }
            print("✅ Job priority ordering test passed")
            
        except Exception as e:
            result = {
                "test": "job_priority_ordering",
                "status": "FAIL",
                "message": f"Priority ordering failed: {e}",
                "details": {"error": str(e)}
            }
            print(f"❌ Job priority ordering test failed: {e}")
        
        self.test_results.append(result)
        return result
    
    async def test_concurrent_operations(self, num_jobs: int = 50) -> dict:
        """Test concurrent queue operations"""
        print(f"🔄 Testing concurrent operations ({num_jobs} jobs)...")
        
        try:
            queue = RedisJobQueue(self.redis_url, "test-concurrent")
            await queue.connect()
            
            # Clean up
            redis_client = queue._get_redis_client()
            keys_to_delete = await redis_client.keys("test-concurrent:*")
            if keys_to_delete:
                await redis_client.delete(*keys_to_delete)
            await redis_client.aclose()
            
            # Create jobs
            jobs = [
                JobRequest.create(
                    job_type="concurrent_test",
                    url=f"https://test{i}.com",
                    parameters={}
                ) for i in range(num_jobs)
            ]
            
            # Measure enqueue performance
            start_time = time.time()
            enqueue_tasks = [queue.enqueue_job(job) for job in jobs]
            await asyncio.gather(*enqueue_tasks)
            enqueue_time = time.time() - start_time
            
            # Verify all jobs are queued
            stats = await queue.get_queue_stats()
            assert stats["queued_jobs"] == num_jobs, f"Expected {num_jobs} queued, got {stats['queued_jobs']}"
            
            # Measure dequeue performance
            start_time = time.time()
            dequeue_tasks = [queue.dequeue_job() for _ in range(num_jobs)]
            dequeued_jobs = await asyncio.gather(*dequeue_tasks)
            dequeue_time = time.time() - start_time
            
            # Verify all jobs were dequeued
            successful_dequeues = sum(1 for job in dequeued_jobs if job is not None)
            
            await queue.disconnect()
            
            result = {
                "test": "concurrent_operations",
                "status": "PASS",
                "message": f"Concurrent operations successful",
                "details": {
                    "jobs_processed": num_jobs,
                    "successful_enqueues": num_jobs,
                    "successful_dequeues": successful_dequeues,
                    "enqueue_time": f"{enqueue_time:.2f}s",
                    "dequeue_time": f"{dequeue_time:.2f}s",
                    "enqueue_throughput": f"{num_jobs/enqueue_time:.2f} ops/sec",
                    "dequeue_throughput": f"{successful_dequeues/dequeue_time:.2f} ops/sec"
                }
            }
            print(f"✅ Concurrent operations test passed - {num_jobs/enqueue_time:.1f} enqueues/sec, {successful_dequeues/dequeue_time:.1f} dequeues/sec")
            
        except Exception as e:
            result = {
                "test": "concurrent_operations",
                "status": "FAIL",
                "message": f"Concurrent operations failed: {e}",
                "details": {"error": str(e)}
            }
            print(f"❌ Concurrent operations test failed: {e}")
        
        self.test_results.append(result)
        return result
    
    async def test_job_cancellation(self) -> dict:
        """Test job cancellation functionality"""
        print("🔄 Testing job cancellation...")
        
        try:
            queue = RedisJobQueue(self.redis_url, "test-cancel")
            await queue.connect()
            
            # Clean up
            redis_client = queue._get_redis_client()
            keys_to_delete = await redis_client.keys("test-cancel:*")
            if keys_to_delete:
                await redis_client.delete(*keys_to_delete)
            await redis_client.aclose()
            
            # Create and enqueue job
            job_request = JobRequest.create(
                job_type="cancellable_test",
                url="https://cancel-me.com",
                parameters={}
            )
            job_id = await queue.enqueue_job(job_request)
            
            # Verify job is queued
            stats = await queue.get_queue_stats()
            assert stats["queued_jobs"] == 1, "Job not queued properly"
            
            # Cancel the job
            success = await queue.cancel_job(job_id)
            assert success, "Job cancellation failed"
            
            # Verify job status is cancelled
            status = await queue.get_job_status(job_id)
            assert status.status == JobStatus.CANCELLED, f"Expected CANCELLED, got {status.status}"
            
            # Verify queue is empty
            stats = await queue.get_queue_stats()
            assert stats["queued_jobs"] == 0, "Queue not empty after cancellation"
            
            await queue.disconnect()
            
            result = {
                "test": "job_cancellation",
                "status": "PASS",
                "message": "Job cancellation works correctly",
                "details": {
                    "cancellation_success": True,
                    "final_status": "CANCELLED",
                    "queue_cleaned": True
                }
            }
            print("✅ Job cancellation test passed")
            
        except Exception as e:
            result = {
                "test": "job_cancellation",
                "status": "FAIL",
                "message": f"Job cancellation failed: {e}",
                "details": {"error": str(e)}
            }
            print(f"❌ Job cancellation test failed: {e}")
        
        self.test_results.append(result)
        return result
    
    async def run_all_tests(self) -> dict:
        """Run all Redis integration tests"""
        print("🚀 Starting Redis Queue Integration Tests")
        print("=" * 60)
        
        if not REDIS_AVAILABLE:
            return {
                "overall_status": "SKIP",
                "message": "Redis dependencies not available",
                "tests": []
            }
        
        test_methods = [
            self.test_redis_connectivity,
            self.test_job_queue_basic_operations,
            self.test_job_priority_ordering,
            self.test_concurrent_operations,
            self.test_job_cancellation,
        ]
        
        for test_method in test_methods:
            await test_method()
            await asyncio.sleep(0.1)  # Brief pause between tests
        
        # Generate summary
        passed_tests = [r for r in self.test_results if r["status"] == "PASS"]
        failed_tests = [r for r in self.test_results if r["status"] == "FAIL"]
        
        overall_status = "PASS" if len(failed_tests) == 0 else "FAIL"
        
        summary = {
            "overall_status": overall_status,
            "total_tests": len(self.test_results),
            "passed": len(passed_tests),
            "failed": len(failed_tests),
            "success_rate": f"{len(passed_tests)/len(self.test_results)*100:.1f}%",
            "tests": self.test_results
        }
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Overall Status: {'✅ PASS' if overall_status == 'PASS' else '❌ FAIL'}")
        print(f"Total Tests: {len(self.test_results)}")
        print(f"Passed: {len(passed_tests)}")
        print(f"Failed: {len(failed_tests)}")
        print(f"Success Rate: {len(passed_tests)/len(self.test_results)*100:.1f}%")
        
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['message']}")
        
        return summary


async def main():
    """Main test execution function"""
    tester = RedisIntegrationTester()
    
    # Check if Redis service is running
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 6379))
        sock.close()
        
        if result != 0:
            print("❌ Redis service is not running on localhost:6379")
            print("   Please start Redis with: docker-compose up redis -d")
            return
    except Exception as e:
        print(f"❌ Could not check Redis service: {e}")
        return
    
    # Run tests
    summary = await tester.run_all_tests()
    
    # Save results
    with open("redis_integration_test_results.json", "w") as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📄 Results saved to: redis_integration_test_results.json")
    
    # Return appropriate exit code
    return 0 if summary["overall_status"] == "PASS" else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)