[07/21/25 22:51:04] INFO     Connected to Redis at              job_queue.py:127
                             redis://localhost:6379                             
[07/21/25 22:51:05] INFO     Worker test-worker-1 initialized   job_worker.py:64
                             successfully                                       
                    INFO     Starting worker test-worker-1 with job_worker.py:77
                             3 concurrent tasks                                 
                    INFO     Starting worker loop 0 for        job_worker.py:124
                             test-worker-1                                      
                    INFO     Starting worker loop 1 for        job_worker.py:124
                             test-worker-1                                      
                    INFO     Starting worker loop 2 for        job_worker.py:124
                             test-worker-1                                      
[07/21/25 22:52:01] INFO     Received shutdown signal for      job_worker.py:466
                             worker test-worker-1                               
                    INFO     Received shutdown signal for      job_worker.py:466
                             worker test-worker-1                               
[07/21/25 22:52:02] INFO     Shutting down worker              job_worker.py:105
                             test-worker-1                                      
                    INFO     Worker loop 0 cancelled           job_worker.py:142
                    INFO     Worker loop 1 cancelled           job_worker.py:142
                    INFO     Worker loop 2 cancelled           job_worker.py:142
