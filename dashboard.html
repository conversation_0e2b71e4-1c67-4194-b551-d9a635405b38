<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AY Knowledge Base v0.1</title>
    <style>
        /* AY Knowledge Base Dashboard - Black/Grey/White/Neon Pink Theme */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #ff0080;
            background: #1a1a1a;
            border-radius: 10px;
        }

        .title {
            font-size: 3rem;
            font-weight: bold;
            color: #ff0080;
            text-shadow: 0 0 10px #ff0080;
            margin-bottom: 10px;
            letter-spacing: 3px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            font-style: italic;
        }

        .version {
            font-size: 1rem;
            color: #888888;
            margin-top: 5px;
        }

        /* Navigation Menu */
        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .nav-button {
            background: #333333;
            color: #ffffff;
            border: 2px solid #ff0080;
            padding: 20px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            font-family: inherit;
        }

        .nav-button:hover {
            background: #ff0080;
            color: #000000;
            box-shadow: 0 0 20px #ff0080;
            transform: translateY(-2px);
        }

        .nav-button:active {
            transform: translateY(0);
        }

        /* Main Content Area */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 40px;
        }

        .content-panel {
            background: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;
        }

        .sidebar {
            background: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 20px;
        }

        /* Panel Headers */
        .panel-header {
            color: #ff0080;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333333;
        }

        /* Status Bar */
        .status-bar {
            background: #1a1a1a;
            border: 1px solid #333333;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff0080;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Forms */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #cccccc;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: #333333;
            border: 1px solid #555555;
            border-radius: 4px;
            color: #ffffff;
            font-family: inherit;
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff0080;
            box-shadow: 0 0 5px #ff0080;
        }

        .btn {
            background: #333333;
            color: #ffffff;
            border: 2px solid #ff0080;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .btn:hover {
            background: #ff0080;
            color: #000000;
        }

        .btn-primary {
            background: #ff0080;
            color: #000000;
        }

        .btn-primary:hover {
            background: #ff0080;
            box-shadow: 0 0 15px #ff0080;
        }

        /* Queue Items */
        .queue-item {
            background: #333333;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .queue-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .queue-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending { background: #ffa500; color: #000; }
        .status-processing { background: #ff0080; color: #000; }
        .status-completed { background: #00ff00; color: #000; }
        .status-failed { background: #ff0000; color: #fff; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2rem;
            }
        }

        /* Hidden sections */
        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        /* Loading spinner */
        .spinner {
            border: 3px solid #333333;
            border-top: 3px solid #ff0080;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Server Control Panel */
        .control-panel {
            background: #1a1a1a;
            border: 2px solid #ff0080;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .control-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: #333333;
            border-radius: 4px;
        }

        .control-row:last-child {
            margin-bottom: 0;
        }

        .service-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .service-name {
            font-weight: bold;
            color: #ffffff;
            min-width: 120px;
        }

        .service-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-running {
            background: #00ff00;
            color: #000000;
        }

        .status-stopped {
            background: #ff0000;
            color: #ffffff;
        }

        .status-starting {
            background: #ffa500;
            color: #000000;
        }

        .status-unknown {
            background: #888888;
            color: #ffffff;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.9rem;
            min-width: 80px;
        }

        .btn-success {
            background: #00ff00;
            color: #000000;
            border: 2px solid #00ff00;
        }

        .btn-success:hover {
            background: #00cc00;
            box-shadow: 0 0 10px #00ff00;
        }

        .btn-danger {
            background: #ff0000;
            color: #ffffff;
            border: 2px solid #ff0000;
        }

        .btn-danger:hover {
            background: #cc0000;
            box-shadow: 0 0 10px #ff0000;
        }

        .btn-warning {
            background: #ffa500;
            color: #000000;
            border: 2px solid #ffa500;
        }

        .btn-warning:hover {
            background: #cc8400;
            box-shadow: 0 0 10px #ffa500;
        }

        /* Queue Management Enhancements */
        .queue-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .queue-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #333333;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ff0080;
            display: block;
        }

        .stat-label {
            color: #cccccc;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .queue-item-enhanced {
            background: #333333;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
        }

        .queue-item-enhanced.selected {
            border-color: #ff0080;
            background: #2a1a2a;
        }

        .queue-item-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }

        .btn-icon {
            width: 30px;
            height: 30px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #555555;
            border-radius: 3px;
            margin-top: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0080, #ff4080);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: #ffffff;
            font-weight: bold;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .notification.success {
            background: #00ff00;
            color: #000000;
        }

        .notification.error {
            background: #ff0000;
        }

        .notification.warning {
            background: #ffa500;
            color: #000000;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="title">AY KNOWLEDGE BASE</div>
            <div class="version">v0.1</div>
            <div class="subtitle">Intelligent RAG-powered Knowledge Management</div>
        </div>

        <!-- Navigation Menu -->
        <div class="nav-menu">
            <button class="nav-button" onclick="showSection('crawl-url')">
                🌐 Crawl by URL
            </button>
            <button class="nav-button" onclick="showSection('crawl-search')">
                🔍 Crawl by Search
            </button>
            <button class="nav-button" onclick="showSection('queue-manager')">
                🔄 Queue Manager
            </button>
            <button class="nav-button" onclick="showSection('chat')">
                💬 Chat/Query
            </button>
            <button class="nav-button" onclick="showSection('sources')">
                📚 View Sources
            </button>
            <button class="nav-button" onclick="showSection('settings')">
                ⚙️ Settings
            </button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-panel">
                <!-- Home Section -->
                <div id="home" class="section active">
                    <div class="panel-header">Welcome to AY Knowledge Base</div>

                    <!-- Server Control Panel -->
                    <div class="control-panel">
                        <div class="panel-header" style="margin-bottom: 15px; font-size: 1.1rem;">🔧 System Control</div>

                        <div class="control-row">
                            <div class="service-info">
                                <span class="service-name">MCP Server</span>
                                <span id="mcp-server-status" class="service-status status-unknown">Unknown</span>
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-small btn-success" onclick="startService('mcp')">Start</button>
                                <button class="btn btn-small btn-danger" onclick="stopService('mcp')">Stop</button>
                                <button class="btn btn-small" onclick="restartService('mcp')">Restart</button>
                            </div>
                        </div>

                        <div class="control-row">
                            <div class="service-info">
                                <span class="service-name">Redis Queue</span>
                                <span id="redis-server-status" class="service-status status-unknown">Unknown</span>
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-small btn-success" onclick="startService('redis')">Start</button>
                                <button class="btn btn-small btn-danger" onclick="stopService('redis')">Stop</button>
                                <button class="btn btn-small" onclick="restartService('redis')">Restart</button>
                            </div>
                        </div>

                        <div class="control-row">
                            <div class="service-info">
                                <span class="service-name">Crawl Workers</span>
                                <span id="workers-status" class="service-status status-unknown">Unknown</span>
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-small btn-success" onclick="startWorkers()">Start</button>
                                <button class="btn btn-small btn-warning" onclick="pauseWorkers()">Pause</button>
                                <button class="btn btn-small btn-danger" onclick="stopWorkers()">Stop</button>
                            </div>
                        </div>
                    </div>

                    <p style="color: #cccccc; line-height: 1.6; margin-bottom: 20px;">
                        Your intelligent RAG-powered knowledge management system. Use the menu above to:
                    </p>
                    <ul style="color: #cccccc; line-height: 1.8; margin-left: 20px;">
                        <li><strong>Crawl by URL:</strong> Extract content from specific web pages</li>
                        <li><strong>Crawl by Search:</strong> Discover and crawl content based on search queries</li>
                        <li><strong>Queue Manager:</strong> Monitor and manage crawling tasks</li>
                        <li><strong>Chat/Query:</strong> Ask questions about your crawled content</li>
                        <li><strong>View Sources:</strong> Browse your knowledge base</li>
                        <li><strong>Settings:</strong> Configure system preferences</li>
                    </ul>
                </div>

                <!-- Crawl by URL Section -->
                <div id="crawl-url" class="section">
                    <div class="panel-header">Crawl by URL</div>
                    <form onsubmit="startUrlCrawl(event)">
                        <div class="form-group">
                            <label class="form-label">URL to Crawl:</label>
                            <input type="url" class="form-input" id="url-input" 
                                   placeholder="https://example.com" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Depth:</label>
                            <input type="number" class="form-input" id="depth-input" 
                                   value="3" min="1" max="10">
                        </div>
                        <button type="submit" class="btn btn-primary">Start Crawling</button>
                    </form>
                    <div id="crawl-status"></div>
                </div>

                <!-- Crawl by Search Section -->
                <div id="crawl-search" class="section">
                    <div class="panel-header">Crawl by Search</div>
                    <form onsubmit="startSearchCrawl(event)">
                        <div class="form-group">
                            <label class="form-label">Search Query:</label>
                            <input type="text" class="form-input" id="search-input"
                                   placeholder="Enter search terms..." required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Results:</label>
                            <input type="number" class="form-input" id="max-results-input"
                                   value="10" min="1" max="50">
                        </div>
                        <button type="submit" class="btn btn-primary">Start Search Crawl</button>
                    </form>
                    <div id="search-status"></div>
                </div>

                <!-- Queue Manager Section -->
                <div id="queue-manager" class="section">
                    <div class="panel-header">Advanced Queue Manager</div>

                    <!-- Queue Statistics -->
                    <div class="queue-stats">
                        <div class="stat-card">
                            <span class="stat-number" id="queue-total">0</span>
                            <span class="stat-label">Total Tasks</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="queue-pending">0</span>
                            <span class="stat-label">Pending</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="queue-processing">0</span>
                            <span class="stat-label">Processing</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="queue-completed">0</span>
                            <span class="stat-label">Completed</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number" id="queue-failed">0</span>
                            <span class="stat-label">Failed</span>
                        </div>
                    </div>

                    <!-- Queue Controls -->
                    <div class="queue-controls">
                        <button class="btn" onclick="refreshQueue()">🔄 Refresh</button>
                        <button class="btn btn-success" onclick="pauseQueue()">⏸️ Pause Queue</button>
                        <button class="btn btn-warning" onclick="resumeQueue()">▶️ Resume Queue</button>
                        <button class="btn" onclick="clearCompleted()">🗑️ Clear Completed</button>
                        <button class="btn btn-danger" onclick="clearAll()">🗑️ Clear All</button>
                        <button class="btn" onclick="exportQueue()">📥 Export</button>
                        <select id="queue-filter" class="form-input" style="width: auto; margin-left: 10px;" onchange="filterQueue()">
                            <option value="all">All Tasks</option>
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                        </select>
                        <select id="queue-sort" class="form-input" style="width: auto; margin-left: 10px;" onchange="sortQueue()">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="priority">Priority</option>
                            <option value="status">Status</option>
                        </select>
                    </div>

                    <!-- Bulk Actions -->
                    <div style="margin-bottom: 20px; padding: 15px; background: #2a2a2a; border-radius: 4px;">
                        <div style="margin-bottom: 10px;">
                            <label style="color: #cccccc;">
                                <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                                Select All Visible
                            </label>
                        </div>
                        <div class="queue-controls">
                            <button class="btn btn-small" onclick="bulkAction('retry')">🔄 Retry Selected</button>
                            <button class="btn btn-small btn-warning" onclick="bulkAction('pause')">⏸️ Pause Selected</button>
                            <button class="btn btn-small btn-danger" onclick="bulkAction('cancel')">❌ Cancel Selected</button>
                            <button class="btn btn-small" onclick="bulkAction('priority')">⭐ Set Priority</button>
                        </div>
                    </div>

                    <!-- Queue List -->
                    <div id="queue-list">
                        <!-- Queue items will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Chat/Query Section -->
                <div id="chat" class="section">
                    <div class="panel-header">Chat with Your Knowledge Base</div>
                    <div id="chat-messages" style="height: 300px; overflow-y: auto; border: 1px solid #333; padding: 15px; margin-bottom: 20px; background: #0a0a0a;">
                        <div style="color: #ff0080; margin-bottom: 10px;">
                            <strong>AI Assistant:</strong> Hello! Ask me anything about your crawled content.
                        </div>
                    </div>
                    <form onsubmit="sendMessage(event)">
                        <div class="form-group">
                            <input type="text" class="form-input" id="message-input"
                                   placeholder="Ask a question about your knowledge base..." required>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>

                <!-- View Sources Section -->
                <div id="sources" class="section">
                    <div class="panel-header">Knowledge Base Sources</div>
                    <div style="margin-bottom: 20px;">
                        <button class="btn" onclick="refreshSources()">🔄 Refresh</button>
                        <input type="text" class="form-input" id="source-filter"
                               placeholder="Filter sources..." style="width: 300px; display: inline-block; margin-left: 10px;">
                    </div>
                    <div id="sources-list">
                        <div class="queue-item">
                            <div class="queue-item-header">
                                <span>example.com</span>
                                <span style="color: #cccccc;">15 documents</span>
                            </div>
                            <div style="color: #888; font-size: 0.9rem;">
                                Last crawled: 2 hours ago
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settings" class="section">
                    <div class="panel-header">Settings</div>
                    <form onsubmit="saveSettings(event)">
                        <div class="form-group">
                            <label class="form-label">Default Crawl Depth:</label>
                            <input type="number" class="form-input" id="default-depth"
                                   value="3" min="1" max="10">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Concurrent Crawls:</label>
                            <input type="number" class="form-input" id="max-concurrent"
                                   value="5" min="1" max="20">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Chunk Size:</label>
                            <input type="number" class="form-input" id="chunk-size"
                                   value="5000" min="1000" max="10000">
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" id="auto-refresh" checked>
                                Auto-refresh queue status
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">Save Settings</button>
                    </form>
                </div>
            </div>

            <!-- Enhanced Sidebar -->
            <div class="sidebar">
                <div class="panel-header">🔧 System Status</div>

                <!-- Service Status Indicators -->
                <div class="status-item">
                    <div id="mcp-indicator" class="status-indicator"></div>
                    <span>MCP Server: <span id="mcp-status">Unknown</span></span>
                </div>
                <div class="status-item" style="margin-top: 10px;">
                    <div id="redis-indicator" class="status-indicator"></div>
                    <span>Redis Queue: <span id="redis-status">Unknown</span></span>
                </div>
                <div class="status-item" style="margin-top: 10px;">
                    <div id="workers-indicator" class="status-indicator"></div>
                    <span>Workers: <span id="workers-status-text">Unknown</span></span>
                </div>

                <!-- System Health -->
                <div class="panel-header" style="margin-top: 30px;">📊 System Health</div>
                <div style="color: #cccccc; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>CPU Usage:</span>
                        <span id="cpu-usage">--</span>
                    </div>
                    <div class="progress-bar" style="height: 4px;">
                        <div id="cpu-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>

                <div style="color: #cccccc; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>Memory Usage:</span>
                        <span id="memory-usage">--</span>
                    </div>
                    <div class="progress-bar" style="height: 4px;">
                        <div id="memory-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>

                <div style="color: #cccccc; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>Queue Load:</span>
                        <span id="queue-load">--</span>
                    </div>
                    <div class="progress-bar" style="height: 4px;">
                        <div id="queue-progress" class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="panel-header" style="margin-top: 30px;">📈 Quick Stats</div>
                <div style="color: #cccccc;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Total Documents:</span>
                        <span id="total-docs" style="color: #ff0080; font-weight: bold;">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Active Tasks:</span>
                        <span id="active-tasks" style="color: #ff0080; font-weight: bold;">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Sources:</span>
                        <span id="total-sources" style="color: #ff0080; font-weight: bold;">0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Success Rate:</span>
                        <span id="success-rate" style="color: #00ff00; font-weight: bold;">--</span>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="panel-header" style="margin-top: 30px;">🕒 Recent Activity</div>
                <div id="recent-activity" style="color: #cccccc; font-size: 0.9rem; max-height: 150px; overflow-y: auto;">
                    <div style="margin-bottom: 5px;">• System started</div>
                    <div style="margin-bottom: 5px;">• Dashboard loaded</div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <span>Ready</span>
            </div>
            <div class="status-item">
                <span id="current-time"></span>
            </div>
        </div>
    </div>

    <script>
        // AY Knowledge Base Dashboard JavaScript

        // Global state
        let currentQueue = [];
        let currentSources = [];
        let chatHistory = [];
        let selectedTasks = new Set();
        let queueFilter = 'all';
        let queueSort = 'newest';
        let systemServices = {
            mcp: 'unknown',
            redis: 'unknown',
            workers: 'unknown'
        };

        // Navigation
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.add('active');
            } else {
                document.getElementById('home').classList.add('active');
            }
        }

        // API Configuration
        const API_BASE = 'http://localhost:8080/api';

        // Crawl by URL
        async function startUrlCrawl(event) {
            event.preventDefault();
            const url = document.getElementById('url-input').value;
            const depth = document.getElementById('depth-input').value;

            document.getElementById('crawl-status').innerHTML = `
                <div class="spinner"></div>
                <p style="text-align: center; color: #ff0080; margin-top: 10px;">
                    Starting crawl for: ${url}
                </p>
            `;

            try {
                const response = await fetch(`${API_BASE}/crawl/url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        max_depth: parseInt(depth)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('crawl-status').innerHTML = `
                        <div style="background: #1a4a1a; border: 1px solid #00ff00; padding: 15px; border-radius: 4px; margin-top: 20px;">
                            <strong style="color: #00ff00;">✓ Crawl Started Successfully!</strong><br>
                            <span style="color: #cccccc;">URL: ${url}</span><br>
                            <span style="color: #cccccc;">Depth: ${depth}</span><br>
                            <span style="color: #cccccc;">Task ID: ${result.task_id}</span><br>
                            <span style="color: #888;">Check Queue Manager for progress...</span>
                        </div>
                    `;
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Unknown error');
                }
            } catch (error) {
                document.getElementById('crawl-status').innerHTML = `
                    <div style="background: #4a1a1a; border: 1px solid #ff0000; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <strong style="color: #ff0000;">✗ Error Starting Crawl</strong><br>
                        <span style="color: #cccccc;">${error.message}</span>
                    </div>
                `;
            }
        }

        // Crawl by Search
        async function startSearchCrawl(event) {
            event.preventDefault();
            const query = document.getElementById('search-input').value;
            const maxResults = document.getElementById('max-results-input').value;

            document.getElementById('search-status').innerHTML = `
                <div class="spinner"></div>
                <p style="text-align: center; color: #ff0080; margin-top: 10px;">
                    Searching for: "${query}"
                </p>
            `;

            try {
                const response = await fetch(`${API_BASE}/crawl/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        max_results: parseInt(maxResults)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('search-status').innerHTML = `
                        <div style="background: #1a4a1a; border: 1px solid #00ff00; padding: 15px; border-radius: 4px; margin-top: 20px;">
                            <strong style="color: #00ff00;">✓ Search Crawl Started!</strong><br>
                            <span style="color: #cccccc;">Query: "${query}"</span><br>
                            <span style="color: #cccccc;">Max Results: ${maxResults}</span><br>
                            <span style="color: #cccccc;">Task ID: ${result.task_id}</span><br>
                            <span style="color: #888;">Found URLs will be added to queue...</span>
                        </div>
                    `;
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Unknown error');
                }
            } catch (error) {
                document.getElementById('search-status').innerHTML = `
                    <div style="background: #4a1a1a; border: 1px solid #ff0000; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <strong style="color: #ff0000;">✗ Error Starting Search</strong><br>
                        <span style="color: #cccccc;">${error.message}</span>
                    </div>
                `;
            }
        }

        // Queue Management
        function addToQueue(item, status) {
            const queueItem = {
                id: Date.now(),
                item: item,
                status: status,
                timestamp: new Date().toLocaleString()
            };
            currentQueue.push(queueItem);
            updateQueueDisplay();
            updateStats();
        }

        function updateQueueDisplay() {
            const queueList = document.getElementById('queue-list');

            // Filter and sort queue
            let filteredQueue = currentQueue.filter(item => {
                if (queueFilter === 'all') return true;
                return item.status === queueFilter;
            });

            // Sort queue
            filteredQueue.sort((a, b) => {
                switch (queueSort) {
                    case 'newest': return new Date(b.timestamp) - new Date(a.timestamp);
                    case 'oldest': return new Date(a.timestamp) - new Date(b.timestamp);
                    case 'priority': return (b.priority || 0) - (a.priority || 0);
                    case 'status': return a.status.localeCompare(b.status);
                    default: return 0;
                }
            });

            if (filteredQueue.length === 0) {
                queueList.innerHTML = '<p style="color: #888; text-align: center;">No tasks match current filter</p>';
                updateQueueStats();
                return;
            }

            queueList.innerHTML = filteredQueue.map(item => `
                <div class="queue-item-enhanced ${selectedTasks.has(item.id) ? 'selected' : ''}" data-task-id="${item.id}">
                    <div class="queue-item-actions">
                        <button class="btn btn-icon" onclick="retryTask('${item.id}')" title="Retry">🔄</button>
                        <button class="btn btn-icon btn-warning" onclick="pauseTask('${item.id}')" title="Pause">⏸️</button>
                        <button class="btn btn-icon btn-danger" onclick="cancelTask('${item.id}')" title="Cancel">❌</button>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <label style="color: #cccccc;">
                            <input type="checkbox" ${selectedTasks.has(item.id) ? 'checked' : ''}
                                   onchange="toggleTaskSelection('${item.id}')">
                            Select
                        </label>
                    </div>
                    <div class="queue-item-header">
                        <span>${item.item}</span>
                        <span class="queue-status status-${item.status}">${item.status.toUpperCase()}</span>
                    </div>
                    <div style="color: #cccccc; font-size: 0.9rem; margin-bottom: 8px;">
                        Added: ${item.timestamp} | Priority: ${item.priority || 'Normal'}
                    </div>
                    ${item.progress !== undefined ? `
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${item.progress}%"></div>
                        </div>
                        <div style="color: #888; font-size: 0.8rem; margin-top: 5px;">
                            Progress: ${item.progress}%
                        </div>
                    ` : ''}
                    ${item.error ? `
                        <div style="color: #ff0000; font-size: 0.9rem; margin-top: 5px;">
                            Error: ${item.error}
                        </div>
                    ` : ''}
                </div>
            `).join('');

            updateQueueStats();
        }

        async function refreshQueue() {
            document.getElementById('queue-list').innerHTML = '<div class="spinner"></div>';

            try {
                const response = await fetch(`${API_BASE}/queue/status`);
                const result = await response.json();

                if (result.success) {
                    currentQueue = result.tasks;
                    updateQueueDisplay();
                } else {
                    throw new Error(result.error || 'Failed to fetch queue');
                }
            } catch (error) {
                document.getElementById('queue-list').innerHTML = `
                    <p style="color: #ff0000; text-align: center;">Error loading queue: ${error.message}</p>
                `;
            }
        }

        function clearQueue() {
            currentQueue = currentQueue.filter(item => item.status !== 'completed');
            updateQueueDisplay();
            updateStats();
        }

        // Chat functionality
        function sendMessage(event) {
            event.preventDefault();
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();

            if (!message) return;

            // Add user message
            addChatMessage('user', message);
            messageInput.value = '';

            // Simulate AI response
            setTimeout(() => {
                const responses = [
                    "I found relevant information in your knowledge base...",
                    "Based on the crawled content, here's what I can tell you...",
                    "Let me search through your documents for that information...",
                    "I can help you with that based on your crawled sources..."
                ];
                const response = responses[Math.floor(Math.random() * responses.length)];
                addChatMessage('ai', response);
            }, 1500);
        }

        function addChatMessage(sender, message) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '15px';

            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div style="color: #ffffff; margin-bottom: 5px;">
                        <strong>You:</strong> ${message}
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div style="color: #ff0080; margin-bottom: 5px;">
                        <strong>AI Assistant:</strong> ${message}
                    </div>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Sources management
        function refreshSources() {
            document.getElementById('sources-list').innerHTML = '<div class="spinner"></div>';
            setTimeout(() => {
                updateSourcesDisplay();
            }, 1000);
        }

        function updateSourcesDisplay() {
            const sourcesList = document.getElementById('sources-list');
            const sampleSources = [
                { domain: 'example.com', docs: 15, lastCrawl: '2 hours ago' },
                { domain: 'docs.python.org', docs: 42, lastCrawl: '1 day ago' },
                { domain: 'github.com', docs: 8, lastCrawl: '3 hours ago' }
            ];

            sourcesList.innerHTML = sampleSources.map(source => `
                <div class="queue-item">
                    <div class="queue-item-header">
                        <span>${source.domain}</span>
                        <span style="color: #cccccc;">${source.docs} documents</span>
                    </div>
                    <div style="color: #888; font-size: 0.9rem;">
                        Last crawled: ${source.lastCrawl}
                    </div>
                </div>
            `).join('');
        }

        // Settings
        function saveSettings(event) {
            event.preventDefault();

            // Show success message
            const settingsForm = event.target;
            const successDiv = document.createElement('div');
            successDiv.style.cssText = 'background: #1a4a1a; border: 1px solid #00ff00; padding: 10px; border-radius: 4px; margin-top: 15px; color: #00ff00;';
            successDiv.textContent = '✓ Settings saved successfully!';

            settingsForm.appendChild(successDiv);
            setTimeout(() => successDiv.remove(), 3000);
        }

        // Update stats
        function updateStats() {
            document.getElementById('total-docs').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('active-tasks').textContent = currentQueue.filter(item => item.status === 'processing').length;
            document.getElementById('total-sources').textContent = Math.floor(Math.random() * 10) + 5;
        }

        // Update time
        function updateTime() {
            document.getElementById('current-time').textContent =
                new Date().toLocaleTimeString();
        }

        // Real-time updates
        async function updateStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;
                    document.getElementById('total-docs').textContent = stats.total_documents;
                    document.getElementById('active-tasks').textContent = stats.active_tasks;
                    document.getElementById('total-sources').textContent = stats.total_sources;
                    document.getElementById('redis-status').textContent = stats.redis_status;
                    document.getElementById('mcp-status').textContent = stats.mcp_status;
                }
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        async function updateSourcesDisplay() {
            try {
                const response = await fetch(`${API_BASE}/sources`);
                const result = await response.json();

                if (result.success) {
                    const sourcesList = document.getElementById('sources-list');
                    sourcesList.innerHTML = result.sources.map(source => `
                        <div class="queue-item">
                            <div class="queue-item-header">
                                <span>${source.domain}</span>
                                <span style="color: #cccccc;">${source.documents} documents</span>
                            </div>
                            <div style="color: #888; font-size: 0.9rem;">
                                Last crawled: ${source.last_crawl}
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('Error updating sources:', error);
            }
        }

        // Auto-refresh functionality
        function startAutoRefresh() {
            setInterval(updateStats, 5000);           // Update stats every 5 seconds
            setInterval(refreshQueue, 10000);         // Refresh queue every 10 seconds
            setInterval(updateSourcesDisplay, 30000); // Update sources every 30 seconds
            setInterval(updateSystemHealth, 3000);    // Update system health every 3 seconds
            setInterval(updateSuccessRate, 5000);     // Update success rate every 5 seconds
        }

        // Server Control Functions
        async function startService(service) {
            showNotification(`Starting ${service} service...`, 'warning');
            try {
                const response = await fetch(`${API_BASE}/services/${service}/start`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification(`${service} service started successfully!`, 'success');
                    updateServiceStatus(service, 'running');
                } else {
                    throw new Error(result.error || 'Failed to start service');
                }
            } catch (error) {
                showNotification(`Error starting ${service}: ${error.message}`, 'error');
            }
        }

        async function stopService(service) {
            showNotification(`Stopping ${service} service...`, 'warning');
            try {
                const response = await fetch(`${API_BASE}/services/${service}/stop`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification(`${service} service stopped successfully!`, 'success');
                    updateServiceStatus(service, 'stopped');
                } else {
                    throw new Error(result.error || 'Failed to stop service');
                }
            } catch (error) {
                showNotification(`Error stopping ${service}: ${error.message}`, 'error');
            }
        }

        async function restartService(service) {
            showNotification(`Restarting ${service} service...`, 'warning');
            try {
                await stopService(service);
                setTimeout(() => startService(service), 2000);
            } catch (error) {
                showNotification(`Error restarting ${service}: ${error.message}`, 'error');
            }
        }

        function updateServiceStatus(service, status) {
            systemServices[service] = status;

            // Update status text
            const statusElement = document.getElementById(`${service}-server-status`) ||
                                 document.getElementById(`${service}-status`);
            if (statusElement) {
                statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusElement.className = `service-status status-${status}`;
            }

            // Update status indicator
            const indicatorElement = document.getElementById(`${service}-indicator`);
            if (indicatorElement) {
                indicatorElement.className = 'status-indicator';
                indicatorElement.style.background = getStatusColor(status);
            }

            // Update workers status text specifically
            if (service === 'workers') {
                const workersText = document.getElementById('workers-status-text');
                if (workersText) {
                    workersText.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                }
            }

            // Log activity
            addRecentActivity(`${service} service ${status}`);
        }

        function getStatusColor(status) {
            switch (status) {
                case 'running': return '#00ff00';
                case 'stopped': return '#ff0000';
                case 'starting': case 'paused': return '#ffa500';
                default: return '#888888';
            }
        }

        function addRecentActivity(message) {
            const activityContainer = document.getElementById('recent-activity');
            const timestamp = new Date().toLocaleTimeString();
            const activityItem = document.createElement('div');
            activityItem.style.marginBottom = '5px';
            activityItem.innerHTML = `• ${timestamp}: ${message}`;

            // Add to top
            activityContainer.insertBefore(activityItem, activityContainer.firstChild);

            // Keep only last 10 items
            while (activityContainer.children.length > 10) {
                activityContainer.removeChild(activityContainer.lastChild);
            }
        }

        function updateSystemHealth() {
            // Simulate system health metrics
            const cpu = Math.floor(Math.random() * 100);
            const memory = Math.floor(Math.random() * 100);
            const queueLoad = Math.min(100, (currentQueue.filter(t => t.status === 'processing').length / 5) * 100);

            // Update CPU
            document.getElementById('cpu-usage').textContent = `${cpu}%`;
            document.getElementById('cpu-progress').style.width = `${cpu}%`;
            document.getElementById('cpu-progress').style.background =
                cpu > 80 ? '#ff0000' : cpu > 60 ? '#ffa500' : '#00ff00';

            // Update Memory
            document.getElementById('memory-usage').textContent = `${memory}%`;
            document.getElementById('memory-progress').style.width = `${memory}%`;
            document.getElementById('memory-progress').style.background =
                memory > 80 ? '#ff0000' : memory > 60 ? '#ffa500' : '#00ff00';

            // Update Queue Load
            document.getElementById('queue-load').textContent = `${queueLoad}%`;
            document.getElementById('queue-progress').style.width = `${queueLoad}%`;
            document.getElementById('queue-progress').style.background =
                queueLoad > 80 ? '#ff0000' : queueLoad > 60 ? '#ffa500' : '#ff0080';
        }

        function updateSuccessRate() {
            const completed = currentQueue.filter(t => t.status === 'completed').length;
            const failed = currentQueue.filter(t => t.status === 'failed').length;
            const total = completed + failed;

            if (total > 0) {
                const rate = Math.round((completed / total) * 100);
                document.getElementById('success-rate').textContent = `${rate}%`;
                document.getElementById('success-rate').style.color =
                    rate > 80 ? '#00ff00' : rate > 60 ? '#ffa500' : '#ff0000';
            } else {
                document.getElementById('success-rate').textContent = '--';
            }
        }

        // Worker Control Functions
        async function startWorkers() {
            showNotification('Starting crawl workers...', 'warning');
            try {
                const response = await fetch(`${API_BASE}/workers/start`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Crawl workers started successfully!', 'success');
                    updateServiceStatus('workers', 'running');
                } else {
                    throw new Error(result.error || 'Failed to start workers');
                }
            } catch (error) {
                showNotification(`Error starting workers: ${error.message}`, 'error');
            }
        }

        async function pauseWorkers() {
            showNotification('Pausing crawl workers...', 'warning');
            try {
                const response = await fetch(`${API_BASE}/workers/pause`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Crawl workers paused successfully!', 'success');
                    updateServiceStatus('workers', 'paused');
                } else {
                    throw new Error(result.error || 'Failed to pause workers');
                }
            } catch (error) {
                showNotification(`Error pausing workers: ${error.message}`, 'error');
            }
        }

        async function stopWorkers() {
            showNotification('Stopping crawl workers...', 'warning');
            try {
                const response = await fetch(`${API_BASE}/workers/stop`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Crawl workers stopped successfully!', 'success');
                    updateServiceStatus('workers', 'stopped');
                } else {
                    throw new Error(result.error || 'Failed to stop workers');
                }
            } catch (error) {
                showNotification(`Error stopping workers: ${error.message}`, 'error');
            }
        }

        // Queue Management Functions
        function updateQueueStats() {
            const stats = {
                total: currentQueue.length,
                pending: currentQueue.filter(t => t.status === 'pending').length,
                processing: currentQueue.filter(t => t.status === 'processing').length,
                completed: currentQueue.filter(t => t.status === 'completed').length,
                failed: currentQueue.filter(t => t.status === 'failed').length
            };

            Object.keys(stats).forEach(key => {
                const element = document.getElementById(`queue-${key}`);
                if (element) element.textContent = stats[key];
            });
        }

        async function pauseQueue() {
            try {
                const response = await fetch(`${API_BASE}/queue/pause`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Queue paused successfully!', 'success');
                } else {
                    throw new Error(result.error || 'Failed to pause queue');
                }
            } catch (error) {
                showNotification(`Error pausing queue: ${error.message}`, 'error');
            }
        }

        async function resumeQueue() {
            try {
                const response = await fetch(`${API_BASE}/queue/resume`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Queue resumed successfully!', 'success');
                } else {
                    throw new Error(result.error || 'Failed to resume queue');
                }
            } catch (error) {
                showNotification(`Error resuming queue: ${error.message}`, 'error');
            }
        }

        // Task Management Functions
        function toggleTaskSelection(taskId) {
            if (selectedTasks.has(taskId)) {
                selectedTasks.delete(taskId);
            } else {
                selectedTasks.add(taskId);
            }
            updateQueueDisplay();
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all').checked;
            if (selectAll) {
                currentQueue.forEach(task => selectedTasks.add(task.id));
            } else {
                selectedTasks.clear();
            }
            updateQueueDisplay();
        }

        async function retryTask(taskId) {
            try {
                const response = await fetch(`${API_BASE}/queue/task/${taskId}/retry`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Task retry initiated!', 'success');
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Failed to retry task');
                }
            } catch (error) {
                showNotification(`Error retrying task: ${error.message}`, 'error');
            }
        }

        async function pauseTask(taskId) {
            try {
                const response = await fetch(`${API_BASE}/queue/task/${taskId}/pause`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Task paused!', 'success');
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Failed to pause task');
                }
            } catch (error) {
                showNotification(`Error pausing task: ${error.message}`, 'error');
            }
        }

        async function cancelTask(taskId) {
            try {
                const response = await fetch(`${API_BASE}/queue/task/${taskId}/cancel`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Task cancelled!', 'success');
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Failed to cancel task');
                }
            } catch (error) {
                showNotification(`Error cancelling task: ${error.message}`, 'error');
            }
        }

        async function bulkAction(action) {
            if (selectedTasks.size === 0) {
                showNotification('No tasks selected!', 'warning');
                return;
            }

            const taskIds = Array.from(selectedTasks);
            try {
                const response = await fetch(`${API_BASE}/queue/bulk/${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ task_ids: taskIds })
                });
                const result = await response.json();

                if (result.success) {
                    showNotification(`Bulk ${action} completed!`, 'success');
                    selectedTasks.clear();
                    refreshQueue();
                } else {
                    throw new Error(result.error || `Failed to perform bulk ${action}`);
                }
            } catch (error) {
                showNotification(`Error in bulk ${action}: ${error.message}`, 'error');
            }
        }

        function filterQueue() {
            queueFilter = document.getElementById('queue-filter').value;
            updateQueueDisplay();
        }

        function sortQueue() {
            queueSort = document.getElementById('queue-sort').value;
            updateQueueDisplay();
        }

        async function clearCompleted() {
            try {
                const response = await fetch(`${API_BASE}/queue/clear/completed`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('Completed tasks cleared!', 'success');
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Failed to clear completed tasks');
                }
            } catch (error) {
                showNotification(`Error clearing completed tasks: ${error.message}`, 'error');
            }
        }

        async function clearAll() {
            if (!confirm('Are you sure you want to clear ALL tasks? This cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/queue/clear/all`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showNotification('All tasks cleared!', 'success');
                    selectedTasks.clear();
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Failed to clear all tasks');
                }
            } catch (error) {
                showNotification(`Error clearing all tasks: ${error.message}`, 'error');
            }
        }

        function exportQueue() {
            const dataStr = JSON.stringify(currentQueue, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `queue_export_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            showNotification('Queue exported successfully!', 'success');
        }

        // Notification System
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Initialize
        setInterval(updateTime, 1000);
        updateTime();
        updateStats();
        updateSourcesDisplay();
        startAutoRefresh();

        // Initial queue load
        refreshQueue();

        // Initialize with sample data and status
        setTimeout(() => {
            addToQueue('https://example.com', 'pending');
            addToQueue('Search: AI technology', 'processing');
            addToQueue('https://docs.python.org', 'completed');

            // Initialize service statuses
            updateServiceStatus('mcp', 'running');
            updateServiceStatus('redis', 'running');
            updateServiceStatus('workers', 'running');

            // Add initial activity
            addRecentActivity('Dashboard initialized');
            addRecentActivity('Services status checked');

            // Start health monitoring
            updateSystemHealth();
            updateSuccessRate();
        }, 1000);
    </script>
</body>
</html>
