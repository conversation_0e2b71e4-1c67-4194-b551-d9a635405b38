<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AY Knowledge Base v0.1</title>
    <style>
        /* AY Knowledge Base Dashboard - Black/Grey/White/Neon Pink Theme */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #ff0080;
            background: #1a1a1a;
            border-radius: 10px;
        }

        .title {
            font-size: 3rem;
            font-weight: bold;
            color: #ff0080;
            text-shadow: 0 0 10px #ff0080;
            margin-bottom: 10px;
            letter-spacing: 3px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            font-style: italic;
        }

        .version {
            font-size: 1rem;
            color: #888888;
            margin-top: 5px;
        }

        /* Navigation Menu */
        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .nav-button {
            background: #333333;
            color: #ffffff;
            border: 2px solid #ff0080;
            padding: 20px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            font-family: inherit;
        }

        .nav-button:hover {
            background: #ff0080;
            color: #000000;
            box-shadow: 0 0 20px #ff0080;
            transform: translateY(-2px);
        }

        .nav-button:active {
            transform: translateY(0);
        }

        /* Main Content Area */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 40px;
        }

        .content-panel {
            background: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;
        }

        .sidebar {
            background: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 20px;
        }

        /* Panel Headers */
        .panel-header {
            color: #ff0080;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333333;
        }

        /* Status Bar */
        .status-bar {
            background: #1a1a1a;
            border: 1px solid #333333;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff0080;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Forms */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #cccccc;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: #333333;
            border: 1px solid #555555;
            border-radius: 4px;
            color: #ffffff;
            font-family: inherit;
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff0080;
            box-shadow: 0 0 5px #ff0080;
        }

        .btn {
            background: #333333;
            color: #ffffff;
            border: 2px solid #ff0080;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .btn:hover {
            background: #ff0080;
            color: #000000;
        }

        .btn-primary {
            background: #ff0080;
            color: #000000;
        }

        .btn-primary:hover {
            background: #ff0080;
            box-shadow: 0 0 15px #ff0080;
        }

        /* Queue Items */
        .queue-item {
            background: #333333;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .queue-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .queue-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending { background: #ffa500; color: #000; }
        .status-processing { background: #ff0080; color: #000; }
        .status-completed { background: #00ff00; color: #000; }
        .status-failed { background: #ff0000; color: #fff; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2rem;
            }
        }

        /* Hidden sections */
        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        /* Loading spinner */
        .spinner {
            border: 3px solid #333333;
            border-top: 3px solid #ff0080;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="title">AY KNOWLEDGE BASE</div>
            <div class="version">v0.1</div>
            <div class="subtitle">Intelligent RAG-powered Knowledge Management</div>
        </div>

        <!-- Navigation Menu -->
        <div class="nav-menu">
            <button class="nav-button" onclick="showSection('crawl-url')">
                🌐 Crawl by URL
            </button>
            <button class="nav-button" onclick="showSection('crawl-search')">
                🔍 Crawl by Search
            </button>
            <button class="nav-button" onclick="showSection('queue-manager')">
                🔄 Queue Manager
            </button>
            <button class="nav-button" onclick="showSection('chat')">
                💬 Chat/Query
            </button>
            <button class="nav-button" onclick="showSection('sources')">
                📚 View Sources
            </button>
            <button class="nav-button" onclick="showSection('settings')">
                ⚙️ Settings
            </button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-panel">
                <!-- Home Section -->
                <div id="home" class="section active">
                    <div class="panel-header">Welcome to AY Knowledge Base</div>
                    <p style="color: #cccccc; line-height: 1.6; margin-bottom: 20px;">
                        Your intelligent RAG-powered knowledge management system. Use the menu above to:
                    </p>
                    <ul style="color: #cccccc; line-height: 1.8; margin-left: 20px;">
                        <li><strong>Crawl by URL:</strong> Extract content from specific web pages</li>
                        <li><strong>Crawl by Search:</strong> Discover and crawl content based on search queries</li>
                        <li><strong>Queue Manager:</strong> Monitor and manage crawling tasks</li>
                        <li><strong>Chat/Query:</strong> Ask questions about your crawled content</li>
                        <li><strong>View Sources:</strong> Browse your knowledge base</li>
                        <li><strong>Settings:</strong> Configure system preferences</li>
                    </ul>
                </div>

                <!-- Crawl by URL Section -->
                <div id="crawl-url" class="section">
                    <div class="panel-header">Crawl by URL</div>
                    <form onsubmit="startUrlCrawl(event)">
                        <div class="form-group">
                            <label class="form-label">URL to Crawl:</label>
                            <input type="url" class="form-input" id="url-input" 
                                   placeholder="https://example.com" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Depth:</label>
                            <input type="number" class="form-input" id="depth-input" 
                                   value="3" min="1" max="10">
                        </div>
                        <button type="submit" class="btn btn-primary">Start Crawling</button>
                    </form>
                    <div id="crawl-status"></div>
                </div>

                <!-- Crawl by Search Section -->
                <div id="crawl-search" class="section">
                    <div class="panel-header">Crawl by Search</div>
                    <form onsubmit="startSearchCrawl(event)">
                        <div class="form-group">
                            <label class="form-label">Search Query:</label>
                            <input type="text" class="form-input" id="search-input"
                                   placeholder="Enter search terms..." required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Results:</label>
                            <input type="number" class="form-input" id="max-results-input"
                                   value="10" min="1" max="50">
                        </div>
                        <button type="submit" class="btn btn-primary">Start Search Crawl</button>
                    </form>
                    <div id="search-status"></div>
                </div>

                <!-- Queue Manager Section -->
                <div id="queue-manager" class="section">
                    <div class="panel-header">Queue Manager</div>
                    <div style="margin-bottom: 20px;">
                        <button class="btn" onclick="refreshQueue()">🔄 Refresh</button>
                        <button class="btn" onclick="clearQueue()">🗑️ Clear Completed</button>
                    </div>
                    <div id="queue-list">
                        <div class="queue-item">
                            <div class="queue-item-header">
                                <span>Sample Task</span>
                                <span class="queue-status status-pending">Pending</span>
                            </div>
                            <div style="color: #cccccc; font-size: 0.9rem;">
                                URL: https://example.com
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat/Query Section -->
                <div id="chat" class="section">
                    <div class="panel-header">Chat with Your Knowledge Base</div>
                    <div id="chat-messages" style="height: 300px; overflow-y: auto; border: 1px solid #333; padding: 15px; margin-bottom: 20px; background: #0a0a0a;">
                        <div style="color: #ff0080; margin-bottom: 10px;">
                            <strong>AI Assistant:</strong> Hello! Ask me anything about your crawled content.
                        </div>
                    </div>
                    <form onsubmit="sendMessage(event)">
                        <div class="form-group">
                            <input type="text" class="form-input" id="message-input"
                                   placeholder="Ask a question about your knowledge base..." required>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>

                <!-- View Sources Section -->
                <div id="sources" class="section">
                    <div class="panel-header">Knowledge Base Sources</div>
                    <div style="margin-bottom: 20px;">
                        <button class="btn" onclick="refreshSources()">🔄 Refresh</button>
                        <input type="text" class="form-input" id="source-filter"
                               placeholder="Filter sources..." style="width: 300px; display: inline-block; margin-left: 10px;">
                    </div>
                    <div id="sources-list">
                        <div class="queue-item">
                            <div class="queue-item-header">
                                <span>example.com</span>
                                <span style="color: #cccccc;">15 documents</span>
                            </div>
                            <div style="color: #888; font-size: 0.9rem;">
                                Last crawled: 2 hours ago
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settings" class="section">
                    <div class="panel-header">Settings</div>
                    <form onsubmit="saveSettings(event)">
                        <div class="form-group">
                            <label class="form-label">Default Crawl Depth:</label>
                            <input type="number" class="form-input" id="default-depth"
                                   value="3" min="1" max="10">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Max Concurrent Crawls:</label>
                            <input type="number" class="form-input" id="max-concurrent"
                                   value="5" min="1" max="20">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Chunk Size:</label>
                            <input type="number" class="form-input" id="chunk-size"
                                   value="5000" min="1000" max="10000">
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" id="auto-refresh" checked>
                                Auto-refresh queue status
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary">Save Settings</button>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <div class="panel-header">System Status</div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>Redis Queue: <span id="redis-status">Connected</span></span>
                </div>
                <div class="status-item" style="margin-top: 10px;">
                    <div class="status-indicator"></div>
                    <span>MCP Server: <span id="mcp-status">Active</span></span>
                </div>
                
                <div class="panel-header" style="margin-top: 30px;">Quick Stats</div>
                <div style="color: #cccccc;">
                    <p>Total Documents: <span id="total-docs">0</span></p>
                    <p>Active Tasks: <span id="active-tasks">0</span></p>
                    <p>Sources: <span id="total-sources">0</span></p>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <span>Ready</span>
            </div>
            <div class="status-item">
                <span id="current-time"></span>
            </div>
        </div>
    </div>

    <script>
        // AY Knowledge Base Dashboard JavaScript

        // Global state
        let currentQueue = [];
        let currentSources = [];
        let chatHistory = [];

        // Navigation
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            const section = document.getElementById(sectionId);
            if (section) {
                section.classList.add('active');
            } else {
                document.getElementById('home').classList.add('active');
            }
        }

        // API Configuration
        const API_BASE = 'http://localhost:8080/api';

        // Crawl by URL
        async function startUrlCrawl(event) {
            event.preventDefault();
            const url = document.getElementById('url-input').value;
            const depth = document.getElementById('depth-input').value;

            document.getElementById('crawl-status').innerHTML = `
                <div class="spinner"></div>
                <p style="text-align: center; color: #ff0080; margin-top: 10px;">
                    Starting crawl for: ${url}
                </p>
            `;

            try {
                const response = await fetch(`${API_BASE}/crawl/url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        max_depth: parseInt(depth)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('crawl-status').innerHTML = `
                        <div style="background: #1a4a1a; border: 1px solid #00ff00; padding: 15px; border-radius: 4px; margin-top: 20px;">
                            <strong style="color: #00ff00;">✓ Crawl Started Successfully!</strong><br>
                            <span style="color: #cccccc;">URL: ${url}</span><br>
                            <span style="color: #cccccc;">Depth: ${depth}</span><br>
                            <span style="color: #cccccc;">Task ID: ${result.task_id}</span><br>
                            <span style="color: #888;">Check Queue Manager for progress...</span>
                        </div>
                    `;
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Unknown error');
                }
            } catch (error) {
                document.getElementById('crawl-status').innerHTML = `
                    <div style="background: #4a1a1a; border: 1px solid #ff0000; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <strong style="color: #ff0000;">✗ Error Starting Crawl</strong><br>
                        <span style="color: #cccccc;">${error.message}</span>
                    </div>
                `;
            }
        }

        // Crawl by Search
        async function startSearchCrawl(event) {
            event.preventDefault();
            const query = document.getElementById('search-input').value;
            const maxResults = document.getElementById('max-results-input').value;

            document.getElementById('search-status').innerHTML = `
                <div class="spinner"></div>
                <p style="text-align: center; color: #ff0080; margin-top: 10px;">
                    Searching for: "${query}"
                </p>
            `;

            try {
                const response = await fetch(`${API_BASE}/crawl/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        max_results: parseInt(maxResults)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('search-status').innerHTML = `
                        <div style="background: #1a4a1a; border: 1px solid #00ff00; padding: 15px; border-radius: 4px; margin-top: 20px;">
                            <strong style="color: #00ff00;">✓ Search Crawl Started!</strong><br>
                            <span style="color: #cccccc;">Query: "${query}"</span><br>
                            <span style="color: #cccccc;">Max Results: ${maxResults}</span><br>
                            <span style="color: #cccccc;">Task ID: ${result.task_id}</span><br>
                            <span style="color: #888;">Found URLs will be added to queue...</span>
                        </div>
                    `;
                    refreshQueue();
                } else {
                    throw new Error(result.error || 'Unknown error');
                }
            } catch (error) {
                document.getElementById('search-status').innerHTML = `
                    <div style="background: #4a1a1a; border: 1px solid #ff0000; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <strong style="color: #ff0000;">✗ Error Starting Search</strong><br>
                        <span style="color: #cccccc;">${error.message}</span>
                    </div>
                `;
            }
        }

        // Queue Management
        function addToQueue(item, status) {
            const queueItem = {
                id: Date.now(),
                item: item,
                status: status,
                timestamp: new Date().toLocaleString()
            };
            currentQueue.push(queueItem);
            updateQueueDisplay();
            updateStats();
        }

        function updateQueueDisplay() {
            const queueList = document.getElementById('queue-list');
            if (currentQueue.length === 0) {
                queueList.innerHTML = '<p style="color: #888; text-align: center;">No tasks in queue</p>';
                return;
            }

            queueList.innerHTML = currentQueue.map(item => `
                <div class="queue-item">
                    <div class="queue-item-header">
                        <span>${item.item}</span>
                        <span class="queue-status status-${item.status}">${item.status.toUpperCase()}</span>
                    </div>
                    <div style="color: #cccccc; font-size: 0.9rem;">
                        Added: ${item.timestamp}
                    </div>
                </div>
            `).join('');
        }

        async function refreshQueue() {
            document.getElementById('queue-list').innerHTML = '<div class="spinner"></div>';

            try {
                const response = await fetch(`${API_BASE}/queue/status`);
                const result = await response.json();

                if (result.success) {
                    currentQueue = result.tasks;
                    updateQueueDisplay();
                } else {
                    throw new Error(result.error || 'Failed to fetch queue');
                }
            } catch (error) {
                document.getElementById('queue-list').innerHTML = `
                    <p style="color: #ff0000; text-align: center;">Error loading queue: ${error.message}</p>
                `;
            }
        }

        function clearQueue() {
            currentQueue = currentQueue.filter(item => item.status !== 'completed');
            updateQueueDisplay();
            updateStats();
        }

        // Chat functionality
        function sendMessage(event) {
            event.preventDefault();
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();

            if (!message) return;

            // Add user message
            addChatMessage('user', message);
            messageInput.value = '';

            // Simulate AI response
            setTimeout(() => {
                const responses = [
                    "I found relevant information in your knowledge base...",
                    "Based on the crawled content, here's what I can tell you...",
                    "Let me search through your documents for that information...",
                    "I can help you with that based on your crawled sources..."
                ];
                const response = responses[Math.floor(Math.random() * responses.length)];
                addChatMessage('ai', response);
            }, 1500);
        }

        function addChatMessage(sender, message) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '15px';

            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div style="color: #ffffff; margin-bottom: 5px;">
                        <strong>You:</strong> ${message}
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div style="color: #ff0080; margin-bottom: 5px;">
                        <strong>AI Assistant:</strong> ${message}
                    </div>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Sources management
        function refreshSources() {
            document.getElementById('sources-list').innerHTML = '<div class="spinner"></div>';
            setTimeout(() => {
                updateSourcesDisplay();
            }, 1000);
        }

        function updateSourcesDisplay() {
            const sourcesList = document.getElementById('sources-list');
            const sampleSources = [
                { domain: 'example.com', docs: 15, lastCrawl: '2 hours ago' },
                { domain: 'docs.python.org', docs: 42, lastCrawl: '1 day ago' },
                { domain: 'github.com', docs: 8, lastCrawl: '3 hours ago' }
            ];

            sourcesList.innerHTML = sampleSources.map(source => `
                <div class="queue-item">
                    <div class="queue-item-header">
                        <span>${source.domain}</span>
                        <span style="color: #cccccc;">${source.docs} documents</span>
                    </div>
                    <div style="color: #888; font-size: 0.9rem;">
                        Last crawled: ${source.lastCrawl}
                    </div>
                </div>
            `).join('');
        }

        // Settings
        function saveSettings(event) {
            event.preventDefault();

            // Show success message
            const settingsForm = event.target;
            const successDiv = document.createElement('div');
            successDiv.style.cssText = 'background: #1a4a1a; border: 1px solid #00ff00; padding: 10px; border-radius: 4px; margin-top: 15px; color: #00ff00;';
            successDiv.textContent = '✓ Settings saved successfully!';

            settingsForm.appendChild(successDiv);
            setTimeout(() => successDiv.remove(), 3000);
        }

        // Update stats
        function updateStats() {
            document.getElementById('total-docs').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('active-tasks').textContent = currentQueue.filter(item => item.status === 'processing').length;
            document.getElementById('total-sources').textContent = Math.floor(Math.random() * 10) + 5;
        }

        // Update time
        function updateTime() {
            document.getElementById('current-time').textContent =
                new Date().toLocaleTimeString();
        }

        // Real-time updates
        async function updateStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;
                    document.getElementById('total-docs').textContent = stats.total_documents;
                    document.getElementById('active-tasks').textContent = stats.active_tasks;
                    document.getElementById('total-sources').textContent = stats.total_sources;
                    document.getElementById('redis-status').textContent = stats.redis_status;
                    document.getElementById('mcp-status').textContent = stats.mcp_status;
                }
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        async function updateSourcesDisplay() {
            try {
                const response = await fetch(`${API_BASE}/sources`);
                const result = await response.json();

                if (result.success) {
                    const sourcesList = document.getElementById('sources-list');
                    sourcesList.innerHTML = result.sources.map(source => `
                        <div class="queue-item">
                            <div class="queue-item-header">
                                <span>${source.domain}</span>
                                <span style="color: #cccccc;">${source.documents} documents</span>
                            </div>
                            <div style="color: #888; font-size: 0.9rem;">
                                Last crawled: ${source.last_crawl}
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('Error updating sources:', error);
            }
        }

        // Auto-refresh functionality
        function startAutoRefresh() {
            setInterval(updateStats, 5000);      // Update stats every 5 seconds
            setInterval(refreshQueue, 10000);    // Refresh queue every 10 seconds
            setInterval(updateSourcesDisplay, 30000); // Update sources every 30 seconds
        }

        // Initialize
        setInterval(updateTime, 1000);
        updateTime();
        updateStats();
        updateSourcesDisplay();
        startAutoRefresh();

        // Initial queue load
        refreshQueue();
    </script>
</body>
</html>
