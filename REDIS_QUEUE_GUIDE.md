# Redis Queue System Guide

## Overview

The AY RAG MCP Server now includes a Redis-based job queue system to handle long-running crawl operations asynchronously. This prevents timeout issues with Claude Desktop and enables better scalability.

## Architecture

### Components

1. **Redis Server**: Stores job queue, status, and results
2. **Job Worker**: Background process that executes crawl jobs
3. **MCP Server**: API that accepts jobs and provides status updates

### Job Flow

```
Client Request → Job Queue → Background Worker → Results Storage → Status Updates
```

## New API Endpoints

### 1. `smart_crawl_url` (Modified)

Now returns immediately with a job ID instead of blocking until completion.

**Response**:
```json
{
  "success": true,
  "job_id": "uuid-here",
  "status": "queued",
  "message": "Crawl job submitted. Use get_crawl_job_status to check progress.",
  "url": "https://example.com",
  "parameters": {
    "max_depth": 3,
    "max_concurrent": 10,
    "chunk_size": 5000
  }
}
```

### 2. `get_crawl_job_status` (New)

Check the status of a submitted crawl job.

**Parameters**:
- `job_id`: Job ID returned from smart_crawl_url

**Response (Queued)**:
```json
{
  "success": true,
  "job_id": "uuid-here",
  "status": "queued",
  "retry_count": 0
}
```

**Response (Processing)**:
```json
{
  "success": true,
  "job_id": "uuid-here",
  "status": "processing",
  "started_at": "2025-01-21T10:30:00Z",
  "retry_count": 0
}
```

**Response (Completed)**:
```json
{
  "success": true,
  "job_id": "uuid-here",
  "status": "completed",
  "started_at": "2025-01-21T10:30:00Z",
  "completed_at": "2025-01-21T10:35:00Z",
  "duration_seconds": 300,
  "retry_count": 0,
  "result": {
    "success": true,
    "url": "https://example.com",
    "total_pages_crawled": 25,
    "total_chunks_stored": 150,
    "sources_crawled": ["example.com"],
    "crawl_type": "smart_crawl"
  }
}
```

**Response (Failed)**:
```json
{
  "success": true,
  "job_id": "uuid-here",
  "status": "failed",
  "started_at": "2025-01-21T10:30:00Z",
  "completed_at": "2025-01-21T10:31:00Z",
  "duration_seconds": 60,
  "retry_count": 1,
  "error": "Network timeout error"
}
```

### 3. `cancel_crawl_job` (New)

Cancel a queued crawl job.

**Parameters**:
- `job_id`: Job ID to cancel

**Response**:
```json
{
  "success": true,
  "message": "Job uuid-here cancelled successfully",
  "job_id": "uuid-here"
}
```

### 4. `get_queue_stats` (New)

Get statistics about the job queue.

**Response**:
```json
{
  "success": true,
  "queued_jobs": 3,
  "processing_jobs": 1,
  "total_active_jobs": 4
}
```

## Docker Deployment

The updated `docker-compose.yml` includes:

1. **Redis Container**: Persistent storage with health checks
2. **Worker Container**: Background job processor
3. **MCP Server**: Main API with Redis connectivity

### Quick Start

```bash
# Start all services
docker-compose up -d

# Check service health
docker-compose ps

# View logs
docker-compose logs -f worker
docker-compose logs -f ay-rag-mcp
```

### Environment Variables

Add to your `.env` file:

```bash
# Redis Configuration
REDIS_URL=redis://localhost:6379

# Worker Configuration  
WORKER_ID=worker-1
WORKER_CONCURRENCY=3
```

## Development Setup

### Local Development

1. **Start Redis**:
```bash
docker run -d -p 6379:6379 redis:7-alpine
```

2. **Start Worker**:
```bash
cd /path/to/mcp-crawl4ai-rag
python -m src.job_worker
```

3. **Start MCP Server**:
```bash
python src/ay_rag_mcp.py
```

## Job Management

### Job States

- **queued**: Job submitted, waiting for worker
- **processing**: Worker is executing the job  
- **completed**: Job finished successfully
- **failed**: Job failed with error
- **cancelled**: Job was cancelled before processing

### Job Lifecycle

1. Client submits crawl request
2. Job created with UUID and queued
3. Worker picks up job and starts processing
4. Status updates throughout crawling
5. Results stored and status marked complete
6. Client can retrieve results via status endpoint

### Error Handling

- **Automatic Retries**: Jobs retry up to 3 times on failure
- **Timeout Protection**: Jobs timeout after 1 hour
- **Circuit Breaker**: Failed workers are temporarily disabled
- **Graceful Shutdown**: Workers finish current jobs before stopping

## Monitoring

### Health Checks

- Redis: `redis-cli ping`
- Worker: Process monitoring
- MCP Server: `/ready` endpoint

### Logging

- Worker logs: Job processing details
- MCP Server logs: API requests and responses
- Redis logs: Connection and persistence

### Metrics

Use `get_queue_stats` to monitor:
- Queue depth
- Processing capacity
- Job throughput

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis is running
   - Verify REDIS_URL environment variable
   - Check network connectivity

2. **Jobs Stuck in Queue**
   - Ensure worker is running
   - Check worker logs for errors
   - Verify worker has access to required services

3. **Job Failures**
   - Check error messages in job status
   - Review worker logs
   - Verify environment variables (API keys, etc.)

### Performance Tuning

1. **Worker Concurrency**
   - Increase `WORKER_CONCURRENCY` for more parallel jobs
   - Monitor memory/CPU usage
   - Balance with API rate limits

2. **Redis Memory**
   - Jobs are stored for 24 hours
   - Configure Redis memory policies
   - Monitor memory usage

3. **Job Timeouts**
   - Adjust timeout values for large crawls
   - Consider breaking large jobs into smaller chunks

## Migration Guide

### From Synchronous to Async

1. **Update Client Code**:
   - Handle job ID responses from `smart_crawl_url`
   - Poll `get_crawl_job_status` for completion
   - Process results from status response

2. **Example Client Pattern**:
```python
# Submit job
response = await smart_crawl_url(url="https://example.com")
job_id = response["job_id"]

# Poll for completion
while True:
    status = await get_crawl_job_status(job_id)
    if status["status"] in ["completed", "failed"]:
        break
    await asyncio.sleep(5)  # Wait 5 seconds

# Process results
if status["status"] == "completed":
    results = status["result"]
    # Process crawl results
else:
    error = status["error"]
    # Handle error
```

### Backward Compatibility

- `crawl_single_page` remains synchronous for quick single-page crawls
- All other MCP tools unchanged
- Environment variables are backward compatible

## Best Practices

1. **Job Submission**
   - Always store job IDs for tracking
   - Handle job submission errors gracefully
   - Consider job priorities for important crawls

2. **Status Polling**
   - Use reasonable polling intervals (5-10 seconds)
   - Implement exponential backoff
   - Handle network errors in polling

3. **Resource Management**
   - Monitor queue depth
   - Scale workers based on demand
   - Clean up completed job data

4. **Error Handling**
   - Always check job status before processing results
   - Implement retry logic for transient failures
   - Log job failures for debugging

## Security Considerations

1. **Redis Security**
   - Use Redis AUTH in production
   - Limit Redis network access
   - Consider Redis SSL/TLS

2. **Job Data**
   - Jobs contain URL and parameters
   - Results may contain sensitive content
   - Implement data retention policies

3. **Access Control**
   - Job IDs provide access to results
   - Consider job ownership models
   - Implement authorization if needed