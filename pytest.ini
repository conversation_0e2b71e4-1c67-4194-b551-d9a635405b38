[tool:pytest]
minversion = 6.0
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    tui: TUI-specific tests
    slow: Slow running tests
    async: Async tests
    security: Security tests
    external: Tests requiring external services
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function