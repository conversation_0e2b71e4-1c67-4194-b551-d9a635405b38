#!/usr/bin/env python3
"""
Wait-for-MC<PERSON> Script

A simple script that waits for the MCP server to become ready before exiting.
Useful for container orchestration and CI/CD pipelines.

Usage:
    python wait-for-mcp.py [--url URL] [--timeout TIMEOUT]
    
Example:
    python wait-for-mcp.py --url http://localhost:8051 --timeout 120
"""
import asyncio
import argparse
import sys
import logging
from src.mcp_client_retry import wait_for_mcp_server

async def main():
    parser = argparse.ArgumentParser(description="Wait for MCP server to become ready")
    parser.add_argument(
        "--url", 
        default="http://localhost:8051",
        help="MCP server URL (default: http://localhost:8051)"
    )
    parser.add_argument(
        "--timeout", 
        type=float, 
        default=120.0,
        help="Maximum wait time in seconds (default: 120)"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    level = logging.INFO if args.verbose else logging.WARNING
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    
    logger.info(f"Waiting for MCP server at {args.url} (timeout: {args.timeout}s)")
    
    try:
        success = await wait_for_mcp_server(args.url, args.timeout)
        
        if success:
            logger.info("✅ MCP server is ready!")
            print("MCP server is ready")
            sys.exit(0)
        else:
            logger.error("❌ Timeout waiting for MCP server")
            print("Timeout waiting for MCP server", file=sys.stderr)
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️ Interrupted by user")
        print("Interrupted", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())