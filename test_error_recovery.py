#!/usr/bin/env python3
"""
Error Recovery and Resilience Testing for Redis Queue System
Tests system behavior during failure scenarios and recovery capabilities
"""

import asyncio
import time
import json
import random
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue, JobRequest, JobStatus


class ErrorRecoveryTestSuite:
    """Error recovery and resilience testing suite"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.test_results = []
    
    async def test_network_interruption_recovery(self, num_jobs: int = 100) -> Dict[str, Any]:
        """Test recovery after simulated network interruption"""
        print(f"🌐 Testing network interruption recovery ({num_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "network-recovery-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("network-recovery-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Phase 1: Normal operations
        successful_before_interruption = 0
        job_ids_created = []
        
        for i in range(num_jobs // 3):
            try:
                job = JobRequest.create(
                    job_type="network_recovery_test",
                    url=f"https://pre-interruption-{i}.com",
                    parameters={"phase": "before_interruption", "job_id": i}
                )
                job_id = await queue.enqueue_job(job)
                job_ids_created.append(job_id)
                successful_before_interruption += 1
            except Exception as e:
                print(f"Error in pre-interruption phase: {e}")
        
        # Phase 2: Simulate network issues (connection pool exhaustion)
        print("   🔌 Simulating network issues...")
        connection_errors = 0
        failed_during_interruption = 0
        
        # Create many connections to exhaust pool
        temp_queues = []
        try:
            for i in range(50):  # Create more connections than pool limit
                temp_queue = RedisJobQueue(self.redis_url, f"temp-{i}")
                await temp_queue.connect()
                temp_queues.append(temp_queue)
        except Exception as e:
            print(f"Expected connection exhaustion: {e}")
        
        # Try operations during "network issues"
        for i in range(num_jobs // 3):
            try:
                job = JobRequest.create(
                    job_type="network_recovery_test",
                    url=f"https://during-interruption-{i}.com",
                    parameters={"phase": "during_interruption", "job_id": i}
                )
                job_id = await queue.enqueue_job(job)
                job_ids_created.append(job_id)
            except Exception as e:
                connection_errors += 1
                failed_during_interruption += 1
        
        # Clean up temp connections to simulate recovery
        for temp_queue in temp_queues:
            try:
                await temp_queue.disconnect()
            except:
                pass
        
        # Wait for connection pool to recover
        await asyncio.sleep(2)
        
        # Phase 3: Recovery operations
        print("   🔄 Testing recovery capabilities...")
        successful_after_recovery = 0
        
        for i in range(num_jobs // 3):
            try:
                job = JobRequest.create(
                    job_type="network_recovery_test",
                    url=f"https://post-recovery-{i}.com",
                    parameters={"phase": "after_recovery", "job_id": i}
                )
                job_id = await queue.enqueue_job(job)
                job_ids_created.append(job_id)
                
                # Also test status updates during recovery
                await queue.update_job_status(job_id, JobStatus.PROCESSING)
                await queue.update_job_status(job_id, JobStatus.COMPLETED)
                
                successful_after_recovery += 1
            except Exception as e:
                print(f"Recovery error: {e}")
        
        # Test that previously created jobs are still accessible
        accessible_jobs = 0
        for job_id in job_ids_created:
            try:
                status = await queue.get_job_status(job_id)
                if status is not None:
                    accessible_jobs += 1
            except:
                pass
        
        await queue.disconnect()
        
        recovery_rate = successful_after_recovery / (num_jobs // 3)
        data_persistence_rate = accessible_jobs / len(job_ids_created) if job_ids_created else 0
        
        result = {
            "test_name": "network_interruption_recovery",
            "total_jobs": num_jobs,
            "successful_before_interruption": successful_before_interruption,
            "failed_during_interruption": failed_during_interruption,
            "successful_after_recovery": successful_after_recovery,
            "connection_errors": connection_errors,
            "accessible_jobs": accessible_jobs,
            "total_created_jobs": len(job_ids_created),
            "recovery_rate": recovery_rate,
            "data_persistence_rate": data_persistence_rate,
            "network_resilient": recovery_rate >= 0.9 and data_persistence_rate >= 0.8
        }
        
        print(f"   ✅ Recovery Rate: {recovery_rate * 100:.1f}%")
        print(f"   💾 Data Persistence: {data_persistence_rate * 100:.1f}%")
        print(f"   🌐 Network Resilient: {'✅ YES' if result['network_resilient'] else '❌ NO'}")
        
        return result
    
    async def test_malformed_data_handling(self) -> Dict[str, Any]:
        """Test system behavior with malformed Redis data"""
        print("🔧 Testing malformed data handling...")
        
        queue = RedisJobQueue(self.redis_url, "malformed-data-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("malformed-data-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Test cases for malformed data
        test_cases = [
            ("invalid_json", "{ invalid json }"),
            ("missing_fields", json.dumps({"partial": "data"})),
            ("wrong_data_types", json.dumps({"status": 12345, "started_at": "not_a_date"})),
            ("empty_data", ""),
            ("null_data", None),
            ("corrupted_status", json.dumps({"status": "unknown_status"})),
        ]
        
        successful_recoveries = 0
        graceful_failures = 0
        system_crashes = 0
        
        for test_name, malformed_data in test_cases:
            try:
                # First create a normal job
                job = JobRequest.create(
                    job_type="malformed_data_test",
                    url=f"https://malformed-{test_name}.com",
                    parameters={"test_case": test_name}
                )
                job_id = await queue.enqueue_job(job)
                
                # Manually corrupt the Redis data
                redis_client = queue._get_redis_client()
                result_key = f"malformed-data-test:result:{job_id}"
                
                if malformed_data is not None:
                    await redis_client.hset(result_key, "corrupted_field", malformed_data)
                    await redis_client.hset(result_key, "status", "corrupted")
                else:
                    await redis_client.delete(result_key)
                
                await redis_client.aclose()
                
                # Try to retrieve the job status
                status = await queue.get_job_status(job_id)
                
                if status is not None:
                    # System recovered gracefully
                    successful_recoveries += 1
                    print(f"   ✅ {test_name}: Graceful recovery")
                else:
                    # System handled failure gracefully (returned None)
                    graceful_failures += 1
                    print(f"   ⚠️ {test_name}: Graceful failure handling")
                
            except Exception as e:
                # Check if it's a system crash or expected error
                if "Too many connections" in str(e):
                    graceful_failures += 1
                    print(f"   ⚠️ {test_name}: Connection limit (expected)")
                else:
                    system_crashes += 1
                    print(f"   ❌ {test_name}: System error - {e}")
        
        await queue.disconnect()
        
        total_tests = len(test_cases)
        robustness_score = (successful_recoveries + graceful_failures) / total_tests
        
        result = {
            "test_name": "malformed_data_handling",
            "total_test_cases": total_tests,
            "successful_recoveries": successful_recoveries,
            "graceful_failures": graceful_failures,
            "system_crashes": system_crashes,
            "robustness_score": robustness_score,
            "data_corruption_resilient": system_crashes == 0
        }
        
        print(f"   📊 Robustness Score: {robustness_score * 100:.1f}%")
        print(f"   🛡️ Corruption Resilient: {'✅ YES' if result['data_corruption_resilient'] else '❌ NO'}")
        
        return result
    
    async def test_job_timeout_handling(self, timeout_jobs: int = 20) -> Dict[str, Any]:
        """Test handling of job timeouts and cleanup"""
        print(f"⏰ Testing job timeout handling ({timeout_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "timeout-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("timeout-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        # Create jobs with very short timeout
        timeout_job_ids = []
        for i in range(timeout_jobs):
            job = JobRequest.create(
                job_type="timeout_test",
                url=f"https://timeout-{i}.com",
                parameters={"timeout_test": True},
                timeout=1  # 1 second timeout
            )
            job_id = await queue.enqueue_job(job)
            timeout_job_ids.append(job_id)
            
            # Move to processing to simulate active job
            await queue.update_job_status(job_id, JobStatus.PROCESSING)
        
        # Manually set old timestamps to simulate timeout
        redis_client = queue._get_redis_client()
        old_timestamp = time.time() - 3700  # 1 hour and 5 minutes ago
        
        for job_id in timeout_job_ids:
            job_key = f"timeout-test:job:{job_id}"
            await redis_client.hset(job_key, "enqueued_at", str(old_timestamp))
        
        await redis_client.aclose()
        
        # Test cleanup of expired jobs
        cleaned_jobs = await queue.cleanup_expired_jobs(max_age_hours=1)
        
        # Verify cleanup worked
        jobs_marked_failed = 0
        for job_id in timeout_job_ids:
            status = await queue.get_job_status(job_id)
            if status and status.status == JobStatus.FAILED:
                jobs_marked_failed += 1
        
        await queue.disconnect()
        
        cleanup_effectiveness = jobs_marked_failed / timeout_jobs if timeout_jobs > 0 else 0
        
        result = {
            "test_name": "job_timeout_handling",
            "timeout_jobs": timeout_jobs,
            "cleaned_jobs": cleaned_jobs,
            "jobs_marked_failed": jobs_marked_failed,
            "cleanup_effectiveness": cleanup_effectiveness,
            "timeout_handling_works": cleanup_effectiveness >= 0.8
        }
        
        print(f"   🧹 Cleaned Jobs: {cleaned_jobs}")
        print(f"   ❌ Jobs Marked Failed: {jobs_marked_failed}")
        print(f"   ⏰ Timeout Handling: {'✅ WORKING' if result['timeout_handling_works'] else '❌ FAILING'}")
        
        return result
    
    async def test_redis_reconnection(self, test_operations: int = 50) -> Dict[str, Any]:
        """Test Redis reconnection capabilities"""
        print(f"🔄 Testing Redis reconnection ({test_operations} operations)...")
        
        queue = RedisJobQueue(self.redis_url, "reconnection-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("reconnection-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        successful_ops_before = 0
        successful_ops_after = 0
        reconnection_errors = 0
        
        # Phase 1: Normal operations
        for i in range(test_operations // 2):
            try:
                job = JobRequest.create(
                    job_type="reconnection_test",
                    url=f"https://before-reconnect-{i}.com",
                    parameters={"phase": "before"}
                )
                await queue.enqueue_job(job)
                successful_ops_before += 1
            except Exception as e:
                print(f"Error before reconnection: {e}")
        
        # Phase 2: Force disconnection and reconnection
        print("   🔌 Testing reconnection...")
        try:
            await queue.disconnect()
            await asyncio.sleep(1)
            await queue.connect()
        except Exception as e:
            reconnection_errors += 1
            print(f"Reconnection error: {e}")
        
        # Phase 3: Operations after reconnection
        for i in range(test_operations // 2):
            try:
                job = JobRequest.create(
                    job_type="reconnection_test",
                    url=f"https://after-reconnect-{i}.com",
                    parameters={"phase": "after"}
                )
                job_id = await queue.enqueue_job(job)
                
                # Test full lifecycle after reconnection
                await queue.update_job_status(job_id, JobStatus.PROCESSING)
                await queue.update_job_status(job_id, JobStatus.COMPLETED)
                
                successful_ops_after += 1
            except Exception as e:
                reconnection_errors += 1
                print(f"Error after reconnection: {e}")
        
        await queue.disconnect()
        
        reconnection_success_rate = successful_ops_after / (test_operations // 2) if test_operations > 0 else 0
        
        result = {
            "test_name": "redis_reconnection",
            "test_operations": test_operations,
            "successful_ops_before": successful_ops_before,
            "successful_ops_after": successful_ops_after,
            "reconnection_errors": reconnection_errors,
            "reconnection_success_rate": reconnection_success_rate,
            "reconnection_works": reconnection_success_rate >= 0.9 and reconnection_errors <= 2
        }
        
        print(f"   ✅ Success Rate After Reconnection: {reconnection_success_rate * 100:.1f}%")
        print(f"   🔄 Reconnection Errors: {reconnection_errors}")
        print(f"   🔌 Reconnection: {'✅ WORKING' if result['reconnection_works'] else '❌ FAILING'}")
        
        return result
    
    async def test_concurrent_error_scenarios(self, error_jobs: int = 100) -> Dict[str, Any]:
        """Test system behavior with concurrent errors"""
        print(f"⚡ Testing concurrent error scenarios ({error_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "concurrent-errors-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("concurrent-errors-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        async def error_prone_worker(worker_id: int, jobs_per_worker: int) -> Dict[str, int]:
            """Worker that intentionally causes various errors"""
            successful_operations = 0
            handled_errors = 0
            system_failures = 0
            
            for i in range(jobs_per_worker):
                try:
                    # Randomly introduce different error scenarios
                    error_type = random.choice([
                        "normal",  # 40% normal operations
                        "normal", 
                        "normal",
                        "normal",
                        "invalid_status",  # Try to set invalid status
                        "large_payload",   # Very large payload
                        "special_chars",   # Special characters in data
                        "concurrent_update",  # Concurrent status updates
                        "malformed_url"    # Invalid URL format
                    ])
                    
                    if error_type == "normal":
                        job = JobRequest.create(
                            job_type="concurrent_error_test",
                            url=f"https://normal-{worker_id}-{i}.com",
                            parameters={"worker": worker_id, "job": i}
                        )
                        job_id = await queue.enqueue_job(job)
                        await queue.update_job_status(job_id, JobStatus.COMPLETED)
                        successful_operations += 1
                        
                    elif error_type == "invalid_status":
                        job = JobRequest.create(
                            job_type="concurrent_error_test",
                            url=f"https://invalid-status-{worker_id}-{i}.com",
                            parameters={"error_type": "invalid_status"}
                        )
                        job_id = await queue.enqueue_job(job)
                        try:
                            # This should cause an error or be handled gracefully
                            await queue.update_job_status(job_id, "INVALID_STATUS")
                        except (ValueError, TypeError):
                            handled_errors += 1
                        except Exception:
                            system_failures += 1
                            
                    elif error_type == "large_payload":
                        large_data = "x" * 1024 * 1024  # 1MB payload
                        job = JobRequest.create(
                            job_type="concurrent_error_test",
                            url=f"https://large-payload-{worker_id}-{i}.com",
                            parameters={"large_data": large_data}
                        )
                        await queue.enqueue_job(job)
                        handled_errors += 1
                        
                    elif error_type == "special_chars":
                        job = JobRequest.create(
                            job_type="concurrent_error_test",
                            url=f"https://special-chars-{worker_id}-{i}.com",
                            parameters={"special": "测试 🎉 {'nested': {'data': 'value'}} \x00\x01"}
                        )
                        await queue.enqueue_job(job)
                        handled_errors += 1
                        
                    elif error_type == "concurrent_update":
                        job = JobRequest.create(
                            job_type="concurrent_error_test",
                            url=f"https://concurrent-{worker_id}-{i}.com",
                            parameters={"concurrent": True}
                        )
                        job_id = await queue.enqueue_job(job)
                        
                        # Rapid concurrent updates
                        await asyncio.gather(
                            queue.update_job_status(job_id, JobStatus.PROCESSING),
                            queue.update_job_status(job_id, JobStatus.PROCESSING),
                            queue.update_job_status(job_id, JobStatus.COMPLETED),
                            return_exceptions=True
                        )
                        handled_errors += 1
                        
                    elif error_type == "malformed_url":
                        job = JobRequest.create(
                            job_type="concurrent_error_test",
                            url="not://a/valid/url/format",
                            parameters={"malformed": True}
                        )
                        await queue.enqueue_job(job)
                        handled_errors += 1
                        
                except Exception as e:
                    if "Too many connections" in str(e):
                        handled_errors += 1  # Expected under high load
                    else:
                        system_failures += 1
            
            return {
                "successful_operations": successful_operations,
                "handled_errors": handled_errors,
                "system_failures": system_failures
            }
        
        # Run concurrent error-prone workers
        workers = 10
        jobs_per_worker = error_jobs // workers
        
        start_time = time.time()
        worker_tasks = [
            error_prone_worker(worker_id, jobs_per_worker)
            for worker_id in range(workers)
        ]
        
        results = await asyncio.gather(*worker_tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Aggregate results
        total_successful = sum(r["successful_operations"] for r in results if isinstance(r, dict))
        total_handled_errors = sum(r["handled_errors"] for r in results if isinstance(r, dict))
        total_system_failures = sum(r["system_failures"] for r in results if isinstance(r, dict))
        
        error_handling_rate = total_handled_errors / (total_handled_errors + total_system_failures) if (total_handled_errors + total_system_failures) > 0 else 1.0
        
        await queue.disconnect()
        
        result = {
            "test_name": "concurrent_error_scenarios",
            "error_jobs": error_jobs,
            "total_successful": total_successful,
            "total_handled_errors": total_handled_errors,
            "total_system_failures": total_system_failures,
            "error_handling_rate": error_handling_rate,
            "total_time": total_time,
            "error_resilient": total_system_failures <= 5 and error_handling_rate >= 0.8
        }
        
        print(f"   ✅ Successful Operations: {total_successful}")
        print(f"   ⚠️ Handled Errors: {total_handled_errors}")
        print(f"   ❌ System Failures: {total_system_failures}")
        print(f"   🛡️ Error Resilient: {'✅ YES' if result['error_resilient'] else '❌ NO'}")
        
        return result
    
    async def run_error_recovery_suite(self) -> Dict[str, Any]:
        """Run complete error recovery test suite"""
        print("🚀 Starting Error Recovery & Resilience Test Suite")
        print("=" * 60)
        
        # Test Redis connectivity
        try:
            test_queue = RedisJobQueue(self.redis_url, "connectivity-test")
            await test_queue.connect()
            await test_queue.disconnect()
            print("✅ Redis connectivity confirmed")
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            return {"error": "Redis connection failed"}
        
        tests = [
            ("Network Interruption Recovery", self.test_network_interruption_recovery, 100),
            ("Malformed Data Handling", self.test_malformed_data_handling),
            ("Job Timeout Handling", self.test_job_timeout_handling, 20),
            ("Redis Reconnection", self.test_redis_reconnection, 50),
            ("Concurrent Error Scenarios", self.test_concurrent_error_scenarios, 100),
        ]
        
        results = {}
        resilience_score = 0
        total_tests = 0
        
        for test_info in tests:
            test_name = test_info[0]
            test_func = test_info[1]
            test_args = test_info[2:] if len(test_info) > 2 else ()
            
            print(f"\n📊 Running {test_name}...")
            try:
                if test_args:
                    result = await test_func(*test_args)
                else:
                    result = await test_func()
                
                results[test_name] = result
                self.test_results.append(result)
                
                # Score resilience
                total_tests += 1
                resilience_factors = [
                    result.get('network_resilient', False),
                    result.get('data_corruption_resilient', False),
                    result.get('timeout_handling_works', False),
                    result.get('reconnection_works', False),
                    result.get('error_resilient', False)
                ]
                
                if any(resilience_factors):
                    resilience_score += 1
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results[test_name] = {"error": str(e)}
                total_tests += 1
        
        # Overall assessment
        results['overall_resilience_score'] = resilience_score
        results['total_resilience_tests'] = total_tests
        results['resilience_percentage'] = (resilience_score / total_tests * 100) if total_tests > 0 else 0
        results['system_resilient'] = resilience_score >= (total_tests * 0.6)  # 60% threshold
        results['test_timestamp'] = datetime.now(timezone.utc).isoformat()
        
        return results


async def main():
    """Run the error recovery test suite"""
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    error_suite = ErrorRecoveryTestSuite(redis_url)
    
    # Run error recovery tests
    results = await error_suite.run_error_recovery_suite()
    
    # Save raw results as JSON
    results_file = "error_recovery_test_results.json"
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print("\n" + "=" * 60)
    print("🎉 Error Recovery Testing Complete!")
    print(f"📊 Results saved to: {results_file}")
    
    # Print final status
    resilience_score = results.get('resilience_percentage', 0)
    print(f"🛡️ Resilience Score: {resilience_score:.1f}%")
    
    if results.get('system_resilient', False):
        print("✅ SYSTEM IS RESILIENT TO ERRORS - PRODUCTION READY")
    else:
        print("⚠️ SYSTEM NEEDS RESILIENCE IMPROVEMENTS")
    
    print("=" * 60)


if __name__ == "__main__":
    import os
    asyncio.run(main())