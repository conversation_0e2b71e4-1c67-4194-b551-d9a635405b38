#!/usr/bin/env python3
"""
AY Knowledge Base TUI - Terminal User Interface for AY RAG MCP Server

Run this script to launch the interactive TUI for managing your knowledge base.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from tui.app import run

if __name__ == "__main__":
    try:
        print("Starting AY Knowledge Base TUI v0.1...")
        print("Press Ctrl+Q to quit at any time.")
        print()
        run()
    except KeyboardInterrupt:
        print("\nTUI interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nError starting TUI: {e}")
        print("Please check your .env configuration and ensure all dependencies are installed.")
        sys.exit(1)