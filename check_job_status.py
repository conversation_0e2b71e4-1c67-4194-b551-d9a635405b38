#!/usr/bin/env python3
"""
Check status of specific job
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue

async def check_job_status():
    """Check status of the manually processed job"""
    queue = RedisJobQueue("redis://localhost:6380")
    await queue.connect()
    
    # Check the job we processed
    job_id = "d4a8ec5d-22cc-481f-b38e-1b8c9d3f43ee"
    result = await queue.get_job_status(job_id)
    
    if result:
        print(f"📊 Job {job_id} status: {result.status.value}")
        if result.error_message:
            print(f"❌ Error: {result.error_message}")
        if result.result_data:
            print(f"📄 Result: {result.result_data}")
    else:
        print(f"❌ Job {job_id} not found")
    
    await queue.disconnect()

if __name__ == "__main__":
    asyncio.run(check_job_status())