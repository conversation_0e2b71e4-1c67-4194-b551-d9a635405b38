{"suite": {"name": "AY RAG MCP Performance Benchmark", "total_duration_ms": 196.22449000598863, "success_rate": 1.0, "avg_memory_mb": 0.075, "avg_cpu_percent": 0.0}, "results": [{"operation": "code_extraction", "duration_ms": 2.32767901616171, "memory_mb": 0.0, "cpu_percent": 0.0, "success": true, "details": {"blocks_extracted": 5, "total_code_length": 1180, "languages_detected": ["javascript", "typescript", "python"], "functions_found": 3}}, {"operation": "query_enhancement", "duration_ms": 0.21787005243822932, "memory_mb": 0.0, "cpu_percent": 0.0, "success": true, "details": {"queries_processed": 5, "query_types": ["implementation_focused", "code_focused", "code_focused", "code_focused", "code_focused"], "avg_enhancement_ratio": 1.0}}, {"operation": "relevance_scoring", "duration_ms": 0.07711001671850681, "memory_mb": 0.0, "cpu_percent": 0.0, "success": true, "details": {"documents_scored": 3, "avg_relevance_score": 0.65, "score_distribution": {"min": 0.6, "max": 0.7, "std_dev": 0.04999999999999999}}}, {"operation": "concurrent_operations", "duration_ms": 1.8562300247140229, "memory_mb": 0.0, "cpu_percent": 0.0, "success": true, "details": {"concurrent_tasks": 10, "successful_tasks": 10, "success_rate": 1.0}}, {"operation": "memory_efficiency", "duration_ms": 190.6593720195815, "memory_mb": 0.375, "cpu_percent": 0.0, "success": true, "details": {"content_size_kb": 19.189453125, "processing_iterations": 10, "total_blocks_extracted": 500, "memory_efficiency": {"initial_mb": 24.375, "peak_mb": 24.75, "memory_increase_mb": 0.375, "mb_per_kb_content": 0.019541984732824428}}}], "enhanced_extraction_available": true, "timestamp": 1753089963.069157}