#!/usr/bin/env python3
"""
Simple test to verify SSE endpoint protection without hanging.
"""
import httpx
import asyncio
import sys

async def test_sse_protection():
    """Test SSE endpoint returns 503 during initialization or allows connection when ready."""
    async with httpx.AsyncClient(timeout=5.0) as client:
        # Check server status
        health_resp = await client.get("http://localhost:8051/health")
        health_data = health_resp.json()
        
        print(f"Server status: {health_data['status']}")
        print(f"Initialization complete: {health_data['initialization_complete']}")
        
        # Test SSE endpoint with timeout
        try:
            # Use stream=True to handle SSE properly
            async with client.stream('GET', 'http://localhost:8051/sse') as response:
                print(f"SSE endpoint status: {response.status_code}")
                
                if response.status_code == 503:
                    # Server not ready
                    data = await response.aread()
                    print(f"SSE protection active: {data.decode()}")
                    return True
                elif response.status_code == 200:
                    # Server ready, SSE connection established
                    print("SSE endpoint accessible (server ready)")
                    # Read just a bit to confirm it's SSE
                    async for line in response.aiter_lines():
                        print(f"SSE line: {line}")
                        break  # Exit after first line to avoid hanging
                    return True
                else:
                    print(f"Unexpected status: {response.status_code}")
                    return False
                    
        except httpx.TimeoutError:
            print("SSE connection timed out (expected for streaming)")
            return True
        except Exception as e:
            print(f"Error: {e}")
            return False

if __name__ == "__main__":
    success = asyncio.run(test_sse_protection())
    sys.exit(0 if success else 1)