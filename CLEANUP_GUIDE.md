# Project Cleanup Guide

**AY RAG MCP Server - Maintenance and Cleanup Best Practices**

## Overview

This guide documents the cleanup procedures and maintenance best practices for the AY RAG MCP Server project. It provides clear instructions for keeping the codebase clean, organized, and efficient.

## Cleanup Categories

### 1. Dead Code Removal

**Purpose**: Remove unused functions, imports, and files to reduce maintenance burden and improve code clarity.

**Process**:
```bash
# Identify unused imports
grep -r "^import\|^from" src/ | grep -v "__pycache__"

# Search for function definitions and their usage
grep -r "def function_name" . && grep -r "function_name" --exclude-dir=".git"

# Remove unused functions safely
# Only remove if no references found in active codebase
```

**Recently Removed**:
- 4 unused Docker MCP functions (`get_crawl_status`, `cancel_crawl`, `list_crawl_tasks`, `stream_crawl_progress`)
- Unused design documentation files (`enhanced_code_detection_*.md`)
- Standalone utility scripts (`optimized_patterns.py`, `performance_benchmark.py`)

### 2. Import Optimization

**Purpose**: Remove unused imports to reduce memory footprint and improve startup performance.

**Process**:
```bash
# Find imports and check usage
for file in src/*.py; do
    echo "Checking $file:"
    python -c "
import ast
import sys
with open('$file') as f:
    tree = ast.parse(f.read())
for node in ast.walk(tree):
    if isinstance(node, ast.Import):
        for alias in node.names:
            print(f'  import {alias.name}')
    elif isinstance(node, ast.ImportFrom):
        print(f'  from {node.module} import {[alias.name for alias in node.names]}')
"
done
```

**Recently Optimized**:
- Removed unused `sys` import from `ay_rag_mcp.py`
- Preserved necessary `uuid` import in `docker_mcp_tools.py`

### 3. File Organization

**Purpose**: Maintain logical project structure with proper file placement.

**Directory Structure**:
```
project/
├── src/                    # Core application code
│   ├── ay_rag_mcp.py      # Main MCP server
│   ├── docker_mcp_tools.py# Docker integration tools
│   ├── utils.py           # Utility functions
│   └── ...
├── tests/                 # All test files
│   ├── test_*.py          # Unit and integration tests
│   └── ...
├── docs/                  # Documentation (if needed)
├── .env.example           # Environment template
├── docker-compose.yml     # Container orchestration
├── pyproject.toml         # Project configuration
└── README.md              # Project overview
```

**File Naming Conventions**:
- Tests: `test_*.py` in `tests/` directory
- Core modules: descriptive names in `src/`
- Configuration: root level with clear names
- Documentation: `.md` files, consider `docs/` for multiple files

### 4. Cache and Temporary File Cleanup

**Purpose**: Remove build artifacts and temporary files that accumulate during development.

**Regular Cleanup Commands**:
```bash
# Python cache files
find . -type d -name "__pycache__" -exec rm -rf {} +
find . -name "*.pyc" -delete
find . -name "*.pyo" -delete

# Log files
find . -name "*.log" -delete

# Build artifacts
rm -rf build/ dist/ *.egg-info/

# IDE files (optional)
find . -name ".DS_Store" -delete
find . -name "Thumbs.db" -delete
```

### 5. Configuration Consolidation

**Purpose**: Maintain single source of truth for configuration with clear organization.

**Current State**:
- ✅ Single `.env.example` with organized sections
- ✅ Consolidated `docker-compose.yml` without unused variables
- ✅ Clean `pyproject.toml` with only necessary dependencies

**Best Practices**:
- Use descriptive section headers in `.env.example`
- Include helpful comments for configuration values
- Remove unused environment variables from compose files
- Keep sensitive data out of repository (use `.env` locally only)

## Maintenance Schedules

### Weekly Maintenance
- [ ] Run cache cleanup commands
- [ ] Check for new unused imports
- [ ] Review log files and clean up

### Monthly Maintenance  
- [ ] Comprehensive dead code analysis
- [ ] Review and update documentation
- [ ] Check dependency updates and security
- [ ] Validate test coverage

### Before Major Releases
- [ ] Full cleanup cycle (all categories)
- [ ] Code quality review
- [ ] Performance profiling
- [ ] Security audit

## Cleanup Automation

### Pre-commit Hooks (Recommended)
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: remove-cache
        name: Remove Python cache files
        entry: find . -type d -name "__pycache__" -exec rm -rf {} +
        language: system
        pass_filenames: false
      
      - id: check-imports
        name: Check for unused imports
        entry: python -m pyflakes src/
        language: system
        files: \.py$
```

### Cleanup Script
```bash
#!/bin/bash
# cleanup.sh - Project cleanup automation

echo "🧹 Starting project cleanup..."

# Remove cache files
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -delete 2>/dev/null
find . -name "*.log" -delete 2>/dev/null

# Remove empty directories (except git/venv)
find . -type d -empty -not -path "./.git/*" -not -path "./.venv/*" -delete 2>/dev/null

echo "✅ Cleanup completed!"
```

## Quality Gates

### Before Committing
1. **Import Check**: Verify no unused imports
2. **Dead Code Check**: Confirm no unused functions
3. **Test Validation**: All tests pass
4. **Lint Check**: Code passes linting rules

### Review Checklist
- [ ] No unused imports or functions
- [ ] All files in appropriate directories
- [ ] Configuration is clean and documented
- [ ] No sensitive data in repository
- [ ] Tests are organized and passing

## Tools and Commands

### Useful Cleanup Commands
```bash
# Find large files
find . -type f -size +1M -not -path "./.git/*" -not -path "./.venv/*"

# Find duplicate files
fdupes -r . --exclude=.git --exclude=.venv

# Check import usage
python -c "import ast; print('Static analysis tools can help identify unused imports')"

# Project statistics
cloc src/ --exclude-dir=__pycache__
```

### Code Quality Tools
```bash
# Install quality tools
pip install pyflakes black isort

# Run checks
pyflakes src/                # Check for issues
black --check src/           # Code formatting
isort --check-only src/      # Import sorting
```

## Common Pitfalls

### ❌ What NOT to Do
- Remove imports without checking all usages
- Delete test files without understanding their purpose
- Remove configuration variables still used in production
- Clean up files that are part of framework structure (.git, .venv)

### ✅ Best Practices
- Always test after cleanup
- Use version control to track changes
- Document reasons for major removals
- Maintain backward compatibility when possible
- Keep cleanup changes in separate commits

## Integration with Development Workflow

### Git Workflow
```bash
# Create cleanup branch
git checkout -b cleanup/dead-code-removal

# Perform cleanup
./cleanup.sh

# Commit changes
git add .
git commit -m "cleanup: remove dead code and optimize imports"

# Test before merging
docker-compose up --build
python -m pytest tests/

# Merge when validated
git checkout main
git merge cleanup/dead-code-removal
```

### CI/CD Integration
```yaml
# .github/workflows/cleanup-check.yml
name: Cleanup Check
on: [pull_request]
jobs:
  cleanup-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check for cache files
        run: |
          if find . -name "*.pyc" -o -name "__pycache__" | grep -q .; then
            echo "❌ Cache files found in repository"
            exit 1
          fi
      - name: Check imports
        run: python -m pyflakes src/
```

## Monitoring and Metrics

### Project Health Indicators
- **Code Coverage**: Maintain >80% test coverage
- **Import Efficiency**: No unused imports in production code
- **File Organization**: All files in correct directories
- **Configuration Clarity**: Clear, documented environment variables

### Cleanup Impact Tracking
- Lines of code reduced
- Files removed
- Performance improvements
- Maintenance burden reduction

## Related Documentation

- **[README.md](README.md)** - Project overview and setup instructions
- **[CLAUDE.md](CLAUDE.md)** - Development guidance for AI assistants
- **[INTELLIGENT_ROUTING.md](INTELLIGENT_ROUTING.md)** - Architecture and routing logic
- **[DEPRECATED_DOCS.md](DEPRECATED_DOCS.md)** - Historical documentation tracker

---

**Last Updated**: 2025-01-19  
**Next Review**: 2025-02-19  
**Maintainer**: Development Team

This guide should be updated whenever significant cleanup procedures change or new maintenance practices are established.