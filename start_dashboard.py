#!/usr/bin/env python3
"""
Startup script for AY Knowledge Base Dashboard.
"""

import asyncio
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ['aiohttp', 'aiohttp-cors', 'redis']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Missing required packages: {', '.join(missing)}")
        print("📦 Installing missing packages...")
        
        for package in missing:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    return True

def check_redis():
    """Check if Redis is running."""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        r.ping()
        print("✅ Redis is running")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("🔧 Please start Redis server:")
        print("   - Ubuntu/Debian: sudo systemctl start redis-server")
        print("   - macOS: brew services start redis")
        print("   - Docker: docker run -d -p 6379:6379 redis:alpine")
        return False

async def main():
    """Main startup function."""
    print("🚀 Starting AY Knowledge Base Dashboard...")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        return
    
    # Check Redis
    if not check_redis():
        print("❌ Redis check failed")
        return
    
    # Check if dashboard files exist
    dashboard_file = Path("dashboard.html")
    server_file = Path("dashboard_server.py")
    
    if not dashboard_file.exists():
        print("❌ dashboard.html not found")
        return
    
    if not server_file.exists():
        print("❌ dashboard_server.py not found")
        return
    
    print("✅ All checks passed!")
    print()
    
    # Import and start the dashboard server
    try:
        from dashboard_server import DashboardServer
        
        server = DashboardServer(port=8080)
        runner = await server.start()
        
        print("🎉 AY Knowledge Base Dashboard Started Successfully!")
        print()
        print("📊 Dashboard URL: http://localhost:8080")
        print("🔌 API Endpoints: http://localhost:8080/api/")
        print()
        print("🎯 Features Available:")
        print("   • 🌐 Crawl by URL")
        print("   • 🔍 Crawl by Search")
        print("   • 🔄 Queue Manager")
        print("   • 💬 Chat/Query")
        print("   • 📚 View Sources")
        print("   • ⚙️ Settings")
        print()
        print("🎨 Theme: Black/Grey/White/Neon Pink")
        print("🔄 Auto-refresh: Enabled")
        print()
        print("Press Ctrl+C to stop the server...")
        
        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down dashboard server...")
            await runner.cleanup()
            print("✅ Dashboard stopped successfully")
            
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)
