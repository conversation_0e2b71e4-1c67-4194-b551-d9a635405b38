# Claude Code Development Guide

**AY RAG MCP Server - Technical Documentation for AI Development Assistants**

This file provides comprehensive guidance for <PERSON> and other AI development assistants when working with this repository. It includes architecture details, development patterns, and operational procedures specific to the AY RAG MCP Server.

## Project Overview

**Technology Stack**: Python 3.12+, FastMCP, Crawl4AI, Supabase, OpenAI Embeddings  
**Architecture**: Microservice-based MCP server with intelligent routing and vector database integration  
**Purpose**: Enable AI agents to crawl, store, and query web content using advanced RAG strategies

### Core Value Proposition
This MCP server transforms any website into a queryable knowledge base, providing AI agents with structured access to web content through semantic search, code example extraction, and intelligent routing for optimal performance.

## Quick Reference

### Essential Commands
```bash
# Local development
uv run src/ay_rag_mcp.py

# Docker build and run
docker build -t ay-rag-mcp --build-arg PORT=8051 .
docker run --env-file .env -p 8051:8051 ay-rag-mcp

# KISS Architecture - Single container deployment
docker-compose up -d
```

## 🚀 OpenRouter Integration (NEW)

The server now supports **OpenRouter** as an LLM provider, enabling access to multiple AI models through a unified API. You can configure 3 different models for specialized tasks:

### Model Specialization
- **Contextual Embedding**: Document comprehension and chunk contextualization
- **Code Analysis**: Code extraction, understanding, and summarization  
- **Query Enhancement**: Search query improvement and source summarization

### Recommended Configurations

**Cost-Optimized Setup:**
```bash
LLM_PROVIDER=openrouter
CONTEXTUAL_EMBEDDING_MODEL=meta-llama/llama-3.1-8b-instruct
CODE_ANALYSIS_MODEL=openai/gpt-4o-mini  
QUERY_ENHANCEMENT_MODEL=meta-llama/llama-3.1-8b-instruct
```

**Quality-Optimized Setup:**
```bash
LLM_PROVIDER=openrouter
CONTEXTUAL_EMBEDDING_MODEL=anthropic/claude-3-sonnet
CODE_ANALYSIS_MODEL=openai/gpt-4o
QUERY_ENHANCEMENT_MODEL=anthropic/claude-3-haiku
```

**Balanced Setup:**
```bash
LLM_PROVIDER=openrouter
CONTEXTUAL_EMBEDDING_MODEL=anthropic/claude-3-haiku
CODE_ANALYSIS_MODEL=openai/gpt-4o-mini
QUERY_ENHANCEMENT_MODEL=meta-llama/llama-3.1-8b-instruct
```

### Migration from Single Model
Existing installations using `MODEL_CHOICE` will continue working. The new model configurations provide optional specialization while maintaining backward compatibility.

### Core MCP Tools
- `smart_crawl_url` - Intelligent crawling with automatic routing
- `perform_rag_query` - Semantic search with source filtering
- `get_available_sources` - List crawled domains
- `search_code_examples` - Code-specific search (requires agentic RAG)

### Key Configuration Flags
- `USE_HYBRID_SEARCH=true` - Combines vector + keyword search
- `USE_AGENTIC_RAG=true` - Enables code example extraction
- `USE_CONTEXTUAL_EMBEDDINGS=true` - Enhanced chunk context
- `USE_RERANKING=true` - Improved result relevance

## Key Architecture Components

### Core Server Architecture (KISS Design)
- **FastMCP Server**: Built on FastMCP framework with lifespan management for async components
- **Direct AsyncWebCrawler**: Uses AsyncWebCrawler directly for optimal performance and simplicity
- **Supabase Vector Database**: Stores crawled content chunks and code examples with pgvector embeddings
- **OpenAI Embeddings**: Uses text-embedding-3-small for semantic search capabilities
- **Async Patterns**: Proper asyncio patterns with run_in_executor for sync operations

### RAG Strategy Engine
The server implements multiple configurable RAG strategies:
- **Contextual Embeddings**: Enhances chunks with document-level context via LLM
- **Hybrid Search**: Combines vector similarity with keyword matching
- **Agentic RAG**: Specialized code example extraction and summarization
- **Reranking**: Cross-encoder model for improved result relevance

### Database Schema
- **crawled_pages**: Main content storage with vector embeddings
- **code_examples**: Specialized code block storage with summaries
- **sources**: Source metadata and statistics

## Development Commands

### Setup and Installation

**Docker Setup (Recommended):**
```bash
# Build Docker image
docker build -t ay-rag-mcp --build-arg PORT=8051 .

# Run with Docker
docker run --env-file .env -p 8051:8051 ay-rag-mcp
```

**Local Development:**
```bash
# Install dependencies
uv pip install -e .
crawl4ai-setup

# Run server
uv run src/ay_rag_mcp.py
```

### Environment Configuration

Create `.env` file with required variables:
```bash
# Core MCP Configuration
HOST=0.0.0.0
PORT=8051
TRANSPORT=sse

# LLM Provider Configuration (NEW)
LLM_PROVIDER=openai  # Options: "openai", "openrouter"
OPENAI_API_KEY=your_key
OPENROUTER_API_KEY=your_key  # Required if using OpenRouter

# Model Selection (3 specialized models)
CONTEXTUAL_EMBEDDING_MODEL=gpt-4o-mini
CODE_ANALYSIS_MODEL=gpt-4o-mini  
QUERY_ENHANCEMENT_MODEL=gpt-4o-mini

# Legacy support (fallback)
MODEL_CHOICE=gpt-4o-mini

# Supabase Configuration
SUPABASE_URL=your_url
SUPABASE_SERVICE_KEY=your_key

# RAG Strategy Flags
USE_CONTEXTUAL_EMBEDDINGS=false
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=false
USE_RERANKING=false

# Code Detection Configuration
MIN_CODE_LENGTH=50
```

### Database Setup

Initialize Supabase database:
```bash
# Run the SQL setup script in Supabase dashboard
cat crawled_pages.sql | psql $SUPABASE_CONNECTION_STRING
```

## MCP Tools Overview

### Core Crawling Tools
- **`crawl_single_page`**: Crawl individual webpage and store in vector database
- **`smart_crawl_url`**: Intelligent crawling (detects sitemaps, txt files, recursive crawling)
- **`get_available_sources`**: List all crawled sources with metadata
- **`perform_rag_query`**: Vector similarity search with optional source filtering

### Specialized Tools
- **`search_code_examples`**: Search specifically for code examples (requires USE_AGENTIC_RAG=true)

## Code Architecture Details

### Crawling Engine (`src/ay_rag_mcp.py`)
- **URL Detection**: Automatically detects sitemaps, text files, and regular webpages
- **Parallel Processing**: Configurable concurrent crawling with memory-adaptive dispatching
- **Content Chunking**: Smart markdown chunking respecting code blocks and headers
- **Context Extraction**: Section headers, word counts, and metadata extraction

### Utilities (`src/utils.py`)
- **Batch Processing**: Efficient embedding creation and database operations
- **Error Handling**: Comprehensive retry logic with exponential backoff
- **Content Processing**: Code block extraction, contextual embedding generation
- **Database Operations**: Upsert operations with foreign key management


## Key Implementation Patterns

### Error Handling Strategy
All operations implement:
- Exponential backoff retry logic
- Graceful fallback mechanisms
- Comprehensive error logging
- Individual record processing fallbacks

### Performance Optimization
- Batch operations for embeddings and database inserts
- Parallel processing for content analysis
- Memory-adaptive crawling dispatch
- Efficient vector similarity search with pgvector

### Configuration Management
- Environment-based feature flags
- Runtime validation of required components
- Conditional tool availability based on configuration
- Comprehensive startup validation

## Testing and Validation

### Manual Testing
Test core functionality:
```bash
# Test single page crawling
curl -X POST http://localhost:8051/tools/crawl_single_page -d '{"url": "https://example.com"}'

# Test smart crawling
curl -X POST http://localhost:8051/tools/smart_crawl_url -d '{"url": "https://example.com/sitemap.xml"}'

# Test RAG queries
curl -X POST http://localhost:8051/tools/perform_rag_query -d '{"query": "your search query"}'
```


## Production Considerations

### Resource Management
- Configure max_concurrent based on available memory
- Monitor embedding API rate limits
- Implement database connection pooling
- Consider chunk_size impact on retrieval quality

### Security
- Use service role keys for Supabase operations
- Validate all input URLs and file paths
- Never expose API keys in logs

### Monitoring
- Track embedding creation success rates
- Monitor database operation performance
- Log crawling statistics and errors

## Integration Examples

### Claude Code MCP Integration
```bash
claude mcp add-json ay-rag-mcp '{"type":"http","url":"http://localhost:8051/sse"}' --scope user
```

### Windsurf Integration
```json
{
  "mcpServers": {
    "ay-rag-mcp": {
      "transport": "sse",
      "serverUrl": "http://localhost:8051/sse"
    }
  }
}
```

## Common Workflows

### Basic RAG Workflow
1. Crawl documentation: `smart_crawl_url`
2. Check available sources: `get_available_sources`
3. Query content: `perform_rag_query`

### Code Example Workflow
1. Enable agentic RAG: `USE_AGENTIC_RAG=true`
2. Crawl technical documentation: `smart_crawl_url`
3. Search code examples: `search_code_examples`