#!/usr/bin/env python3
"""
Verify Crawl Results - Check stored content from URL tests

This script checks the database to verify that our conservative filtering
fix successfully preserved content from the tested URLs.
"""

import os
import psycopg2
from datetime import datetime, timedelta

# Database connection details from environment or defaults
DB_HOST = "*************"  # From logs
DB_PORT = 8000
SUPABASE_URL = os.getenv("SUPABASE_URL", f"postgresql://postgres@{DB_HOST}:5432/postgres")

def get_crawl_results():
    """Get recent crawl results from the database"""
    print("📊 Verifying Crawl Results from Database")
    print("=" * 60)
    
    # Test URLs we crawled
    test_urls = [
        "https://flask.palletsprojects.com/en/3.0.x/quickstart/",
        "https://httpbin.org/html",
        "https://www.python.org/dev/peps/pep-8/",
        "https://docs.python.org/3/tutorial/datastructures.html",
        "https://docs.python.org/3/tutorial/controlflow.html"
    ]
    
    try:
        # Try to connect via Supabase REST API instead of direct DB
        import requests
        
        print("🔍 Checking available sources...")
        
        # Get sources from REST API
        sources_url = f"http://{DB_HOST}:8000/rest/v1/sources?select=*"
        headers = {
            "apikey": os.getenv("SUPABASE_ANON_KEY", ""),
            "Authorization": f"Bearer {os.getenv('SUPABASE_ANON_KEY', '')}"
        }
        
        response = requests.get(sources_url, headers=headers)
        
        if response.status_code == 200:
            sources = response.json()
            print(f"✅ Found {len(sources)} sources in database")
            
            for source in sources:
                print(f"   📁 {source.get('source_id', 'Unknown')}: {source.get('document_count', 0)} docs")
        else:
            print(f"❌ Could not fetch sources: {response.status_code}")
            return False
        
        # Get crawled pages summary
        pages_url = f"http://{DB_HOST}:8000/rest/v1/crawled_pages?select=url,content&limit=5"
        response = requests.get(pages_url, headers=headers)
        
        if response.status_code == 200:
            pages = response.json()
            print(f"\n📄 Recent crawled pages ({len(pages)} shown):")
            
            for page in pages:
                url = page.get('url', 'Unknown URL')
                content = page.get('content', '')
                content_length = len(content)
                
                # Check if this is one of our test URLs
                is_test_url = any(test_url in url for test_url in test_urls)
                status = "🎯" if is_test_url else "📄"
                
                print(f"   {status} {url}: {content_length} chars")
                
                # Show content preview for test URLs
                if is_test_url and content:
                    preview = content[:200].replace('\n', ' ')
                    print(f"      Preview: {preview}...")
                    
                    # Quick assessment
                    if content_length < 100:
                        print(f"      🚨 VERY SHORT - potential issue!")
                    elif content_length < 500:
                        print(f"      ⚠️  SHORT - check content quality")
                    else:
                        print(f"      ✅ GOOD length")
        else:
            print(f"❌ Could not fetch pages: {response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False


def check_regression_fix():
    """Check if the Flask quickstart regression is fixed"""
    print("\n🔍 REGRESSION CHECK: Flask Quickstart")
    print("=" * 50)
    
    try:
        import requests
        
        # Query specifically for Flask quickstart
        url = f"http://{DB_HOST}:8000/rest/v1/crawled_pages"
        params = {
            "select": "url,content",
            "url": "like.*flask.palletsprojects.com*quickstart*",
            "limit": 1
        }
        headers = {
            "apikey": os.getenv("SUPABASE_ANON_KEY", ""),
            "Authorization": f"Bearer {os.getenv('SUPABASE_ANON_KEY', '')}"
        }
        
        response = requests.get(url, params=params, headers=headers)
        
        if response.status_code == 200:
            results = response.json()
            
            if results:
                content = results[0].get('content', '')
                content_length = len(content)
                
                print(f"📊 Flask quickstart content: {content_length} chars")
                
                # Original issue was 86 chars
                if content_length <= 100:
                    print(f"❌ REGRESSION STILL EXISTS: Only {content_length} chars (was 86)")
                    return False
                elif content_length < 1000:
                    print(f"⚠️  PARTIAL FIX: {content_length} chars (improvement from 86)")
                    print(f"   Still below expected ~5000+ chars for Flask docs")
                    return False
                else:
                    print(f"✅ REGRESSION FIXED: {content_length} chars (was 86)")
                    print(f"   Significant improvement!")
                    
                    # Show content preview
                    if content:
                        preview = content[:300].replace('\n', ' ')
                        print(f"   Preview: {preview}...")
                        
                        # Check for key Flask content
                        flask_indicators = ['Flask', 'application', '@app.route', 'quickstart']
                        found_indicators = [ind for ind in flask_indicators if ind.lower() in content.lower()]
                        
                        print(f"   Key content found: {found_indicators}")
                        
                        if len(found_indicators) >= 3:
                            print(f"   ✅ Contains expected Flask documentation content")
                            return True
                        else:
                            print(f"   ⚠️  May not contain full Flask documentation")
                            return False
                    
                    return True
            else:
                print(f"❌ No Flask quickstart content found in database")
                return False
        else:
            print(f"❌ Could not query Flask content: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking regression: {e}")
        return False


def check_diverse_urls():
    """Check results for all test URLs"""
    print("\n🌐 DIVERSE URL CHECK")
    print("=" * 50)
    
    test_configs = [
        {
            "name": "Flask Quickstart",
            "pattern": "*flask.palletsprojects.com*quickstart*",
            "expected_min": 1000,
            "keywords": ["Flask", "application", "@app.route"]
        },
        {
            "name": "Python Data Structures",
            "pattern": "*docs.python.org*datastructures*",
            "expected_min": 2000,
            "keywords": ["list", "dictionary", "tuple"]
        },
        {
            "name": "Python Control Flow", 
            "pattern": "*docs.python.org*controlflow*",
            "expected_min": 2000,
            "keywords": ["if", "for", "while", "loop"]
        },
        {
            "name": "PEP 8",
            "pattern": "*python.org*pep-8*",
            "expected_min": 3000,
            "keywords": ["style", "formatting", "naming"]
        },
        {
            "name": "HTTPBin HTML",
            "pattern": "*httpbin.org*html*",
            "expected_min": 200,
            "keywords": ["Herman", "Melville"]
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n📋 Checking: {config['name']}")
        
        try:
            import requests
            
            url = f"http://{DB_HOST}:8000/rest/v1/crawled_pages"
            params = {
                "select": "url,content",
                "url": f"like.{config['pattern']}",
                "limit": 1
            }
            headers = {
                "apikey": os.getenv("SUPABASE_ANON_KEY", ""),
                "Authorization": f"Bearer {os.getenv('SUPABASE_ANON_KEY', '')}"
            }
            
            response = requests.get(url, params=params, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                if data:
                    content = data[0].get('content', '')
                    content_length = len(content)
                    
                    print(f"   📊 Content: {content_length} chars")
                    
                    # Check against expectations
                    if content_length >= config['expected_min']:
                        print(f"   ✅ Length adequate ({content_length} >= {config['expected_min']})")
                        success = True
                    else:
                        print(f"   ❌ Length too short ({content_length} < {config['expected_min']})")
                        success = False
                    
                    # Check for keywords
                    if content:
                        found_keywords = [kw for kw in config['keywords'] if kw.lower() in content.lower()]
                        print(f"   🔍 Keywords found: {found_keywords}")
                        
                        if found_keywords:
                            print(f"   ✅ Contains expected content")
                        else:
                            print(f"   ⚠️  Missing expected keywords")
                            success = False
                    
                    results.append((config['name'], success, content_length))
                else:
                    print(f"   ❌ No content found")
                    results.append((config['name'], False, 0))
            else:
                print(f"   ❌ Query failed: {response.status_code}")
                results.append((config['name'], False, 0))
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append((config['name'], False, 0))
    
    return results


def main():
    """Run complete verification of crawl results"""
    print("🔍 Crawl Results Verification")
    print("=" * 80)
    print("Checking database to verify conservative filtering fix results")
    
    # Basic database check
    if not get_crawl_results():
        print("\n❌ Cannot access database - skipping verification")
        return False
    
    # Regression check
    regression_fixed = check_regression_fix()
    
    # Diverse URL check
    url_results = check_diverse_urls()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 80)
    
    print(f"🔄 Regression Fix: {'✅ SUCCESS' if regression_fixed else '❌ FAILED'}")
    
    if url_results:
        passed = sum(1 for _, success, _ in url_results if success)
        total = len(url_results)
        print(f"🌐 URL Tests: {passed}/{total} passed")
        
        for name, success, length in url_results:
            status = "✅" if success else "❌"
            print(f"   {status} {name}: {length} chars")
    
    # Overall assessment
    if regression_fixed and url_results:
        passed_ratio = sum(1 for _, success, _ in url_results if success) / len(url_results)
        
        if passed_ratio >= 0.8:
            print(f"\n🎉 EXCELLENT: Conservative filtering fix is working!")
            print(f"   • Regression resolved ✅")
            print(f"   • {passed_ratio*100:.0f}% of test URLs working correctly")
            print(f"   • Content extraction significantly improved")
            return True
        elif passed_ratio >= 0.6:
            print(f"\n⚠️  GOOD: Significant improvement achieved")
            print(f"   • Regression resolved ✅") 
            print(f"   • {passed_ratio*100:.0f}% of test URLs working")
            print(f"   • Some sites may need fine-tuning")
            return True
        else:
            print(f"\n❌ ISSUES: Still some problems remain")
            print(f"   • Only {passed_ratio*100:.0f}% of URLs working correctly")
            return False
    else:
        print(f"\n❌ CRITICAL: Major issues remain")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)