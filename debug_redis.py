#!/usr/bin/env python3
"""
Debug Redis connectivity and check queues
"""
import asyncio
import redis.asyncio as redis

async def debug_redis():
    """Debug Redis connectivity"""
    print("🔍 Debugging Redis connections...")
    
    for port in [6379, 6380]:
        print(f"\n📡 Testing Redis on port {port}...")
        try:
            client = redis.from_url(f"redis://localhost:{port}")
            await client.ping()
            print(f"✅ Successfully connected to Redis on port {port}")
            
            # Check queue keys
            queue_key = "ay-rag:queue:crawl"
            queue_length = await client.zcard(queue_key)
            print(f"📊 Queue length: {queue_length}")
            
            # Check for any jobs
            if queue_length > 0:
                jobs = await client.zrange(queue_key, 0, -1)
                print(f"📋 Jobs in queue: {jobs}")
                
            await client.close()
            
        except Exception as e:
            print(f"❌ Failed to connect to Redis on port {port}: {e}")
    
    print("🏁 Redis debugging completed")

if __name__ == "__main__":
    asyncio.run(debug_redis())