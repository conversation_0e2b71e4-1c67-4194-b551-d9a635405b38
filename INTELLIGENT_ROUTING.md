# Intelligent Routing Architecture

**Technical Documentation for Automatic Crawling Method Selection**

## Overview

The AY RAG MCP Server implements **intelligent routing** that automatically selects the optimal crawling method based on URL characteristics, site type, and crawl parameters. This architecture eliminates user confusion about tool selection while providing automatic timeout protection and performance optimization.

### Key Benefits
- **Zero Configuration**: Works automatically without user decision-making
- **Timeout Protection**: Automatic fallback for large sites and complex crawls
- **Performance Optimization**: Fast direct crawling for simple sites
- **Tool Simplification**: Single `smart_crawl_url` tool replaces multiple options

## Tool Architecture

### Core MCP Tools

| Tool | Purpose | Routing Logic |
|------|---------|---------------|
| `crawl_single_page` | Single page crawling | Direct crawling only |
| `smart_crawl_url` | **Intelligent multi-site crawling** | **Auto-detects optimal method** |
| `get_available_sources` | Source metadata retrieval | Database query |
| `perform_rag_query` | Semantic content search | Vector similarity |
| `search_code_examples` | Code-specific search | Specialized RAG (optional) |

### Primary Intelligence: `smart_crawl_url`
This tool serves as the **intelligent router** that automatically chooses between:
- **Docker Crawling**: Timeout-safe for large/complex sites
- **Direct Crawling**: Fast processing for simple sites

## How Intelligent Routing Works

### `smart_crawl_url` Decision Logic

The tool automatically decides between **Docker crawling** (timeout-safe) and **direct crawling** (faster) based on:

#### Docker Crawling Used For:
- ✅ **Sitemaps** (`.xml` files or URLs containing "sitemap")
- ✅ **Deep crawls** (max_depth > 2) 
- ✅ **High concurrency** (max_concurrent > 15)
- ✅ **Documentation sites** (domains containing: docs., api., help., wiki, etc.)
- ✅ **Large platforms** (github.com, stackoverflow.com, medium.com, etc.)

#### Direct Crawling Used For:
- ⚡ **Small sites** (simple domains like example.com)
- ⚡ **Shallow crawls** (max_depth ≤ 2)
- ⚡ **Low concurrency** (max_concurrent ≤ 15)

### Manual Override

```python
# Force direct crawling (faster but may timeout)
smart_crawl_url(url="https://docs.python.org", force_direct=True)

# Let the system decide automatically (recommended)  
smart_crawl_url(url="https://docs.python.org")  # Will use Docker
```

## Examples

### Automatic Routing Examples

```python
# These will use DOCKER crawling (timeout protection):
smart_crawl_url("https://docs.python.org/3/")           # docs. domain
smart_crawl_url("https://example.com/sitemap.xml")      # sitemap
smart_crawl_url("https://github.com/user/repo")         # GitHub domain
smart_crawl_url("https://example.com", max_depth=4)     # deep crawl

# These will use DIRECT crawling (faster):
smart_crawl_url("https://myblog.com/post")              # small site
smart_crawl_url("https://example.com", max_depth=1)     # shallow crawl
```

### User Experience

**Before (Confusing):**
- 10 tools shown to user
- LLM confused between `smart_crawl_url` vs `crawl_with_timeout`
- Users had to understand technical differences

**After (Simple):**
- 5 tools shown to user  
- No duplicate functionality
- System automatically handles optimization
- Users just use `smart_crawl_url` for everything

## Migration Guide

### From Previous Implementation

**Old Way:**
```python
# Users had to choose manually
smart_crawl_url(url)           # Might timeout on large sites
crawl_with_timeout(url)        # Confusing which to use
```

**New Way:**
```python
# System chooses automatically
smart_crawl_url(url)           # Always works, automatically optimized
```

### Configuration Changes

**No configuration needed!** The system automatically:
- Detects large sites and uses Docker crawling
- Uses direct crawling for small sites
- Provides timeout protection when needed
- Falls back gracefully if Docker unavailable

## Architecture Benefits

✅ **No User Confusion** - Single crawl tool with intelligent behavior  
✅ **Automatic Optimization** - System chooses best method automatically  
✅ **Timeout Protection** - Large sites automatically protected  
✅ **Performance** - Small sites get faster direct crawling  
✅ **Zero Configuration** - Works out of the box  
✅ **Backward Compatible** - Existing code continues to work  

## Technical Implementation

### Routing Logic (Testable)

```python
def should_use_docker_crawl(url: str, max_depth: int, max_concurrent: int) -> bool:
    # Sitemaps always use Docker
    if is_sitemap(url): return True
    
    # Deep crawls use Docker  
    if max_depth > 2: return True
    
    # High concurrency uses Docker
    if max_concurrent > 15: return True
    
    # Known large domains use Docker
    domain = urlparse(url).netloc.lower()
    if any(pattern in domain for pattern in ['docs.', 'github.com', ...]): return True
    
    # Default to direct crawling
    return False
```

### Error Handling

- **Docker Unavailable**: Falls back to direct crawling with warning
- **Direct Timeout**: Clear error message suggesting retry (rare with intelligent routing)
- **Invalid URL**: Consistent error handling across both methods

## Testing

The routing logic is fully tested with automated test cases covering:
- Small sites → Direct crawling
- Documentation sites → Docker crawling  
- Sitemaps → Docker crawling
- Deep crawls → Docker crawling
- High concurrency → Docker crawling

Run tests: `python3 test_routing.py`