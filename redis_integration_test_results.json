{"overall_status": "FAIL", "total_tests": 5, "passed": 4, "failed": 1, "success_rate": "80.0%", "tests": [{"test": "redis_connectivity", "status": "PASS", "message": "Redis connection successful", "details": {"value_retrieved": true}}, {"test": "job_queue_basic_operations", "status": "FAIL", "message": "Basic operations failed: 'NoneType' object has no attribute 'status'", "details": {"error": "'NoneType' object has no attribute 'status'"}}, {"test": "job_priority_ordering", "status": "PASS", "message": "Priority ordering works correctly", "details": {"high_priority_first": true, "low_priority_second": true}}, {"test": "concurrent_operations", "status": "PASS", "message": "Concurrent operations successful", "details": {"jobs_processed": 50, "successful_enqueues": 50, "successful_dequeues": 50, "enqueue_time": "0.01s", "dequeue_time": "0.03s", "enqueue_throughput": "4253.77 ops/sec", "dequeue_throughput": "1679.48 ops/sec"}}, {"test": "job_cancellation", "status": "PASS", "message": "Job cancellation works correctly", "details": {"cancellation_success": true, "final_status": "CANCELLED", "queue_cleaned": true}}]}