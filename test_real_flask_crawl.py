#!/usr/bin/env python3
"""
Test Real Flask Documentation Crawling

This script tests actual crawling of Flask documentation to identify
why only copyright footers are being extracted instead of main content.
"""

import asyncio
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from enhanced_content_filter import (
    EnhancedContentFilter,
    FilteringLevel,
    create_enhanced_crawler_config
)

# Test if crawl4ai is available
try:
    from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
    crawl4ai_available = True
except ImportError:
    crawl4ai_available = False
    print("⚠️  Crawl4AI not available - testing with mock content")


async def test_flask_quickstart_real():
    """Test crawling real Flask quickstart documentation"""
    if not crawl4ai_available:
        print("❌ Cannot test real crawling without Crawl4AI")
        return False
    
    print("🌐 Testing Real Flask Quickstart Crawling")
    print("=" * 60)
    
    flask_url = "https://flask.palletsprojects.com/en/3.0.x/quickstart/"
    
    # Test 1: Raw crawling without enhanced filtering
    print("\n1️⃣ Testing RAW crawling (no enhanced filtering)...")
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        try:
            # Basic crawl configuration
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                stream=False,
                word_count_threshold=10  # Low threshold to get more content
            )
            
            result = await crawler.arun(url=flask_url, config=run_config)
            
            if result.success:
                raw_content = result.markdown
                print(f"   ✅ Raw crawl successful")
                print(f"   📏 Content length: {len(raw_content)} chars")
                
                # Check for key indicators
                checks = [
                    ("Flask" in raw_content, "Contains 'Flask'"),
                    ("Quickstart" in raw_content, "Contains 'Quickstart'"),
                    ("@app.route" in raw_content, "Contains Flask code examples"),
                    ("copyright" in raw_content.lower(), "Contains copyright"),
                    ("minimal application" in raw_content.lower(), "Contains main content"),
                ]
                
                for check, description in checks:
                    status = "✅" if check else "❌"
                    print(f"   {status} {description}")
                
                # Preview content
                preview = raw_content[:500].replace('\n', '\\n')
                print(f"   📄 Content preview: {preview}...")
                
                if len(raw_content) < 200:
                    print(f"   🚨 VERY SHORT CONTENT - likely extraction failure!")
                    return False
                    
            else:
                print(f"   ❌ Raw crawl failed: {result.error_message}")
                return False
                
        except Exception as e:
            print(f"   ❌ Raw crawl error: {e}")
            return False
    
    # Test 2: Enhanced filtering configuration
    print("\n2️⃣ Testing with ENHANCED FILTERING configuration...")
    
    enhanced_config = create_enhanced_crawler_config(
        url=flask_url,
        filtering_level=FilteringLevel.STANDARD
    )
    
    print(f"   📋 Enhanced config:")
    print(f"      Excluded tags: {enhanced_config.get('excluded_tags', [])}")
    print(f"      CSS selectors to exclude: {len(enhanced_config.get('css_selector_to_exclude', []))} selectors")
    print(f"      Word count threshold: {enhanced_config.get('word_count_threshold', 0)}")
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        try:
            # Enhanced crawl configuration
            css_selectors = enhanced_config.get('css_selector_to_exclude', [])
            css_selector_string = ','.join(css_selectors)  # Fixed format after our bug fix
            
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                stream=False,
                word_count_threshold=enhanced_config.get('word_count_threshold', 20),
                excluded_tags=enhanced_config.get('excluded_tags', []),
                css_selector=css_selector_string  # This is where our bug was fixed
            )
            
            result = await crawler.arun(url=flask_url, config=run_config)
            
            if result.success:
                enhanced_content = result.markdown
                print(f"   ✅ Enhanced crawl successful")
                print(f"   📏 Content length: {len(enhanced_content)} chars")
                
                # Compare with raw content
                if len(raw_content) > 0:
                    reduction = (len(raw_content) - len(enhanced_content)) / len(raw_content) * 100
                    print(f"   📊 Content reduction: {reduction:.1f}%")
                    
                    if reduction > 90:
                        print(f"   🚨 CRITICAL: Over 90% content reduction!")
                    elif reduction > 70:
                        print(f"   ⚠️  WARNING: High content reduction")
                    else:
                        print(f"   ✅ Reasonable content reduction")
                
                # Check what remains
                checks = [
                    ("Flask" in enhanced_content, "Contains 'Flask'"),
                    ("Quickstart" in enhanced_content, "Contains 'Quickstart'"),
                    ("@app.route" in enhanced_content, "Contains Flask code examples"),
                    ("copyright" in enhanced_content.lower(), "Contains copyright"),
                    ("minimal application" in enhanced_content.lower(), "Contains main content"),
                ]
                
                for check, description in checks:
                    status = "✅" if check else "❌"
                    print(f"   {status} {description}")
                
                # Check if only copyright remains (the reported issue)
                lines = [line.strip() for line in enhanced_content.split('\n') if line.strip()]
                if len(lines) <= 5 and any('copyright' in line.lower() for line in lines):
                    print(f"   🚨 ISSUE DETECTED: Only copyright footer remains!")
                    print(f"   📄 Remaining content: {repr(enhanced_content)}")
                    return False
                elif len(enhanced_content) < 100:
                    print(f"   🚨 VERY SHORT CONTENT after enhanced filtering!")
                    print(f"   📄 Content: {repr(enhanced_content)}")
                    return False
                else:
                    preview = enhanced_content[:500].replace('\n', '\\n')
                    print(f"   📄 Enhanced content preview: {preview}...")
                    
            else:
                print(f"   ❌ Enhanced crawl failed: {result.error_message}")
                return False
                
        except Exception as e:
            print(f"   ❌ Enhanced crawl error: {e}")
            return False
    
    # Test 3: Post-processing with content filter
    print("\n3️⃣ Testing POST-PROCESSING with content filter...")
    
    filter_system = EnhancedContentFilter()
    filter_result = filter_system.filter_content(
        content=enhanced_content,
        url=flask_url,
        filtering_level=FilteringLevel.ADAPTIVE,
        preserve_code=True
    )
    
    print(f"   📏 After post-processing: {filter_result.filtered_length} chars")
    print(f"   📊 Total reduction: {filter_result.reduction_percentage:.1f}%")
    print(f"   📈 Quality score: {filter_result.quality_score:.2f}")
    print(f"   🔧 Filters applied: {filter_result.filters_applied}")
    
    if filter_result.content_classification:
        print(f"   🏷️  Content type: {filter_result.content_classification.content_type}")
        print(f"   📊 Confidence: {filter_result.content_classification.confidence_score:.2f}")
    
    # Final check
    final_content = filter_result.filtered_content
    if len(final_content) < 100:
        print(f"   🚨 CRITICAL: Very little content after full processing!")
        print(f"   📄 Final content: {repr(final_content)}")
        return False
    elif "Flask" not in final_content:
        print(f"   🚨 CRITICAL: Main topic 'Flask' lost during processing!")
        return False
    else:
        print(f"   ✅ Post-processing successful")
        preview = final_content[:300].replace('\n', '\\n')
        print(f"   📄 Final preview: {preview}...")
    
    return True


async def test_alternative_urls():
    """Test with alternative documentation URLs to isolate the issue"""
    if not crawl4ai_available:
        return
    
    print("\n🧪 Testing Alternative Documentation URLs")
    print("=" * 60)
    
    test_urls = [
        ("Flask Installation", "https://flask.palletsprojects.com/en/3.0.x/installation/"),
        ("Python.org Tutorial", "https://docs.python.org/3/tutorial/introduction.html"),
        ("FastAPI Tutorial", "https://fastapi.tiangolo.com/tutorial/"),
    ]
    
    for name, url in test_urls:
        print(f"\n🔗 Testing {name}: {url}")
        
        try:
            async with AsyncWebCrawler(verbose=False) as crawler:
                # Test with enhanced config
                enhanced_config = create_enhanced_crawler_config(
                    url=url,
                    filtering_level=FilteringLevel.STANDARD
                )
                
                css_selectors = enhanced_config.get('css_selector_to_exclude', [])
                css_selector_string = ','.join(css_selectors)
                
                run_config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    stream=False,
                    word_count_threshold=enhanced_config.get('word_count_threshold', 20),
                    excluded_tags=enhanced_config.get('excluded_tags', []),
                    css_selector=css_selector_string
                )
                
                result = await crawler.arun(url=url, config=run_config)
                
                if result.success:
                    content_length = len(result.markdown)
                    print(f"   ✅ Content length: {content_length} chars")
                    
                    if content_length < 100:
                        print(f"   🚨 Very short content - possible extraction failure")
                    elif content_length < 500:
                        print(f"   ⚠️  Short content - check for over-filtering")
                    else:
                        print(f"   ✅ Good content length")
                    
                    # Check for copyright-only issue
                    lines = [line.strip() for line in result.markdown.split('\n') if line.strip()]
                    copyright_lines = [line for line in lines if 'copyright' in line.lower()]
                    
                    if len(copyright_lines) > 0 and len(lines) <= 5:
                        print(f"   🚨 ISSUE: Only copyright footer extracted!")
                    elif len(copyright_lines) > len(lines) * 0.5:
                        print(f"   ⚠️  High proportion of copyright content")
                    else:
                        print(f"   ✅ Normal content mix")
                else:
                    print(f"   ❌ Failed: {result.error_message}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")


def test_css_selector_simulation():
    """Test specific CSS selectors that might be causing issues"""
    print("\n🎯 Testing Specific CSS Selector Issues")
    print("=" * 60)
    
    # Simulate HTML that might be affected by our CSS selectors
    mock_html_scenarios = [
        ("Main content in article tag", """
<article class="main-content">
    <h1>Flask Quickstart</h1>
    <p>A minimal Flask application looks like this:</p>
    <pre><code>from flask import Flask</code></pre>
</article>
<footer>Copyright 2023 Flask Team</footer>
"""),
        ("Content with navigation", """
<nav class="navigation">
    <ul>
        <li>Home</li>
        <li>Docs</li>
    </ul>
</nav>
<main>
    <h1>Flask Quickstart</h1>
    <p>Main documentation content here.</p>
</main>
<footer>Copyright 2023</footer>
"""),
        ("Documentation structure", """
<div class="documentation">
    <div class="sidebar">Table of Contents</div>
    <div class="content">
        <h1>Quickstart</h1>
        <p>Flask tutorial content...</p>
    </div>
</div>
<footer>Copyright notice</footer>
""")
    ]
    
    enhanced_config = create_enhanced_crawler_config(
        url="https://flask.palletsprojects.com/quickstart/",
        filtering_level=FilteringLevel.STANDARD
    )
    
    excluded_tags = enhanced_config.get('excluded_tags', [])
    css_selectors = enhanced_config.get('css_selector_to_exclude', [])
    
    print(f"📋 Analyzing impact of current configuration:")
    print(f"   Excluded tags: {excluded_tags}")
    print(f"   CSS selectors: {css_selectors[:10]}... ({len(css_selectors)} total)")
    
    for scenario_name, html in mock_html_scenarios:
        print(f"\n📝 Scenario: {scenario_name}")
        
        # Simulate what gets removed
        removed_elements = []
        
        # Check excluded tags
        for tag in excluded_tags:
            if f"<{tag}" in html:
                removed_elements.append(f"{tag} tag")
        
        # Check CSS selectors (simplified simulation)
        for selector in css_selectors:
            if selector.startswith('.') and f'class="{selector[1:]}"' in html:
                removed_elements.append(f"{selector} class")
            elif selector in ['nav', 'main', 'article', 'footer'] and f"<{selector}" in html:
                removed_elements.append(f"{selector} element")
        
        if removed_elements:
            print(f"   🚨 Would remove: {', '.join(removed_elements)}")
            
            # Check if main content would be removed
            main_content_indicators = ['<main', 'class="content"', 'class="main-content"', '<article']
            if any(indicator in html for indicator in main_content_indicators):
                main_removed = any(
                    ('main' in removed_elements) or 
                    ('content' in selector for selector in removed_elements) or
                    ('article' in removed_elements)
                )
                if main_removed:
                    print(f"   🚨 CRITICAL: Main content would be removed!")
                else:
                    print(f"   ✅ Main content preserved")
            else:
                print(f"   ⚠️  No clear main content structure")
        else:
            print(f"   ✅ No elements would be removed")


async def main():
    """Run all tests to diagnose the content extraction issue"""
    print("🔍 Real Flask Documentation Crawling Diagnostics")
    print("=" * 80)
    
    if crawl4ai_available:
        success = await test_flask_quickstart_real()
        await test_alternative_urls()
    else:
        success = False
        print("⚠️  Skipping real crawling tests - Crawl4AI not available")
    
    test_css_selector_simulation()
    
    print("\n" + "=" * 80)
    print("📋 DIAGNOSTIC CONCLUSION")
    print("=" * 80)
    
    if not crawl4ai_available:
        print("❌ Cannot complete full diagnosis without Crawl4AI")
        print("🔧 RECOMMENDED ACTIONS:")
        print("   1. Install Crawl4AI dependencies in test environment")
        print("   2. Run this test in production container")
        print("   3. Check CSS selector configuration manually")
    elif success:
        print("✅ Flask documentation crawling appears to work correctly")
        print("🔧 POSSIBLE CAUSES of reported issue:")
        print("   1. Site-specific HTML structure differences")
        print("   2. Temporary website changes")
        print("   3. Network or caching issues")
        print("   4. Different URL than tested")
    else:
        print("🚨 ISSUE CONFIRMED: Content extraction is failing")
        print("🔧 IMMEDIATE FIXES NEEDED:")
        print("   1. Reduce CSS selector exclusions")
        print("   2. Lower word count thresholds")
        print("   3. Add main content preservation logic")
        print("   4. Improve content structure detection")


if __name__ == "__main__":
    asyncio.run(main())