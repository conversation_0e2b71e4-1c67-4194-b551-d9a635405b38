#!/usr/bin/env python3
"""
Debug worker Redis URL issue
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_worker():
    """Debug worker environment and Redis URL"""
    print("🔍 Debugging worker environment...")
    
    # Check environment variable
    redis_url_env = os.getenv("REDIS_URL", "NOT_SET")
    print(f"📊 REDIS_URL environment variable: {redis_url_env}")
    
    # Check worker script behavior
    from job_worker import CrawlJobWorker
    
    # Check default parameters
    worker_default = CrawlJobWorker()
    print(f"📊 Worker default redis_url: {worker_default.redis_url}")
    
    # Check with explicit parameter
    worker_explicit = CrawlJobWorker(redis_url="redis://localhost:6380")
    print(f"📊 Worker explicit redis_url: {worker_explicit.redis_url}")
    
    # Check environment-based worker
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    worker_env = CrawlJobWorker(redis_url=redis_url)
    print(f"📊 Worker env-based redis_url: {worker_env.redis_url}")

if __name__ == "__main__":
    debug_worker()