"""
Pytest configuration for AY RAG MCP Server tests
Provides shared fixtures and test configuration
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Test configuration
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "slow: Slow running tests")


@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing"""
    mock_client = Mock()
    mock_response = Mock()
    mock_response.data = []
    mock_client.table.return_value.select.return_value.execute.return_value = mock_response
    mock_client.rpc.return_value = mock_response
    return mock_client


@pytest.fixture
def mock_crawler():
    """Mock AsyncWebCrawler for testing"""
    mock_crawler = AsyncMock()
    mock_result = Mock()
    mock_result.markdown = "# Test Page\n\nTest content"
    mock_result.metadata.title = "Test Page"
    mock_result.links = {"internal": [], "external": []}
    mock_crawler.arun.return_value = mock_result
    return mock_crawler


@pytest.fixture
def mock_context():
    """Mock MCP context for testing"""
    mock_ctx = Mock()
    mock_ctx.request_context.lifespan_context.supabase_client = None
    mock_ctx.request_context.lifespan_context.crawler = None
    return mock_ctx


@pytest.fixture
def sample_content():
    """Sample content for testing"""
    return """
    # Test Document
    
    This is a test document with various elements.
    
    ## Code Example
    
    ```python
    def hello_world():
        print("Hello, World!")
    ```
    
    Some more text here.
    """


@pytest.fixture
def sample_embeddings():
    """Sample embeddings for testing"""
    return [[0.1, 0.2, 0.3] * 512] * 3  # Mock 1536-dim embeddings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Environment setup for tests
@pytest.fixture(autouse=True)
def setup_test_env():
    """Setup test environment variables"""
    test_env = {
        'SUPABASE_URL': 'https://test.supabase.co',
        'SUPABASE_SERVICE_KEY': 'test-key',
        'OPENAI_API_KEY': 'test-openai-key',
        'MODEL_CHOICE': 'gpt-4o-mini',
        'USE_HYBRID_SEARCH': 'true',
        'USE_AGENTIC_RAG': 'false',
        'USE_CONTEXTUAL_EMBEDDINGS': 'false',
        'USE_RERANKING': 'false'
    }
    
    # Set test environment variables
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original environment
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value


# Skip tests that require external services in CI
def pytest_collection_modifyitems(config, items):
    """Modify test collection to skip external tests in CI"""
    if os.environ.get('CI'):
        skip_external = pytest.mark.skip(reason="External service tests disabled in CI")
        for item in items:
            if "external" in item.keywords:
                item.add_marker(skip_external)