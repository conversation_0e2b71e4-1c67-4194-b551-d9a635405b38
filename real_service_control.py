#!/usr/bin/env python3
"""
Real service control implementation for AY Knowledge Base Dashboard.
This replaces the mock implementations with actual process control.
"""

import asyncio
import subprocess
import psutil
import redis
import logging
from pathlib import Path
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)


class RealServiceController:
    """Actual service control implementation."""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.redis_client = None
        
    async def start_mcp_server(self) -> bool:
        """Actually start the MCP server process."""
        try:
            if 'mcp' in self.processes and self.processes['mcp'].poll() is None:
                logger.info("MCP server already running")
                return True
            
            # Start the actual MCP server
            cmd = ["python", "src/mcp_server.py"]
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=Path.cwd()
            )
            
            self.processes['mcp'] = process
            logger.info(f"MCP server started with PID: {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start MCP server: {e}")
            return False
    
    async def stop_mcp_server(self) -> bool:
        """Actually stop the MCP server process."""
        try:
            if 'mcp' not in self.processes:
                return True
            
            process = self.processes['mcp']
            if process.poll() is None:  # Still running
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
            del self.processes['mcp']
            logger.info("MCP server stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop MCP server: {e}")
            return False
    
    async def get_mcp_status(self) -> str:
        """Get actual MCP server status."""
        if 'mcp' not in self.processes:
            return 'stopped'
        
        process = self.processes['mcp']
        if process.poll() is None:
            return 'running'
        else:
            return 'stopped'
    
    async def start_redis_server(self) -> bool:
        """Start Redis server if not running."""
        try:
            # Check if Redis is already running
            if await self.check_redis_connection():
                logger.info("Redis already running")
                return True
            
            # Try to start Redis
            cmd = ["redis-server", "--daemonize", "yes"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Redis server started")
                return True
            else:
                logger.error(f"Failed to start Redis: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting Redis: {e}")
            return False
    
    async def stop_redis_server(self) -> bool:
        """Stop Redis server."""
        try:
            # Use redis-cli to shutdown
            cmd = ["redis-cli", "shutdown"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            logger.info("Redis server shutdown command sent")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Redis: {e}")
            return False
    
    async def check_redis_connection(self) -> bool:
        """Check if Redis is actually running and accessible."""
        try:
            if not self.redis_client:
                self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            
            self.redis_client.ping()
            return True
        except Exception:
            return False
    
    async def get_redis_status(self) -> str:
        """Get actual Redis status."""
        if await self.check_redis_connection():
            return 'running'
        else:
            return 'stopped'
    
    async def start_workers(self, num_workers: int = 3) -> bool:
        """Start actual crawl worker processes."""
        try:
            for i in range(num_workers):
                worker_id = f"worker_{i}"
                if worker_id in self.processes and self.processes[worker_id].poll() is None:
                    continue  # Worker already running
                
                cmd = ["python", "src/worker.py", "--worker-id", str(i)]
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=Path.cwd()
                )
                
                self.processes[worker_id] = process
                logger.info(f"Worker {i} started with PID: {process.pid}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            return False
    
    async def stop_workers(self) -> bool:
        """Stop all worker processes."""
        try:
            worker_keys = [k for k in self.processes.keys() if k.startswith('worker_')]
            
            for worker_id in worker_keys:
                process = self.processes[worker_id]
                if process.poll() is None:  # Still running
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
                
                del self.processes[worker_id]
                logger.info(f"Worker {worker_id} stopped")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop workers: {e}")
            return False
    
    async def get_workers_status(self) -> str:
        """Get actual workers status."""
        worker_processes = [p for k, p in self.processes.items() if k.startswith('worker_')]
        
        if not worker_processes:
            return 'stopped'
        
        running_workers = [p for p in worker_processes if p.poll() is None]
        
        if len(running_workers) == len(worker_processes):
            return 'running'
        elif len(running_workers) > 0:
            return 'partial'
        else:
            return 'stopped'
    
    async def get_system_health(self) -> Dict[str, float]:
        """Get actual system health metrics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent,
                'memory_total': memory.total,
                'memory_available': memory.available
            }
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                'cpu_usage': 0,
                'memory_usage': 0,
                'disk_usage': 0,
                'memory_total': 0,
                'memory_available': 0
            }
    
    async def get_running_processes(self) -> List[Dict]:
        """Get list of all managed processes."""
        processes = []
        
        for name, process in self.processes.items():
            try:
                if process.poll() is None:  # Still running
                    proc_info = psutil.Process(process.pid)
                    processes.append({
                        'name': name,
                        'pid': process.pid,
                        'status': 'running',
                        'cpu_percent': proc_info.cpu_percent(),
                        'memory_percent': proc_info.memory_percent(),
                        'create_time': proc_info.create_time()
                    })
                else:
                    processes.append({
                        'name': name,
                        'pid': process.pid,
                        'status': 'stopped',
                        'cpu_percent': 0,
                        'memory_percent': 0,
                        'create_time': None
                    })
            except psutil.NoSuchProcess:
                processes.append({
                    'name': name,
                    'pid': process.pid,
                    'status': 'not_found',
                    'cpu_percent': 0,
                    'memory_percent': 0,
                    'create_time': None
                })
        
        return processes
    
    async def cleanup(self):
        """Clean up all processes on shutdown."""
        logger.info("Cleaning up all processes...")
        
        for name, process in self.processes.items():
            try:
                if process.poll() is None:
                    logger.info(f"Terminating {name} (PID: {process.pid})")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"Force killing {name}")
                        process.kill()
                        process.wait()
            except Exception as e:
                logger.error(f"Error cleaning up {name}: {e}")
        
        self.processes.clear()
        logger.info("Cleanup complete")


# Example usage in dashboard_server.py:
"""
# Replace the mock implementations with:

from real_service_control import RealServiceController

class DashboardServer:
    def __init__(self, port: int = 8080):
        # ... existing code ...
        self.service_controller = RealServiceController()
    
    async def start_service(self, request):
        service = request.match_info['service']
        
        if service == 'mcp':
            success = await self.service_controller.start_mcp_server()
        elif service == 'redis':
            success = await self.service_controller.start_redis_server()
        else:
            return web.json_response({'error': f'Unknown service: {service}'}, status=400)
        
        return web.json_response({
            'success': success,
            'message': f'{service} service {"started" if success else "failed to start"}'
        })
    
    # Similar implementations for stop_service, get_service_status, etc.
"""
