"""Test containerized MCP server performance - Windows compatible"""
import asyncio
import aiohttp
import json
import time

async def test_container_health():
    """Test container health endpoint"""
    print("[HEALTH] Testing Container Health...")
    
    async with aiohttp.ClientSession() as session:
        try:
            start_time = time.time()
            async with session.get("http://192.168.1.84:8051/health") as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status == 200:
                    data = await response.json()
                    print(f"[PASS] Health Check: {response_time:.2f}ms")
                    print(f"   Status: {data.get('status', 'unknown')}")
                    print(f"   Components: {list(data.get('components', {}).keys())}")
                    startup_time = data.get('startup_metrics', {}).get('total_startup_time', 'unknown')
                    if isinstance(startup_time, (int, float)):
                        print(f"   Startup Time: {startup_time:.2f}s")
                    else:
                        print(f"   Startup Time: {startup_time}")
                    return True
                else:
                    print(f"[FAIL] Health Check Failed: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"[ERROR] Health Check Error: {e}")
            return False

async def test_mcp_tool_call():
    """Test MCP tool call via SSE"""
    print("\n[MCP] Testing MCP Tool Call...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test MCP tool call format
            request_data = {
                "jsonrpc": "2.0",
                "id": "test-1",
                "method": "tools/call",
                "params": {
                    "name": "get_database_stats",
                    "arguments": {}
                }
            }
            
            start_time = time.time()
            async with session.post(
                "http://192.168.1.84:8051/sse",
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                print(f"   Response Time: {response_time:.2f}ms")
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    try:
                        content = await response.text()
                        print(f"   Content Length: {len(content)} chars")
                        if len(content) > 200:
                            print(f"   Content Preview: {content[:200]}...")
                        else:
                            print(f"   Content: {content}")
                        print("[PASS] MCP Tool Call successful")
                        return True
                    except Exception as e:
                        print(f"   Content Read Error: {e}")
                        return False
                else:
                    print(f"[FAIL] Tool Call Failed: HTTP {response.status}")
                    return False
                    
        except Exception as e:
            print(f"[ERROR] Tool Call Error: {e}")
            return False

async def test_ready_endpoint():
    """Test ready endpoint"""
    print("\n[READY] Testing Ready Endpoint...")
    
    async with aiohttp.ClientSession() as session:
        try:
            start_time = time.time()
            async with session.get("http://192.168.1.84:8051/ready") as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                print(f"   Response Time: {response_time:.2f}ms")
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    content = await response.text()
                    print(f"   Response: {content}")
                    print("[PASS] Ready endpoint working")
                    return True
                else:
                    print(f"[FAIL] Ready endpoint failed: HTTP {response.status}")
                    return False
                    
        except Exception as e:
            print(f"[ERROR] Ready endpoint error: {e}")
            return False

async def test_sse_connection():
    """Test SSE endpoint connection"""
    print("\n[SSE] Testing SSE Connection...")
    
    async with aiohttp.ClientSession() as session:
        try:
            start_time = time.time()
            async with session.get("http://192.168.1.84:8051/sse") as response:
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                print(f"   Connection Time: {response_time:.2f}ms")
                print(f"   Status: {response.status}")
                print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
                
                if response.status == 200:
                    print("[PASS] SSE endpoint accessible")
                    return True
                else:
                    print(f"[FAIL] SSE connection failed: HTTP {response.status}")
                    return False
                    
        except Exception as e:
            print(f"[ERROR] SSE connection error: {e}")
            return False

async def main():
    """Run all performance tests"""
    print("RAG MCP Container Performance Testing")
    print("=" * 50)
    
    # Test 1: Ready endpoint
    ready_ok = await test_ready_endpoint()
    
    # Test 2: Health Check
    health_ok = await test_container_health()
    
    # Test 3: SSE Connection
    sse_ok = await test_sse_connection()
    
    # Test 4: MCP Tool Call
    tool_ok = await test_mcp_tool_call()
    
    print("\n" + "=" * 50)
    print("Performance Test Summary:")
    print(f"   Ready Endpoint: {'PASS' if ready_ok else 'FAIL'}")
    print(f"   Health Check: {'PASS' if health_ok else 'FAIL'}")
    print(f"   SSE Connection: {'PASS' if sse_ok else 'FAIL'}")
    print(f"   MCP Tools: {'PASS' if tool_ok else 'FAIL'}")
    
    overall_score = sum([ready_ok, health_ok, sse_ok, tool_ok])
    print(f"\nOverall Score: {overall_score}/4 ({overall_score/4*100:.0f}%)")
    
    if overall_score == 4:
        print("STATUS: All systems operational!")
    elif overall_score >= 3:
        print("STATUS: Most systems working, minor issues detected")
    elif overall_score >= 2:
        print("STATUS: Some systems working, issues detected")
    else:
        print("STATUS: Significant issues detected")

if __name__ == "__main__":
    asyncio.run(main())
