# Documentation Directory

This directory contains technical specifications and documentation for the AY RAG MCP Server project components.

## Contents

### 📋 Specifications

- **[TUI Specification](tui-spec.md)** - Technical specification for the Terminal User Interface (TUI) component, including architecture, features, and implementation details

## Navigation

For comprehensive project documentation, see the main [project README](../README.md) which includes:

- Project overview and features
- Installation and setup instructions
- Usage examples and integration guides
- API documentation and tool references

## Contributing to Documentation

When adding new documentation:

1. **File naming**: Use kebab-case (e.g., `feature-spec.md`)
2. **Structure**: Include table of contents for documents >100 lines
3. **Metadata**: Add version, audience, and purpose headers
4. **Links**: Update this README with new document references

## Documentation Standards

- **Headers**: Use proper markdown hierarchy (`#`, `##`, `###`)
- **Code blocks**: Specify language for syntax highlighting
- **Cross-references**: Use relative links within the project
- **Professional tone**: Technical but accessible writing style

---

*Last updated: Documentation restructure and quality improvements*