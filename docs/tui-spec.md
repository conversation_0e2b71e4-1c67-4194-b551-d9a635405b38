# AY Knowledge Base TUI Specification

**Version**: v0.1  
**Target Audience**: Developers  
**Purpose**: Technical specification for the AY Knowledge Base Terminal User Interface

## Table of Contents

- [Overview](#overview)
- [Core Architecture](#core-architecture)
- [Interface Design](#interface-design)
- [Navigation & Controls](#navigation--controls)
- [Feature Specifications](#feature-specifications)
- [Session Management](#session-management)
- [Technical Implementation](#technical-implementation)
- [Dependencies](#dependencies)
- [Key Advantages](#key-advantages)

## Overview

The AY Knowledge Base TUI provides an interactive terminal interface for web crawling, content management, and AI-powered query capabilities. Built with modern Python frameworks, it offers a professional command-line experience for knowledge base operations.

## Core Architecture

- **Language**: Python 3.12+
- **UI Framework**: Textual (modern TUI framework)
- **Rendering**: Rich for markdown display and styling
- **Implementation**: Direct import from ay_rag_mcp.py (no HTTP dependencies)
- **Dependencies**: Textual, Rich, OpenRouter SDK, existing MCP tools
- **Configuration**: Read from .env file

## Interface Design

### Welcome Screen

- Large gradient title "AY KNOWLEDGE BASE v0.1" (Rich styling)
- Centered menu with keyboard navigation
- Status bar showing connection status

### Screen Architecture

- **HomeScreen**: Main menu with 6 options
- **CrawlURLScreen**: URL input form with progress display
- **CrawlSearchScreen**: Search query input + LLM selection display
- **ChatScreen**: Chat interface with message history + input box
- **SourcesScreen**: Interactive table with search/filter/delete
- **SettingsScreen**: Form-based configuration
- **HelpScreen**: Scrollable help content

## Navigation & Controls

- **Ctrl+H**: Return to HomeScreen from anywhere
- **Arrow keys**: Menu navigation
- **Enter**: Select menu items
- **Tab**: Navigate form fields
- **Escape**: Cancel/back operations

## Feature Specifications

### 1. Crawl by URL (CrawlURLScreen)

- Text input widget for URL
- Progress bar for crawling status
- Live log display of crawling progress
- Success/error notifications

### 2. Crawl by Search (CrawlSearchScreen)

- Search query input
- Brave search results display (table)
- LLM selection logic with reasoning display
- Batch crawling progress with status updates

### 3. Chat/Query (ChatScreen)

- **Layout**: Split view with chat history (top) + input (bottom)
- **Message Display**: Rich markdown rendering
- **Input**: Text area at bottom with send button/Enter key
- **History**: Scrollable message list with timestamps
- **Commands**: /new for fresh session, /sources to view available sources
- **Auto-scroll**: New messages auto-scroll to bottom

### 4. View Crawled Sources (SourcesScreen)

- **Table**: Domain, Document Count, Last Crawled, Size
- **Search**: Filter table by domain name
- **Selection**: Highlight rows for deletion
- **Delete**: Password prompt → confirmation → deletion
- **Refresh**: Real-time updates from cache

### 5. Settings (SettingsScreen)

- **OpenRouter Model**: Dropdown with available models
- **Admin Password**: Masked input field for deletion protection
- **Save/Cancel**: Form buttons with validation

### 6. Help (HelpScreen)

- **Sections**: Navigation, Crawling Tips, Chat Commands, Troubleshooting
- **Scrollable**: Full help content with Rich formatting
- **Search**: Find help topics quickly

## Session Management

- **In-Memory**: Chat history per session within app lifecycle
- **Local Cache**: Crawled sources list cached during runtime
- **Session Reset**: /new command clears chat history, keeps sources
- **No Persistence**: Fresh start each app launch

## Technical Implementation

- **App Class**: Main Textual App with screen management
- **Screen Classes**: One per major feature (HomeScreen, ChatScreen, etc.)
- **Widgets**: Custom chat widget, progress displays, forms
- **Data Models**: Message history, source cache, settings state
- **Integration**: Direct MCP tool imports with async handling

## Dependencies

```
textual>=0.50.0
rich>=13.0.0
openai  # for OpenRouter
python-dotenv
# existing MCP dependencies
```

## Key Advantages

- Real-time chat experience with proper message threading
- Beautiful gradient title and Rich markdown rendering
- Interactive tables for source management
- Form-based settings with validation
- Live progress displays for crawling operations
- Smooth Ctrl+H navigation between screens