#!/usr/bin/env python3
"""
Simple test TUI to debug button text visibility.
"""

from textual.app import App, ComposeResult
from textual.widgets import Button, Label, Static
from textual.containers import Vertical, Container
from rich.text import Text
from rich.style import Style


class SimpleTestApp(App):
    """Simple test app to check button visibility."""
    
    CSS = """
    Screen {
        background: #000000;
        color: #ffffff;
    }
    
    Button {
        background: #1a1a1a;
        color: #ffffff;
        text-style: bold;
        border: solid #ff0080;
        width: 100%;
        height: 3;
        margin: 1 0;
        text-align: center;
        padding: 1;
    }
    
    Button:hover {
        background: #ff0080;
        color: #000000;
    }
    
    Label {
        color: #ffffff;
        text-style: bold;
        text-align: center;
        margin: 1;
    }
    """
    
    def compose(self) -> ComposeResult:
        """Create simple test interface."""
        yield Label("SIMPLE TEST - CAN YOU SEE BUTTON TEXT?")
        
        with Container():
            yield Button("🌐 Test Button 1", id="test1")
            yield Button("🔍 Test Button 2", id="test2") 
            yield Button("🔄 Test Button 3", id="test3")
            yield Button("💬 Test Button 4", id="test4")
        
        yield Label("If you can see this text but not button text, there's a CSS issue")


if __name__ == "__main__":
    app = SimpleTestApp()
    app.run()
