#!/usr/bin/env python3
"""
Final validation test for race condition fix.
This test verifies that the MCP server no longer throws
"RuntimeError: Received request before initialization was complete"
"""
import asyncio
import time
import subprocess
import sys

def check_container_logs_for_race_condition():
    """Check container logs for race condition error messages."""
    try:
        # Get logs from the last 5 minutes
        result = subprocess.run([
            "docker-compose", "logs", "ay-rag-mcp", "--since", "5m"
        ], capture_output=True, text=True, timeout=30)
        
        logs = result.stdout
        
        # Check for race condition indicators
        race_condition_errors = [
            "RuntimeError: Received request before initialization was complete",
            "unhandled errors in a TaskGroup",
            "Exception Group Traceback",
            "ExceptionGroup: unhandled errors"
        ]
        
        errors_found = []
        for error in race_condition_errors:
            if error in logs:
                errors_found.append(error)
        
        return errors_found, logs
        
    except Exception as e:
        print(f"❌ Error checking logs: {e}")
        return None, None

def check_server_health():
    """Check if server is healthy and properly initialized."""
    try:
        result = subprocess.run([
            "curl", "-s", "http://localhost:8051/health"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            import json
            health_data = json.loads(result.stdout)
            return health_data.get("status") == "healthy" and health_data.get("initialization_complete", False)
        
        return False
    except Exception:
        return False

def count_successful_sessions(logs):
    """Count successful MCP session initializations."""
    if not logs:
        return 0
    
    success_indicators = [
        "AY RAG MCP session ready",
        "MCP session handler ready - SSE connections now allowed"
    ]
    
    count = 0
    for indicator in success_indicators:
        count += logs.count(indicator)
    
    return count

def main():
    """Run the final race condition validation."""
    print("🔍 Final Race Condition Fix Validation")
    print("======================================")
    
    # Check server health
    print("\n1️⃣ Checking server health...")
    if check_server_health():
        print("✅ Server is healthy and fully initialized")
    else:
        print("❌ Server is not healthy or not initialized")
        return False
    
    # Check container logs for race condition errors
    print("\n2️⃣ Analyzing container logs for race condition errors...")
    errors_found, logs = check_container_logs_for_race_condition()
    
    if errors_found is None:
        print("❌ Unable to check container logs")
        return False
    
    if errors_found:
        print(f"❌ Race condition errors detected:")
        for error in errors_found:
            print(f"   • {error}")
        return False
    else:
        print("✅ No race condition errors found in recent logs")
    
    # Count successful session initializations
    print("\n3️⃣ Checking successful MCP session initializations...")
    session_count = count_successful_sessions(logs)
    if session_count > 0:
        print(f"✅ Found {session_count} successful MCP session initializations")
    else:
        print("⚠️ No MCP session initializations found (might be expected if server has been running)")
    
    # Final assessment
    print(f"\n🎯 FINAL ASSESSMENT")
    print(f"===================")
    
    if not errors_found and check_server_health():
        print("✅ RACE CONDITION SUCCESSFULLY FIXED!")
        print("   • No race condition errors in logs")
        print("   • Server is healthy and fully initialized")
        print("   • MCP sessions are initializing properly")
        print("\n🏆 The FastMCP server startup race condition has been resolved!")
        return True
    else:
        print("❌ RACE CONDITION FIX INCOMPLETE")
        print("   Race condition issues still present")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)