#!/usr/bin/env python3
"""
Datetime Parsing Fix Validation Test
Tests the specific datetime parsing bug fixes in the Redis queue system
"""

import asyncio
import time
import json
import statistics
from datetime import datetime, timezone
from typing import List, Dict, Any
import psutil
import os

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from job_queue import RedisJobQueue, JobRequest, JobStatus


class DatetimeParsingTestSuite:
    """Test suite specifically for datetime parsing fixes"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.test_results = []
        self.process = psutil.Process(os.getpid())
    
    async def test_datetime_parsing_stress(self, num_jobs: int = 1000) -> Dict[str, Any]:
        """Test datetime parsing under high load conditions"""
        print(f"🔍 Testing datetime parsing fixes under load ({num_jobs} jobs)...")
        
        queue = RedisJobQueue(self.redis_url, "datetime-parsing-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("datetime-parsing-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        successful_operations = 0
        failed_operations = 0
        datetime_parsing_errors = 0
        total_start_time = time.time()
        
        # Create and process jobs through complete lifecycle
        job_ids = []
        
        # Phase 1: Create jobs
        print("📝 Phase 1: Creating jobs...")
        for i in range(num_jobs):
            try:
                job = JobRequest.create(
                    job_type="datetime_parsing_test",
                    url=f"https://datetime-test-{i}.com",
                    parameters={"test_id": i, "test_data": "datetime parsing validation"}
                )
                job_id = await queue.enqueue_job(job)
                job_ids.append(job_id)
            except Exception as e:
                print(f"Failed to create job {i}: {e}")
                failed_operations += 1
        
        # Phase 2: Process jobs through status changes
        print("🔄 Phase 2: Processing job lifecycle with datetime operations...")
        
        for job_id in job_ids:
            try:
                # Simulate full job lifecycle with datetime operations
                
                # 1. Get initial status (should be QUEUED)
                initial_status = await queue.get_job_status(job_id)
                if initial_status is None:
                    datetime_parsing_errors += 1
                    continue
                
                # 2. Move to PROCESSING
                await queue.update_job_status(job_id, JobStatus.PROCESSING)
                
                # 3. Get processing status (tests datetime parsing for started_at)
                processing_status = await queue.get_job_status(job_id)
                if processing_status is None or processing_status.started_at is None:
                    datetime_parsing_errors += 1
                    continue
                
                # 4. Complete the job with result data
                result_data = {
                    "success": True,
                    "processed_at": datetime.now(timezone.utc).isoformat(),
                    "result": "datetime parsing test completed"
                }
                await queue.update_job_status(
                    job_id, 
                    JobStatus.COMPLETED, 
                    result_data=result_data
                )
                
                # 5. Get final status (tests datetime parsing for completed_at)
                final_status = await queue.get_job_status(job_id)
                if final_status is None:
                    datetime_parsing_errors += 1
                    continue
                
                # Validate datetime fields are properly parsed
                if (final_status.started_at is not None and 
                    final_status.completed_at is not None and
                    final_status.completed_at > final_status.started_at):
                    successful_operations += 1
                else:
                    datetime_parsing_errors += 1
                    print(f"DateTime validation failed for job {job_id}: "
                          f"started_at={final_status.started_at}, "
                          f"completed_at={final_status.completed_at}")
                    
            except Exception as e:
                failed_operations += 1
                print(f"Job lifecycle failed for {job_id}: {e}")
        
        total_time = time.time() - total_start_time
        
        await queue.disconnect()
        
        success_rate = successful_operations / num_jobs
        datetime_error_rate = datetime_parsing_errors / num_jobs
        
        result = {
            "test_name": "datetime_parsing_stress",
            "total_jobs": num_jobs,
            "successful_operations": successful_operations,
            "failed_operations": failed_operations,
            "datetime_parsing_errors": datetime_parsing_errors,
            "success_rate": success_rate,
            "datetime_error_rate": datetime_error_rate,
            "total_time": total_time,
            "operations_per_second": (successful_operations * 5) / total_time,  # 5 operations per job
            "datetime_parsing_fixed": datetime_error_rate < 0.01  # Less than 1% error rate
        }
        
        print(f"   ✅ Success Rate: {success_rate * 100:.1f}%")
        print(f"   📅 DateTime Error Rate: {datetime_error_rate * 100:.2f}%")
        print(f"   ⚡ Operations/sec: {result['operations_per_second']:.2f}")
        print(f"   🔧 DateTime Fix Status: {'✅ WORKING' if result['datetime_parsing_fixed'] else '❌ FAILING'}")
        
        return result
    
    async def test_mixed_datetime_formats(self) -> Dict[str, Any]:
        """Test handling of mixed datetime formats in Redis"""
        print("🔍 Testing mixed datetime format handling...")
        
        queue = RedisJobQueue(self.redis_url, "mixed-datetime-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("mixed-datetime-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        test_cases = [
            ("timestamp_float", time.time()),
            ("iso_string", datetime.now(timezone.utc).isoformat()),
            ("iso_with_z", datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')),
            ("none_value", None),
            ("empty_string", ""),
        ]
        
        successful_parses = 0
        failed_parses = 0
        
        for test_name, datetime_value in test_cases:
            try:
                # Create job
                job = JobRequest.create(
                    job_type="mixed_datetime_test",
                    url=f"https://mixed-datetime-{test_name}.com",
                    parameters={"test_case": test_name}
                )
                job_id = await queue.enqueue_job(job)
                
                # Simulate processing with this datetime format
                await queue.update_job_status(job_id, JobStatus.PROCESSING)
                
                # Manually inject different datetime formats into Redis
                redis_client = queue._get_redis_client()
                result_key = f"mixed-datetime-test:result:{job_id}"
                
                # Store with specific datetime format
                if datetime_value is not None:
                    await redis_client.hset(result_key, "started_at", str(datetime_value))
                
                await redis_client.aclose()
                
                # Try to retrieve and parse
                status = await queue.get_job_status(job_id)
                
                if status is not None:
                    successful_parses += 1
                    print(f"   ✅ {test_name}: parsed successfully")
                else:
                    failed_parses += 1
                    print(f"   ❌ {test_name}: failed to parse")
                    
            except Exception as e:
                failed_parses += 1
                print(f"   ❌ {test_name}: exception - {e}")
        
        await queue.disconnect()
        
        success_rate = successful_parses / len(test_cases)
        
        result = {
            "test_name": "mixed_datetime_formats",
            "total_test_cases": len(test_cases),
            "successful_parses": successful_parses,
            "failed_parses": failed_parses,
            "success_rate": success_rate,
            "mixed_format_handling": success_rate >= 0.8  # 80% should work
        }
        
        print(f"   📊 Success Rate: {success_rate * 100:.1f}%")
        
        return result
    
    async def test_concurrent_datetime_operations(self, num_jobs: int = 200, concurrency: int = 10) -> Dict[str, Any]:
        """Test datetime parsing under concurrent load"""
        print(f"🔍 Testing concurrent datetime operations ({num_jobs} jobs, {concurrency} workers)...")
        
        queue = RedisJobQueue(self.redis_url, "concurrent-datetime-test")
        await queue.connect()
        
        # Clean up
        redis_client = queue._get_redis_client()
        keys_to_delete = await redis_client.keys("concurrent-datetime-test:*")
        if keys_to_delete:
            await redis_client.delete(*keys_to_delete)
        await redis_client.aclose()
        
        async def worker_datetime_operations(worker_id: int, jobs_per_worker: int) -> int:
            """Worker that performs datetime-heavy operations"""
            successful = 0
            
            for i in range(jobs_per_worker):
                try:
                    job = JobRequest.create(
                        job_type="concurrent_datetime",
                        url=f"https://concurrent-{worker_id}-{i}.com",
                        parameters={"worker": worker_id, "job": i}
                    )
                    job_id = await queue.enqueue_job(job)
                    
                    # Rapid status changes to stress datetime parsing
                    await queue.update_job_status(job_id, JobStatus.PROCESSING)
                    status1 = await queue.get_job_status(job_id)
                    
                    await queue.update_job_status(job_id, JobStatus.COMPLETED, 
                                                result_data={"worker": worker_id})
                    status2 = await queue.get_job_status(job_id)
                    
                    # Validate datetime parsing worked
                    if (status1 and status2 and 
                        status1.started_at is not None and 
                        status2.completed_at is not None):
                        successful += 1
                        
                except Exception as e:
                    print(f"Worker {worker_id} job {i} failed: {e}")
            
            return successful
        
        jobs_per_worker = num_jobs // concurrency
        start_time = time.time()
        
        # Run concurrent workers
        tasks = [
            worker_datetime_operations(worker_id, jobs_per_worker)
            for worker_id in range(concurrency)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        successful_operations = sum(
            result for result in results 
            if not isinstance(result, Exception)
        )
        
        await queue.disconnect()
        
        success_rate = successful_operations / num_jobs
        
        result = {
            "test_name": "concurrent_datetime_operations",
            "total_jobs": num_jobs,
            "concurrency": concurrency,
            "successful_operations": successful_operations,
            "success_rate": success_rate,
            "total_time": total_time,
            "operations_per_second": (successful_operations * 3) / total_time,  # 3 ops per job
            "concurrent_datetime_safe": success_rate >= 0.95  # 95% should work under concurrency
        }
        
        print(f"   ✅ Success Rate: {success_rate * 100:.1f}%")
        print(f"   ⚡ Operations/sec: {result['operations_per_second']:.2f}")
        
        return result
    
    async def run_datetime_validation_suite(self) -> Dict[str, Any]:
        """Run complete datetime parsing validation suite"""
        print("🚀 Starting DateTime Parsing Fix Validation Suite")
        print("=" * 60)
        
        # Test Redis connectivity
        try:
            test_queue = RedisJobQueue(self.redis_url, "connectivity-test")
            await test_queue.connect()
            await test_queue.disconnect()
            print("✅ Redis connectivity confirmed")
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            return {"error": "Redis connection failed"}
        
        tests = [
            ("DateTime Parsing Stress Test", self.test_datetime_parsing_stress, 1000),
            ("Mixed DateTime Formats Test", self.test_mixed_datetime_formats),
            ("Concurrent DateTime Operations", self.test_concurrent_datetime_operations, 200, 10),
        ]
        
        results = {}
        all_tests_passed = True
        
        for test_info in tests:
            test_name = test_info[0]
            test_func = test_info[1]
            test_args = test_info[2:] if len(test_info) > 2 else ()
            
            print(f"\n📊 Running {test_name}...")
            try:
                if test_args:
                    result = await test_func(*test_args)
                else:
                    result = await test_func()
                
                results[test_name] = result
                self.test_results.append(result)
                
                # Check if critical datetime tests passed
                if 'datetime_parsing_fixed' in result and not result['datetime_parsing_fixed']:
                    all_tests_passed = False
                if 'mixed_format_handling' in result and not result['mixed_format_handling']:
                    all_tests_passed = False
                if 'concurrent_datetime_safe' in result and not result['concurrent_datetime_safe']:
                    all_tests_passed = False
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results[test_name] = {"error": str(e)}
                all_tests_passed = False
        
        # Overall assessment
        results['overall_datetime_fixes_validated'] = all_tests_passed
        results['test_timestamp'] = datetime.now(timezone.utc).isoformat()
        
        return results
    
    def generate_datetime_fix_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive datetime fix validation report"""
        report = []
        report.append("# DateTime Parsing Fix Validation Report")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Redis URL: {self.redis_url}")
        report.append("")
        
        # Executive summary
        report.append("## Executive Summary")
        if results.get('overall_datetime_fixes_validated', False):
            report.append("✅ **ALL DATETIME PARSING FIXES VALIDATED** - Production Ready")
        else:
            report.append("❌ **DATETIME PARSING ISSUES DETECTED** - Requires Attention")
        
        report.append("")
        
        # Test results summary
        report.append("## Test Results Summary")
        report.append("| Test | Status | Success Rate | Key Metrics |")
        report.append("|------|---------|--------------|-------------|")
        
        for test_result in self.test_results:
            test_name = test_result.get('test_name', 'Unknown')
            success_rate = test_result.get('success_rate', 0) * 100
            
            # Determine status
            status = "✅ PASS"
            if test_result.get('datetime_parsing_fixed') == False:
                status = "❌ FAIL"
            elif test_result.get('mixed_format_handling') == False:
                status = "⚠️ WARN"
            elif test_result.get('concurrent_datetime_safe') == False:
                status = "⚠️ WARN"
            
            # Key metrics
            key_metrics = []
            if 'operations_per_second' in test_result:
                key_metrics.append(f"{test_result['operations_per_second']:.0f} ops/sec")
            if 'datetime_error_rate' in test_result:
                key_metrics.append(f"{test_result['datetime_error_rate']*100:.2f}% errors")
            
            report.append(f"| {test_name} | {status} | {success_rate:.1f}% | {', '.join(key_metrics)} |")
        
        report.append("")
        
        # Detailed analysis
        report.append("## Detailed Analysis")
        
        for test_result in self.test_results:
            test_name = test_result.get('test_name', 'Unknown')
            report.append(f"\n### {test_name.replace('_', ' ').title()}")
            
            # Test-specific details
            if test_name == "datetime_parsing_stress":
                total_jobs = test_result.get('total_jobs', 0)
                successful_ops = test_result.get('successful_operations', 0)
                datetime_errors = test_result.get('datetime_parsing_errors', 0)
                
                report.append(f"- **Total Jobs Processed**: {total_jobs:,}")
                report.append(f"- **Successful Operations**: {successful_ops:,}")
                report.append(f"- **DateTime Parsing Errors**: {datetime_errors}")
                report.append(f"- **Error Rate**: {(datetime_errors/total_jobs)*100:.2f}%")
                
                if test_result.get('datetime_parsing_fixed', False):
                    report.append("- **Result**: ✅ DateTime parsing is working correctly under load")
                else:
                    report.append("- **Result**: ❌ DateTime parsing issues detected - requires investigation")
                    
            elif test_name == "mixed_datetime_formats":
                successful = test_result.get('successful_parses', 0)
                total = test_result.get('total_test_cases', 0)
                
                report.append(f"- **Format Compatibility**: {successful}/{total} formats supported")
                report.append(f"- **Success Rate**: {(successful/total)*100:.1f}%")
                
                if test_result.get('mixed_format_handling', False):
                    report.append("- **Result**: ✅ Mixed datetime formats handled correctly")
                else:
                    report.append("- **Result**: ⚠️ Some datetime formats may cause issues")
                    
            elif test_name == "concurrent_datetime_operations":
                concurrency = test_result.get('concurrency', 0)
                ops_per_sec = test_result.get('operations_per_second', 0)
                
                report.append(f"- **Concurrency Level**: {concurrency} workers")
                report.append(f"- **Throughput**: {ops_per_sec:.2f} operations/second")
                
                if test_result.get('concurrent_datetime_safe', False):
                    report.append("- **Result**: ✅ DateTime parsing safe under concurrent load")
                else:
                    report.append("- **Result**: ⚠️ DateTime parsing may have race conditions")
        
        # Recommendations
        report.append("\n## Recommendations")
        
        if results.get('overall_datetime_fixes_validated', False):
            report.append("### ✅ Production Deployment Approved")
            report.append("- DateTime parsing fixes have been successfully validated")
            report.append("- System demonstrates excellent reliability under various load conditions")
            report.append("- Ready for production deployment with high confidence")
        else:
            report.append("### ❌ Additional Investigation Required")
            report.append("- DateTime parsing issues detected in validation testing")
            report.append("- Review job_queue.py parse_datetime_value function")
            report.append("- Consider additional error handling for edge cases")
            report.append("- Re-run validation tests after fixes")
        
        return "\n".join(report)


async def main():
    """Run the datetime parsing fix validation suite"""
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    test_suite = DatetimeParsingTestSuite(redis_url)
    
    # Run validation tests
    results = await test_suite.run_datetime_validation_suite()
    
    # Generate and save report
    report = test_suite.generate_datetime_fix_report(results)
    
    # Save to file
    report_file = "datetime_parsing_fix_validation_report.md"
    with open(report_file, "w") as f:
        f.write(report)
    
    # Save raw results as JSON
    results_file = "datetime_parsing_validation_results.json"
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print("\n" + "=" * 60)
    print("🎉 DateTime Fix Validation Complete!")
    print(f"📄 Report saved to: {report_file}")
    print(f"📊 Raw results saved to: {results_file}")
    
    # Print final status
    if results.get('overall_datetime_fixes_validated', False):
        print("✅ ALL DATETIME PARSING FIXES VALIDATED - PRODUCTION READY")
    else:
        print("❌ DATETIME PARSING ISSUES DETECTED - REQUIRES ATTENTION")
    
    print("=" * 60)
    print("\n" + report)


if __name__ == "__main__":
    asyncio.run(main())