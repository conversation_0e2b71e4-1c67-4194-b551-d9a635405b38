{"DateTime Parsing Stress Test": {"test_name": "datetime_parsing_stress", "total_jobs": 1000, "successful_operations": 1000, "failed_operations": 0, "datetime_parsing_errors": 0, "success_rate": 1.0, "datetime_error_rate": 0.0, "total_time": 2.4262800216674805, "operations_per_second": 2060.7679061560707, "datetime_parsing_fixed": true}, "Mixed DateTime Formats Test": {"test_name": "mixed_datetime_formats", "total_test_cases": 5, "successful_parses": 5, "failed_parses": 0, "success_rate": 1.0, "mixed_format_handling": true}, "Concurrent DateTime Operations": {"test_name": "concurrent_datetime_operations", "total_jobs": 200, "concurrency": 10, "successful_operations": 200, "success_rate": 1.0, "total_time": 0.22855067253112793, "operations_per_second": 2625.238391798133, "concurrent_datetime_safe": true}, "overall_datetime_fixes_validated": true, "test_timestamp": "2025-07-21T12:20:32.686099+00:00"}