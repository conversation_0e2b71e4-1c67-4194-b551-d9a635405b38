#!/usr/bin/env python3
"""
Test script for OpenRouter integration
Tests configuration validation and client creation
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from model_config import get_model_config, validate_configuration, get_recommended_models
from llm_client import get_llm_client, OpenAIClient, OpenRouterClient

async def test_configuration():
    """Test configuration loading and validation"""
    print("🧪 Testing Configuration Management...")
    
    # Test default configuration
    config = get_model_config()
    print(f"✅ Default provider: {config.provider}")
    print(f"✅ Contextual model: {config.contextual_model.name}")
    print(f"✅ Code analysis model: {config.code_analysis_model.name}")
    print(f"✅ Query enhancement model: {config.query_enhancement_model.name}")
    
    # Test recommended configurations
    recommendations = get_recommended_models()
    print(f"✅ Available recommendations: {list(recommendations.keys())}")
    
    return True

async def test_openai_client():
    """Test OpenAI client creation and validation"""
    print("\n🧪 Testing OpenAI Client...")
    
    # Set environment for OpenAI
    os.environ["LLM_PROVIDER"] = "openai"
    os.environ["OPENAI_API_KEY"] = "test-key"
    
    try:
        client = get_llm_client()
        assert isinstance(client, OpenAIClient)
        print("✅ OpenAI client created successfully")
        return True
    except Exception as e:
        print(f"❌ OpenAI client creation failed: {e}")
        return False

async def test_openrouter_client():
    """Test OpenRouter client creation and validation"""
    print("\n🧪 Testing OpenRouter Client...")
    
    # Set environment for OpenRouter
    os.environ["LLM_PROVIDER"] = "openrouter"
    os.environ["OPENROUTER_API_KEY"] = "test-key"
    
    try:
        client = get_llm_client()
        assert isinstance(client, OpenRouterClient)
        print("✅ OpenRouter client created successfully")
        return True
    except Exception as e:
        print(f"❌ OpenRouter client creation failed: {e}")
        return False

async def test_configuration_validation():
    """Test configuration validation"""
    print("\n🧪 Testing Configuration Validation...")
    
    # Test valid OpenAI configuration
    os.environ["LLM_PROVIDER"] = "openai"
    os.environ["OPENAI_API_KEY"] = "test-key"
    try:
        validate_configuration()
        print("✅ OpenAI validation passed")
    except Exception as e:
        print(f"❌ OpenAI validation failed: {e}")
        return False
    
    # Test valid OpenRouter configuration
    os.environ["LLM_PROVIDER"] = "openrouter"
    os.environ["OPENROUTER_API_KEY"] = "test-key"
    try:
        validate_configuration()
        print("✅ OpenRouter validation passed")
    except Exception as e:
        print(f"❌ OpenRouter validation failed: {e}")
        return False
    
    # Test invalid provider
    os.environ["LLM_PROVIDER"] = "invalid"
    try:
        validate_configuration()
        print("❌ Invalid provider validation should have failed")
        return False
    except ValueError:
        print("✅ Invalid provider properly rejected")
    
    # Test missing API key
    os.environ["LLM_PROVIDER"] = "openai"
    if "OPENAI_API_KEY" in os.environ:
        del os.environ["OPENAI_API_KEY"]
    try:
        validate_configuration()
        print("❌ Missing API key validation should have failed")
        return False
    except ValueError:
        print("✅ Missing API key properly rejected")
    
    return True

async def main():
    """Run all tests"""
    print("🚀 OpenRouter Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_configuration,
        test_openai_client,
        test_openrouter_client,
        test_configuration_validation
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("🎉 All tests passed! OpenRouter integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)