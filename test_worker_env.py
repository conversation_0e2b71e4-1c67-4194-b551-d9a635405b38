#!/usr/bin/env python3
"""
Test worker environment variable parsing
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

print(f"🔍 Environment check:")
print(f"📊 REDIS_URL from os.environ: {os.environ.get('REDIS_URL', 'NOT_SET')}")
print(f"📊 REDIS_URL from os.getenv: {os.getenv('REDIS_URL', 'NOT_SET')}")

# Check the exact code from the worker
redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
print(f"📊 Final redis_url value: {redis_url}")

from job_queue import RedisJobQueue
queue = RedisJobQueue(redis_url)
print(f"📊 RedisJobQueue redis_url: {queue.redis_url}")